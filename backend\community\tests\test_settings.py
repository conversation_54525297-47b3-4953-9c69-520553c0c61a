"""
Test Settings for Community App
Optimized settings for running community tests
"""

import os
from django.conf import settings

# Test database configuration
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',  # Use in-memory database for faster tests
    }
}

# Disable migrations for faster test setup
class DisableMigrations:
    def __contains__(self, item):
        return True
    
    def __getitem__(self, item):
        return None

MIGRATION_MODULES = DisableMigrations()

# Cache configuration for testing
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'test-cache',
    }
}

# Logging configuration for tests
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
        'null': {
            'class': 'logging.NullHandler',
        },
    },
    'loggers': {
        'community.security': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'community.middleware': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'django.request': {
            'handlers': ['null'],
            'level': 'ERROR',
            'propagate': False,
        },
    },
    'root': {
        'handlers': ['null'],
    },
}

# Security settings for testing
SECRET_KEY = 'test-secret-key-for-community-tests-only'
DEBUG = True
ALLOWED_HOSTS = ['testserver', 'localhost', '127.0.0.1']

# Disable CSRF for API tests
CSRF_COOKIE_SECURE = False
CSRF_USE_SESSIONS = False

# Session configuration
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'

# Email backend for testing
EMAIL_BACKEND = 'django.core.mail.backends.locmem.EmailBackend'

# Media and static files for testing
MEDIA_ROOT = '/tmp/community_test_media'
STATIC_ROOT = '/tmp/community_test_static'

# Test-specific middleware (minimal for performance)
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'community.middleware.CommunitySecurityMiddleware',
    'community.middleware.CommunityAuthenticationMiddleware',
]

# Installed apps for testing
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'community',
    'core',  # Required for some dependencies
]

# REST Framework configuration for testing
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.TokenAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
    ],
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.JSONParser',
        'rest_framework.parsers.FormParser',
        'rest_framework.parsers.MultiPartParser',
    ],
    'TEST_REQUEST_DEFAULT_FORMAT': 'json',
}

# Community-specific test settings
COMMUNITY_SETTINGS = {
    'MAX_POST_LENGTH': 5000,
    'MAX_COMMENT_LENGTH': 1000,
    'MAX_TITLE_LENGTH': 200,
    'MAX_TAGS_PER_POST': 10,
    'RATE_LIMIT_ENABLED': True,
    'CONTENT_SANITIZATION_ENABLED': True,
    'SECURITY_LOGGING_ENABLED': True,
}

# Rate limiting settings for testing
COMMUNITY_RATE_LIMITS = {
    '/api/community/posts/': {
        'GET': {'requests': 100, 'window': 3600},
        'POST': {'requests': 10, 'window': 3600},
    },
    '/api/community/comments/': {
        'GET': {'requests': 200, 'window': 3600},
        'POST': {'requests': 20, 'window': 3600},
    },
    '/api/community/posts/*/like/': {
        'POST': {'requests': 50, 'window': 3600},
    },
    '/api/community/posts/*/add_comment/': {
        'POST': {'requests': 20, 'window': 3600},
    },
}

# CORS settings for testing
CORS_ALLOWED_ORIGINS = [
    'http://localhost:3000',
    'http://localhost:5173',
    'https://example.com',
]

CORS_ALLOW_CREDENTIALS = True

# Security headers for testing
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'

# Password validation (simplified for testing)
AUTH_PASSWORD_VALIDATORS = []

# Internationalization
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True

# Default auto field
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Test runner configuration
TEST_RUNNER = 'django.test.runner.DiscoverRunner'

# Disable unnecessary features for testing
USE_THOUSAND_SEPARATOR = False
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# Root URL configuration
ROOT_URLCONF = 'yasmeen_ai.urls'

# Template configuration (minimal for testing)
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]
