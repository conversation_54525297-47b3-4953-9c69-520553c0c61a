/**
 * Request Deduplication Utility
 * Prevents duplicate API calls by caching in-flight requests
 */

interface PendingRequest<T> {
  promise: Promise<T>;
  timestamp: number;
}

class RequestDeduplicator {
  private pendingRequests = new Map<string, PendingRequest<any>>();
  private readonly CACHE_DURATION = 5000; // 5 seconds

  /**
   * Deduplicate API requests by caching in-flight requests
   */
  async deduplicate<T>(
    key: string,
    requestFn: () => Promise<T>,
    options: {
      cacheDuration?: number;
      forceRefresh?: boolean;
    } = {}
  ): Promise<T> {
    const { cacheDuration = this.CACHE_DURATION, forceRefresh = false } = options;
    
    // Clean up expired requests
    this.cleanupExpiredRequests();
    
    // Check if we have a pending request for this key
    const existing = this.pendingRequests.get(key);
    
    if (existing && !forceRefresh) {
      console.log(`🔄 Deduplicating request: ${key}`);
      return existing.promise;
    }
    
    // Create new request
    console.log(`🚀 Making new request: ${key}`);
    const promise = requestFn().finally(() => {
      // Remove from pending requests when completed
      setTimeout(() => {
        this.pendingRequests.delete(key);
      }, cacheDuration);
    });
    
    // Store the pending request
    this.pendingRequests.set(key, {
      promise,
      timestamp: Date.now()
    });
    
    return promise;
  }

  /**
   * Clear all pending requests
   */
  clear(): void {
    this.pendingRequests.clear();
  }

  /**
   * Clear specific request
   */
  clearRequest(key: string): void {
    this.pendingRequests.delete(key);
  }

  /**
   * Get pending request count
   */
  getPendingCount(): number {
    return this.pendingRequests.size;
  }

  /**
   * Clean up expired requests
   */
  private cleanupExpiredRequests(): void {
    const now = Date.now();
    for (const [key, request] of this.pendingRequests.entries()) {
      if (now - request.timestamp > this.CACHE_DURATION * 2) {
        this.pendingRequests.delete(key);
      }
    }
  }
}

// Global instance
export const requestDeduplicator = new RequestDeduplicator();

/**
 * Hook for request deduplication
 */
export const useRequestDeduplication = () => {
  return {
    deduplicate: requestDeduplicator.deduplicate.bind(requestDeduplicator),
    clear: requestDeduplicator.clear.bind(requestDeduplicator),
    clearRequest: requestDeduplicator.clearRequest.bind(requestDeduplicator),
    getPendingCount: requestDeduplicator.getPendingCount.bind(requestDeduplicator),
  };
};

/**
 * Generate cache keys for common API calls
 */
export const generateCacheKey = {
  communityPosts: (params?: any) => `community:posts:${JSON.stringify(params || {})}`,
  communityStats: () => 'community:stats',
  communityTrendingHashtags: () => 'community:trending-hashtags',
  communityUserRecommendations: (filters?: any) => `community:user-recommendations:${JSON.stringify(filters || {})}`,
  userProfile: (userId: string) => `user:profile:${userId}`,
  userRoles: (userId: string) => `user:roles:${userId}`,
};

export default requestDeduplicator;
