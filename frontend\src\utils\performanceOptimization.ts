/**
 * 🎯 PERFORMANCE OPTIMIZATION UTILITY
 * Comprehensive performance monitoring and optimization for production
 */

// ========================================
// PERFORMANCE METRICS
// ========================================

export interface PerformanceMetrics {
  // Core Web Vitals
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  
  // Additional metrics
  fcp: number; // First Contentful Paint
  ttfb: number; // Time to First Byte
  domContentLoaded: number;
  loadComplete: number;
  
  // Custom metrics
  routeChangeTime: number;
  apiResponseTime: number;
  componentRenderTime: number;
  
  // Resource metrics
  bundleSize: number;
  imageOptimization: number;
  cacheHitRate: number;
  
  // User experience
  interactionLatency: number;
  scrollPerformance: number;
}

export interface PerformanceCheck {
  id: string;
  name: string;
  description: string;
  category: 'loading' | 'interactivity' | 'visual_stability' | 'resource' | 'network';
  status: 'excellent' | 'good' | 'needs_improvement' | 'poor';
  value: number;
  threshold: number;
  recommendation: string;
}

// ========================================
// PERFORMANCE OPTIMIZER CLASS
// ========================================

export class PerformanceOptimizer {
  private metrics: PerformanceMetrics | null = null;
  private checks: PerformanceCheck[] = [];
  private observer: PerformanceObserver | null = null;

  constructor() {
    this.initializePerformanceMonitoring();
  }

  // ========================================
  // PERFORMANCE MONITORING
  // ========================================

  private initializePerformanceMonitoring(): void {
    if ('PerformanceObserver' in window) {
      this.observer = new PerformanceObserver((list) => {
        this.processPerformanceEntries(list.getEntries());
      });

      // Observe different types of performance entries
      try {
        this.observer.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint', 'first-input', 'layout-shift'] });
      } catch (error) {
        console.warn('Some performance metrics not supported:', error);
      }
    }

    // Monitor route changes
    this.monitorRouteChanges();
    
    // Monitor API calls
    this.monitorAPIPerformance();
  }

  private processPerformanceEntries(entries: PerformanceEntry[]): void {
    entries.forEach(entry => {
      switch (entry.entryType) {
        case 'navigation':
          this.processNavigationEntry(entry as PerformanceNavigationTiming);
          break;
        case 'paint':
          this.processPaintEntry(entry as PerformancePaintTiming);
          break;
        case 'largest-contentful-paint':
          this.processLCPEntry(entry as any);
          break;
        case 'first-input':
          this.processFIDEntry(entry as any);
          break;
        case 'layout-shift':
          this.processCLSEntry(entry as any);
          break;
      }
    });
  }

  private processNavigationEntry(entry: PerformanceNavigationTiming): void {
    if (!this.metrics) {
      this.metrics = {} as PerformanceMetrics;
    }

    this.metrics.ttfb = entry.responseStart - entry.requestStart;
    this.metrics.domContentLoaded = entry.domContentLoadedEventEnd - entry.navigationStart;
    this.metrics.loadComplete = entry.loadEventEnd - entry.navigationStart;
  }

  private processPaintEntry(entry: PerformancePaintTiming): void {
    if (!this.metrics) {
      this.metrics = {} as PerformanceMetrics;
    }

    if (entry.name === 'first-contentful-paint') {
      this.metrics.fcp = entry.startTime;
    }
  }

  private processLCPEntry(entry: any): void {
    if (!this.metrics) {
      this.metrics = {} as PerformanceMetrics;
    }
    this.metrics.lcp = entry.startTime;
  }

  private processFIDEntry(entry: any): void {
    if (!this.metrics) {
      this.metrics = {} as PerformanceMetrics;
    }
    this.metrics.fid = entry.processingStart - entry.startTime;
  }

  private processCLSEntry(entry: any): void {
    if (!this.metrics) {
      this.metrics = {} as PerformanceMetrics;
    }
    if (!entry.hadRecentInput) {
      this.metrics.cls = (this.metrics.cls || 0) + entry.value;
    }
  }

  // ========================================
  // CUSTOM MONITORING
  // ========================================

  private monitorRouteChanges(): void {
    let routeChangeStart = 0;

    // Monitor route changes (React Router)
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function(...args) {
      routeChangeStart = performance.now();
      return originalPushState.apply(this, args);
    };

    history.replaceState = function(...args) {
      routeChangeStart = performance.now();
      return originalReplaceState.apply(this, args);
    };

    // Monitor when route change completes
    window.addEventListener('popstate', () => {
      routeChangeStart = performance.now();
    });

    // Use MutationObserver to detect when route change is complete
    const observer = new MutationObserver(() => {
      if (routeChangeStart > 0) {
        const routeChangeTime = performance.now() - routeChangeStart;
        if (!this.metrics) this.metrics = {} as PerformanceMetrics;
        this.metrics.routeChangeTime = routeChangeTime;
        routeChangeStart = 0;
      }
    });

    observer.observe(document.body, { childList: true, subtree: true });
  }

  private monitorAPIPerformance(): void {
    const originalFetch = window.fetch;
    const apiTimes: number[] = [];

    window.fetch = async function(...args) {
      const start = performance.now();
      try {
        const response = await originalFetch.apply(this, args);
        const duration = performance.now() - start;
        apiTimes.push(duration);
        
        // Keep only last 10 API calls for average
        if (apiTimes.length > 10) {
          apiTimes.shift();
        }
        
        return response;
      } catch (error) {
        const duration = performance.now() - start;
        apiTimes.push(duration);
        throw error;
      }
    };

    // Update metrics periodically
    setInterval(() => {
      if (apiTimes.length > 0 && this.metrics) {
        this.metrics.apiResponseTime = apiTimes.reduce((a, b) => a + b, 0) / apiTimes.length;
      }
    }, 5000);
  }

  // ========================================
  // PERFORMANCE ANALYSIS
  // ========================================

  async analyzePerformance(): Promise<PerformanceCheck[]> {
    this.checks = [];

    // Wait for metrics to be collected
    await this.waitForMetrics();

    if (!this.metrics) {
      return this.checks;
    }

    // Analyze Core Web Vitals
    this.analyzeLCP();
    this.analyzeFID();
    this.analyzeCLS();

    // Analyze loading performance
    this.analyzeFCP();
    this.analyzeTTFB();
    this.analyzeLoadTime();

    // Analyze custom metrics
    this.analyzeRouteChanges();
    this.analyzeAPIPerformance();

    // Analyze resource optimization
    this.analyzeBundleSize();
    this.analyzeImageOptimization();

    return this.checks;
  }

  private async waitForMetrics(timeout = 5000): Promise<void> {
    const start = Date.now();
    
    while (!this.metrics && (Date.now() - start) < timeout) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  private analyzeLCP(): void {
    if (!this.metrics?.lcp) return;

    const check: PerformanceCheck = {
      id: 'lcp',
      name: 'Largest Contentful Paint',
      description: 'Time until the largest content element is rendered',
      category: 'loading',
      status: this.getPerformanceStatus(this.metrics.lcp, 2500, 4000),
      value: this.metrics.lcp,
      threshold: 2500,
      recommendation: this.metrics.lcp > 2500 
        ? 'Optimize images, reduce server response times, and eliminate render-blocking resources'
        : 'LCP is within acceptable range'
    };

    this.checks.push(check);
  }

  private analyzeFID(): void {
    if (!this.metrics?.fid) return;

    const check: PerformanceCheck = {
      id: 'fid',
      name: 'First Input Delay',
      description: 'Time from first user interaction to browser response',
      category: 'interactivity',
      status: this.getPerformanceStatus(this.metrics.fid, 100, 300),
      value: this.metrics.fid,
      threshold: 100,
      recommendation: this.metrics.fid > 100
        ? 'Reduce JavaScript execution time and break up long tasks'
        : 'FID is within acceptable range'
    };

    this.checks.push(check);
  }

  private analyzeCLS(): void {
    if (!this.metrics?.cls) return;

    const check: PerformanceCheck = {
      id: 'cls',
      name: 'Cumulative Layout Shift',
      description: 'Visual stability of the page during loading',
      category: 'visual_stability',
      status: this.getPerformanceStatus(this.metrics.cls, 0.1, 0.25),
      value: this.metrics.cls,
      threshold: 0.1,
      recommendation: this.metrics.cls > 0.1
        ? 'Add size attributes to images and videos, avoid inserting content above existing content'
        : 'CLS is within acceptable range'
    };

    this.checks.push(check);
  }

  private analyzeFCP(): void {
    if (!this.metrics?.fcp) return;

    const check: PerformanceCheck = {
      id: 'fcp',
      name: 'First Contentful Paint',
      description: 'Time until first content is rendered',
      category: 'loading',
      status: this.getPerformanceStatus(this.metrics.fcp, 1800, 3000),
      value: this.metrics.fcp,
      threshold: 1800,
      recommendation: this.metrics.fcp > 1800
        ? 'Optimize critical rendering path and reduce server response times'
        : 'FCP is within acceptable range'
    };

    this.checks.push(check);
  }

  private analyzeTTFB(): void {
    if (!this.metrics?.ttfb) return;

    const check: PerformanceCheck = {
      id: 'ttfb',
      name: 'Time to First Byte',
      description: 'Server response time',
      category: 'network',
      status: this.getPerformanceStatus(this.metrics.ttfb, 600, 1500),
      value: this.metrics.ttfb,
      threshold: 600,
      recommendation: this.metrics.ttfb > 600
        ? 'Optimize server performance, use CDN, and implement caching'
        : 'TTFB is within acceptable range'
    };

    this.checks.push(check);
  }

  private analyzeLoadTime(): void {
    if (!this.metrics?.loadComplete) return;

    const check: PerformanceCheck = {
      id: 'load_time',
      name: 'Page Load Time',
      description: 'Total time to load the page',
      category: 'loading',
      status: this.getPerformanceStatus(this.metrics.loadComplete, 3000, 5000),
      value: this.metrics.loadComplete,
      threshold: 3000,
      recommendation: this.metrics.loadComplete > 3000
        ? 'Optimize bundle size, implement code splitting, and use lazy loading'
        : 'Load time is within acceptable range'
    };

    this.checks.push(check);
  }

  private analyzeRouteChanges(): void {
    if (!this.metrics?.routeChangeTime) return;

    const check: PerformanceCheck = {
      id: 'route_change',
      name: 'Route Change Performance',
      description: 'Time to complete route transitions',
      category: 'interactivity',
      status: this.getPerformanceStatus(this.metrics.routeChangeTime, 200, 500),
      value: this.metrics.routeChangeTime,
      threshold: 200,
      recommendation: this.metrics.routeChangeTime > 200
        ? 'Implement route-based code splitting and optimize component rendering'
        : 'Route changes are performing well'
    };

    this.checks.push(check);
  }

  private analyzeAPIPerformance(): void {
    if (!this.metrics?.apiResponseTime) return;

    const check: PerformanceCheck = {
      id: 'api_performance',
      name: 'API Response Time',
      description: 'Average API response time',
      category: 'network',
      status: this.getPerformanceStatus(this.metrics.apiResponseTime, 500, 1000),
      value: this.metrics.apiResponseTime,
      threshold: 500,
      recommendation: this.metrics.apiResponseTime > 500
        ? 'Optimize API endpoints, implement caching, and use request batching'
        : 'API performance is within acceptable range'
    };

    this.checks.push(check);
  }

  private analyzeBundleSize(): void {
    // Mock implementation - would analyze actual bundle size
    const bundleSize = 250; // KB

    const check: PerformanceCheck = {
      id: 'bundle_size',
      name: 'Bundle Size',
      description: 'JavaScript bundle size',
      category: 'resource',
      status: this.getPerformanceStatus(bundleSize, 200, 400),
      value: bundleSize,
      threshold: 200,
      recommendation: bundleSize > 200
        ? 'Implement code splitting, tree shaking, and remove unused dependencies'
        : 'Bundle size is optimized'
    };

    this.checks.push(check);
  }

  private analyzeImageOptimization(): void {
    // Mock implementation - would analyze image optimization
    const optimizationScore = 85; // Percentage

    const check: PerformanceCheck = {
      id: 'image_optimization',
      name: 'Image Optimization',
      description: 'Image optimization score',
      category: 'resource',
      status: optimizationScore >= 90 ? 'excellent' : optimizationScore >= 75 ? 'good' : 'needs_improvement',
      value: optimizationScore,
      threshold: 90,
      recommendation: optimizationScore < 90
        ? 'Use modern image formats (WebP, AVIF), implement responsive images, and add lazy loading'
        : 'Images are well optimized'
    };

    this.checks.push(check);
  }

  private getPerformanceStatus(value: number, goodThreshold: number, poorThreshold: number): PerformanceCheck['status'] {
    if (value <= goodThreshold) return 'excellent';
    if (value <= goodThreshold * 1.2) return 'good';
    if (value <= poorThreshold) return 'needs_improvement';
    return 'poor';
  }

  // ========================================
  // OPTIMIZATION RECOMMENDATIONS
  // ========================================

  generateOptimizationReport(): string {
    const excellentChecks = this.checks.filter(c => c.status === 'excellent').length;
    const goodChecks = this.checks.filter(c => c.status === 'good').length;
    const needsImprovementChecks = this.checks.filter(c => c.status === 'needs_improvement').length;
    const poorChecks = this.checks.filter(c => c.status === 'poor').length;

    const totalChecks = this.checks.length;
    const score = Math.round(((excellentChecks * 4 + goodChecks * 3 + needsImprovementChecks * 2 + poorChecks * 1) / (totalChecks * 4)) * 100);

    let report = `
# Performance Optimization Report

## Performance Score: ${score}/100

## Summary
- Excellent: ${excellentChecks}
- Good: ${goodChecks}
- Needs Improvement: ${needsImprovementChecks}
- Poor: ${poorChecks}

## Detailed Analysis
`;

    const categories = ['loading', 'interactivity', 'visual_stability', 'resource', 'network'];
    
    categories.forEach(category => {
      const categoryChecks = this.checks.filter(c => c.category === category);
      if (categoryChecks.length === 0) return;

      report += `
### ${category.toUpperCase().replace('_', ' ')}
`;
      
      categoryChecks.forEach(check => {
        const statusIcon = {
          excellent: '🟢',
          good: '🟡',
          needs_improvement: '🟠',
          poor: '🔴'
        }[check.status];

        report += `- ${statusIcon} **${check.name}**: ${check.value.toFixed(2)}ms (threshold: ${check.threshold}ms)
  - ${check.recommendation}
`;
      });
    });

    return report;
  }

  // ========================================
  // PUBLIC API
  // ========================================

  getMetrics(): PerformanceMetrics | null {
    return this.metrics;
  }

  getChecks(): PerformanceCheck[] {
    return this.checks;
  }

  async runPerformanceAudit(): Promise<{
    metrics: PerformanceMetrics | null;
    checks: PerformanceCheck[];
    report: string;
    score: number;
  }> {
    const checks = await this.analyzePerformance();
    const report = this.generateOptimizationReport();
    
    const excellentChecks = checks.filter(c => c.status === 'excellent').length;
    const goodChecks = checks.filter(c => c.status === 'good').length;
    const needsImprovementChecks = checks.filter(c => c.status === 'needs_improvement').length;
    const poorChecks = checks.filter(c => c.status === 'poor').length;
    
    const totalChecks = checks.length;
    const score = totalChecks > 0 
      ? Math.round(((excellentChecks * 4 + goodChecks * 3 + needsImprovementChecks * 2 + poorChecks * 1) / (totalChecks * 4)) * 100)
      : 0;

    return {
      metrics: this.metrics,
      checks,
      report,
      score
    };
  }
}

// ========================================
// SINGLETON INSTANCE
// ========================================

export const performanceOptimizer = new PerformanceOptimizer();

export default performanceOptimizer;
