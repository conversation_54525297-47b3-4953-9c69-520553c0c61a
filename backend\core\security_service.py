"""
Centralized Security Service for Managing Security Operations
"""
import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.contrib.auth.models import User
from django.db.models import Count, Q, Avg
from django.core.mail import send_mail
from django.conf import settings
from .security_models import AuditLog, SecurityThreat, ComplianceCheck
from .threat_detection import ThreatDetector
import json

logger = logging.getLogger(__name__)


class SecurityService:
    """
    Centralized service for security operations and monitoring
    """
    
    def __init__(self):
        self.threat_detector = ThreatDetector()
    
    def get_security_dashboard_data(self):
        """
        Get comprehensive security dashboard data
        """
        try:
            now = timezone.now()
            last_24h = now - timedelta(hours=24)
            last_7d = now - timedelta(days=7)
            last_30d = now - timedelta(days=30)
            
            # Security metrics
            metrics = {
                'total_threats_blocked': SecurityThreat.objects.filter(
                    status__in=['mitigated', 'resolved']
                ).count(),
                'active_threats': SecurityThreat.objects.filter(
                    status__in=['detected', 'investigating']
                ).count(),
                'failed_login_attempts_24h': AuditLog.objects.filter(
                    action_type='login',
                    severity__in=['warning', 'error'],
                    timestamp__gte=last_24h
                ).count(),
                'suspicious_activities_7d': AuditLog.objects.filter(
                    is_suspicious=True,
                    timestamp__gte=last_7d
                ).count(),
                'security_score': self._calculate_security_score(),
                'last_security_scan': self._get_last_security_scan(),
                'vulnerabilities_found': SecurityThreat.objects.filter(
                    threat_type__in=['sql_injection', 'xss', 'path_traversal'],
                    status__in=['detected', 'investigating']
                ).count(),
                'compliance_status': self._get_compliance_status(),
            }
            
            # Recent security events
            recent_events = SecurityThreat.objects.filter(
                detected_at__gte=last_7d
            ).order_by('-detected_at')[:20]
            
            # Security trends
            trends = self._get_security_trends(last_30d)
            
            # Top threats by type
            threat_stats = SecurityThreat.objects.filter(
                detected_at__gte=last_30d
            ).values('threat_type').annotate(
                count=Count('id')
            ).order_by('-count')[:10]
            
            # Top source IPs
            top_ips = SecurityThreat.objects.filter(
                detected_at__gte=last_7d
            ).values('source_ip').annotate(
                count=Count('id')
            ).order_by('-count')[:10]
            
            return {
                'metrics': metrics,
                'recent_events': [self._serialize_threat(threat) for threat in recent_events],
                'trends': trends,
                'threat_stats': list(threat_stats),
                'top_source_ips': list(top_ips),
                'recommendations': self._get_security_recommendations(),
            }
            
        except Exception as e:
            logger.error(f"Error getting security dashboard data: {str(e)}")
            return self._get_fallback_dashboard_data()
    
    def get_audit_logs(self, filters=None, page=1, page_size=50):
        """
        Get filtered audit logs with pagination
        """
        try:
            queryset = AuditLog.objects.all()
            
            if filters:
                if filters.get('user_id'):
                    queryset = queryset.filter(user_id=filters['user_id'])
                if filters.get('action_type'):
                    queryset = queryset.filter(action_type=filters['action_type'])
                if filters.get('severity'):
                    queryset = queryset.filter(severity=filters['severity'])
                if filters.get('start_date'):
                    queryset = queryset.filter(timestamp__gte=filters['start_date'])
                if filters.get('end_date'):
                    queryset = queryset.filter(timestamp__lte=filters['end_date'])
                if filters.get('is_suspicious'):
                    queryset = queryset.filter(is_suspicious=True)
            
            total_count = queryset.count()
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            
            logs = queryset[start_index:end_index]
            
            return {
                'results': [self._serialize_audit_log(log) for log in logs],
                'count': total_count,
                'page': page,
                'page_size': page_size,
                'total_pages': (total_count + page_size - 1) // page_size,
            }
            
        except Exception as e:
            logger.error(f"Error getting audit logs: {str(e)}")
            return {'results': [], 'count': 0, 'page': 1, 'page_size': page_size, 'total_pages': 0}
    
    def get_security_threats(self, filters=None):
        """
        Get security threats with optional filtering
        """
        try:
            queryset = SecurityThreat.objects.all()
            
            if filters:
                if filters.get('severity'):
                    queryset = queryset.filter(severity=filters['severity'])
                if filters.get('status'):
                    queryset = queryset.filter(status=filters['status'])
                if filters.get('threat_type'):
                    queryset = queryset.filter(threat_type=filters['threat_type'])
                if filters.get('start_date'):
                    queryset = queryset.filter(detected_at__gte=filters['start_date'])
                if filters.get('end_date'):
                    queryset = queryset.filter(detected_at__lte=filters['end_date'])
            
            threats = queryset.order_by('-detected_at')[:100]
            
            return [self._serialize_threat(threat) for threat in threats]
            
        except Exception as e:
            logger.error(f"Error getting security threats: {str(e)}")
            return []
    
    def resolve_threat(self, threat_id, resolution_notes, resolved_by):
        """
        Resolve a security threat
        """
        try:
            threat = SecurityThreat.objects.get(id=threat_id)
            threat.status = 'resolved'
            threat.resolved_at = timezone.now()
            threat.resolution_notes = resolution_notes
            threat.assigned_to = resolved_by
            threat.save()
            
            # Log the resolution
            AuditLog.log_action(
                user=resolved_by,
                action_type='security_event',
                event_description=f'Security threat {threat_id} resolved',
                resource_type='security_threat',
                resource_id=str(threat_id),
                severity='info',
                event_data={'resolution_notes': resolution_notes}
            )
            
            return True
            
        except SecurityThreat.DoesNotExist:
            logger.error(f"Security threat {threat_id} not found")
            return False
        except Exception as e:
            logger.error(f"Error resolving threat {threat_id}: {str(e)}")
            return False
    
    def run_compliance_check(self, compliance_type, check_name, checked_by):
        """
        Run a compliance check
        """
        try:
            # This would implement actual compliance checking logic
            # For now, we'll create a sample compliance check
            
            check_results = self._perform_compliance_check(compliance_type, check_name)
            
            compliance_check = ComplianceCheck.objects.create(
                compliance_type=compliance_type,
                check_name=check_name,
                description=f"Automated {compliance_type.upper()} compliance check",
                status=check_results['status'],
                check_results=check_results['details'],
                compliance_score=check_results['score'],
                issues_found=check_results['issues'],
                recommendations=check_results['recommendations'],
                remediation_required=check_results['score'] < 80,
                checked_by=checked_by,
                next_check_date=timezone.now() + timedelta(days=30)
            )
            
            # Log the compliance check
            AuditLog.log_action(
                user=checked_by,
                action_type='security_event',
                event_description=f'Compliance check performed: {check_name}',
                resource_type='compliance_check',
                resource_id=str(compliance_check.id),
                severity='info',
                event_data={'compliance_type': compliance_type, 'score': check_results['score']}
            )
            
            return compliance_check
            
        except Exception as e:
            logger.error(f"Error running compliance check: {str(e)}")
            return None
    
    def _calculate_security_score(self):
        """
        Calculate overall security score (0-100)
        """
        try:
            now = timezone.now()
            last_30d = now - timedelta(days=30)
            
            # Base score
            score = 100
            
            # Deduct points for unresolved threats
            unresolved_threats = SecurityThreat.objects.filter(
                status__in=['detected', 'investigating'],
                detected_at__gte=last_30d
            ).count()
            score -= min(unresolved_threats * 5, 30)
            
            # Deduct points for failed logins
            failed_logins = AuditLog.objects.filter(
                action_type='login',
                severity__in=['warning', 'error'],
                timestamp__gte=last_30d
            ).count()
            score -= min(failed_logins * 0.5, 20)
            
            # Deduct points for suspicious activities
            suspicious_activities = AuditLog.objects.filter(
                is_suspicious=True,
                timestamp__gte=last_30d
            ).count()
            score -= min(suspicious_activities * 2, 25)
            
            return max(score, 0)
            
        except Exception as e:
            logger.error(f"Error calculating security score: {str(e)}")
            return 85  # Default score
    
    def _get_last_security_scan(self):
        """
        Get timestamp of last security scan
        """
        try:
            last_scan = ComplianceCheck.objects.filter(
                check_name__icontains='security scan'
            ).order_by('-check_date').first()
            
            if last_scan:
                return last_scan.check_date.isoformat()
            else:
                return timezone.now().isoformat()
                
        except Exception as e:
            logger.error(f"Error getting last security scan: {str(e)}")
            return timezone.now().isoformat()
    
    def _get_compliance_status(self):
        """
        Get overall compliance status
        """
        try:
            recent_checks = ComplianceCheck.objects.filter(
                check_date__gte=timezone.now() - timedelta(days=90)
            )
            
            if not recent_checks.exists():
                return 'unknown'
            
            avg_score = recent_checks.aggregate(avg_score=Avg('compliance_score'))['avg_score']
            
            if avg_score >= 90:
                return 'compliant'
            elif avg_score >= 70:
                return 'partial'
            else:
                return 'non_compliant'
                
        except Exception as e:
            logger.error(f"Error getting compliance status: {str(e)}")
            return 'unknown'
    
    def _get_security_trends(self, since_date):
        """
        Get security trends over time
        """
        try:
            # Daily threat counts
            daily_threats = SecurityThreat.objects.filter(
                detected_at__gte=since_date
            ).extra(
                select={'day': 'date(detected_at)'}
            ).values('day').annotate(
                count=Count('id')
            ).order_by('day')
            
            return {
                'daily_threats': list(daily_threats),
                'trend_direction': self._calculate_trend_direction(daily_threats),
            }
            
        except Exception as e:
            logger.error(f"Error getting security trends: {str(e)}")
            return {'daily_threats': [], 'trend_direction': 'stable'}
    
    def _calculate_trend_direction(self, daily_data):
        """
        Calculate if threats are trending up, down, or stable
        """
        if len(daily_data) < 2:
            return 'stable'
        
        recent_avg = sum(item['count'] for item in daily_data[-7:]) / min(7, len(daily_data))
        older_avg = sum(item['count'] for item in daily_data[:-7]) / max(1, len(daily_data) - 7)
        
        if recent_avg > older_avg * 1.2:
            return 'increasing'
        elif recent_avg < older_avg * 0.8:
            return 'decreasing'
        else:
            return 'stable'
    
    def _get_security_recommendations(self):
        """
        Get security recommendations based on current state
        """
        recommendations = []
        
        try:
            now = timezone.now()
            last_24h = now - timedelta(hours=24)
            
            # Check for high failed login attempts
            failed_logins = AuditLog.objects.filter(
                action_type='login',
                severity__in=['warning', 'error'],
                timestamp__gte=last_24h
            ).count()
            
            if failed_logins > 50:
                recommendations.append({
                    'type': 'warning',
                    'title': 'High Failed Login Attempts',
                    'description': f'{failed_logins} failed login attempts in the last 24 hours',
                    'action': 'Consider implementing additional rate limiting or account lockout policies'
                })
            
            # Check for unresolved critical threats
            critical_threats = SecurityThreat.objects.filter(
                severity='critical',
                status__in=['detected', 'investigating']
            ).count()
            
            if critical_threats > 0:
                recommendations.append({
                    'type': 'critical',
                    'title': 'Unresolved Critical Threats',
                    'description': f'{critical_threats} critical security threats require immediate attention',
                    'action': 'Review and resolve critical threats immediately'
                })
            
            # Check compliance status
            compliance_status = self._get_compliance_status()
            if compliance_status == 'non_compliant':
                recommendations.append({
                    'type': 'warning',
                    'title': 'Compliance Issues',
                    'description': 'System is not meeting compliance requirements',
                    'action': 'Run compliance checks and address identified issues'
                })
            
        except Exception as e:
            logger.error(f"Error getting security recommendations: {str(e)}")
        
        return recommendations
    
    def _perform_compliance_check(self, compliance_type, check_name):
        """
        Perform actual compliance checking (placeholder implementation)
        """
        # This would implement real compliance checking logic
        # For now, return sample results
        return {
            'status': 'compliant',
            'score': 85,
            'details': {'checks_passed': 17, 'checks_failed': 3, 'checks_total': 20},
            'issues': ['Weak password policy detected', 'Missing security headers', 'Outdated dependencies'],
            'recommendations': ['Implement stronger password requirements', 'Add security headers', 'Update dependencies']
        }
    
    def _serialize_audit_log(self, log):
        """Serialize audit log for API response"""
        return {
            'id': str(log.id),
            'timestamp': log.timestamp.isoformat(),
            'user': log.user.username if log.user else 'System',
            'action_type': log.action_type,
            'severity': log.severity,
            'event_description': log.event_description,
            'resource_type': log.resource_type,
            'resource_id': log.resource_id,
            'ip_address': log.ip_address,
            'is_suspicious': log.is_suspicious,
            'risk_score': log.risk_score,
        }
    
    def _serialize_threat(self, threat):
        """Serialize security threat for API response"""
        return {
            'id': str(threat.id),
            'detected_at': threat.detected_at.isoformat(),
            'threat_type': threat.threat_type,
            'severity': threat.severity,
            'status': threat.status,
            'source_ip': threat.source_ip,
            'description': threat.description,
            'risk_score': threat.risk_score,
            'confidence_level': threat.confidence_level,
            'auto_mitigated': threat.auto_mitigated,
        }
    
    def _get_fallback_dashboard_data(self):
        """Fallback data when main query fails"""
        return {
            'metrics': {
                'total_threats_blocked': 0,
                'active_threats': 0,
                'failed_login_attempts_24h': 0,
                'suspicious_activities_7d': 0,
                'security_score': 85,
                'last_security_scan': timezone.now().isoformat(),
                'vulnerabilities_found': 0,
                'compliance_status': 'unknown',
            },
            'recent_events': [],
            'trends': {'daily_threats': [], 'trend_direction': 'stable'},
            'threat_stats': [],
            'top_source_ips': [],
            'recommendations': [],
        }
