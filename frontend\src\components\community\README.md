# Community Authentication Integration

This directory contains components and utilities for integrating community features with Redux authentication.

## Components

### CommunityAuthProvider
Provides authentication context to all community components.

```tsx
import { CommunityAuthProvider } from './CommunityAuthProvider';

function CommunityPage() {
  return (
    <CommunityAuthProvider>
      {/* All community components go here */}
      <CommunityFeed />
      <CommunitySidebar />
    </CommunityAuthProvider>
  );
}
```

### CommunityAuthGuard
Protects components that require authentication.

```tsx
import { CommunityAuthGuard } from './CommunityAuthGuard';

function CreatePostButton() {
  return (
    <CommunityAuthGuard action="create posts">
      <button>Create Post</button>
    </CommunityAuthGuard>
  );
}
```

### InlineCommunityAuthGuard
For smaller UI elements that need auth protection.

```tsx
import { InlineCommunityAuthGuard } from './CommunityAuthGuard';

function LikeButton() {
  return (
    <InlineCommunityAuthGuard
      fallback={<span>Login to like</span>}
    >
      <button>❤️ Like</button>
    </InlineCommunityAuthGuard>
  );
}
```

### CommunityPostActions
Example component showing how to integrate auth with post actions.

```tsx
import { CommunityPostActions } from './CommunityPostActions';

function PostCard({ post }) {
  return (
    <div>
      {/* Post content */}
      <CommunityPostActions
        post={post}
        onLike={handleLike}
        onComment={handleComment}
        onSave={handleSave}
        onShare={handleShare}
      />
    </div>
  );
}
```

## Hooks

### useCommunityAuth
Access community-specific authentication state.

```tsx
import { useCommunityAuth } from './CommunityAuthProvider';

function MyComponent() {
  const { 
    isAuthenticated, 
    user, 
    canCreatePost, 
    canComment, 
    showAuthPrompt 
  } = useCommunityAuth();

  const handleAction = () => {
    if (!canCreatePost) {
      showAuthPrompt('post');
      return;
    }
    // Proceed with action
  };
}
```

## API Integration

The community API service now includes authentication checks:

```tsx
import { communityApi } from '../../services/communityApi';

// These will throw errors if not authenticated
await communityApi.createPost(data);
await communityApi.likePost(postId);
await communityApi.savePost(postId);

// These will warn but still work for anonymous users
await communityApi.getPosts();
await communityApi.getStats();
```

## Best Practices

1. **Wrap your community pages** with `CommunityAuthProvider`
2. **Use guards** for components that require authentication
3. **Show helpful prompts** instead of hiding functionality
4. **Handle errors gracefully** when API calls fail due to auth
5. **Provide fallbacks** for anonymous users where appropriate

## Authentication Flow

1. User visits community page
2. `CommunityAuthProvider` checks Redux auth state
3. Components use `useCommunityAuth` to check permissions
4. Guards show login prompts for protected actions
5. API calls include JWT tokens automatically
6. Errors are handled gracefully with user-friendly messages
