/**
 * Security Headers Configuration
 * 
 * Implements Content Security Policy and other security headers
 * to protect against XSS, clickjacking, and other attacks.
 */

export interface SecurityConfig {
  csp: {
    defaultSrc: string[];
    scriptSrc: string[];
    styleSrc: string[];
    imgSrc: string[];
    connectSrc: string[];
    fontSrc: string[];
    objectSrc: string[];
    mediaSrc: string[];
    frameSrc: string[];
  };
  frameOptions: 'DENY' | 'SAMEORIGIN' | 'ALLOW-FROM';
  contentTypeOptions: boolean;
  referrerPolicy: string;
  permissionsPolicy: Record<string, string[]>;
}

// Default security configuration
export const DEFAULT_SECURITY_CONFIG: SecurityConfig = {
  csp: {
    defaultSrc: ["'self'"],
    scriptSrc: [
      "'self'",
      "'unsafe-inline'", // Required for Vite in development
      "'unsafe-eval'", // Required for development tools
      "https://cdn.jsdelivr.net",
      "https://unpkg.com"
    ],
    styleSrc: [
      "'self'",
      "'unsafe-inline'", // Required for styled-components and CSS-in-JS
      "https://fonts.googleapis.com",
      "https://cdn.jsdelivr.net"
    ],
    imgSrc: [
      "'self'",
      "data:",
      "blob:",
      "https:",
      "http://localhost:*" // For development
    ],
    connectSrc: [
      "'self'",
      "https://api.gemini.com",
      "https://api.openai.com",
      "ws://localhost:*", // For development WebSocket
      "wss://localhost:*",
      "http://localhost:*", // For development API
      "https://localhost:*"
    ],
    fontSrc: [
      "'self'",
      "https://fonts.gstatic.com",
      "https://cdn.jsdelivr.net",
      "data:"
    ],
    objectSrc: ["'none'"],
    mediaSrc: ["'self'", "blob:", "data:"],
    frameSrc: ["'none'"]
  },
  frameOptions: 'DENY',
  contentTypeOptions: true,
  referrerPolicy: 'strict-origin-when-cross-origin',
  permissionsPolicy: {
    camera: [],
    microphone: [],
    geolocation: [],
    payment: [],
    usb: [],
    magnetometer: [],
    gyroscope: [],
    accelerometer: []
  }
};

/**
 * Generate CSP header string from configuration
 */
export const generateCSPHeader = (config: SecurityConfig['csp']): string => {
  const directives = Object.entries(config).map(([directive, sources]) => {
    const directiveName = directive.replace(/([A-Z])/g, '-$1').toLowerCase();
    return `${directiveName} ${sources.join(' ')}`;
  });

  return directives.join('; ');
};

/**
 * Generate Permissions Policy header string
 */
export const generatePermissionsPolicyHeader = (policy: Record<string, string[]>): string => {
  const directives = Object.entries(policy).map(([feature, allowlist]) => {
    if (allowlist.length === 0) {
      return `${feature}=()`;
    }
    return `${feature}=(${allowlist.map(origin => `"${origin}"`).join(' ')})`;
  });

  return directives.join(', ');
};

/**
 * Security headers manager
 */
export class SecurityHeadersManager {
  private config: SecurityConfig;

  constructor(config: SecurityConfig = DEFAULT_SECURITY_CONFIG) {
    this.config = config;
  }

  /**
   * Apply security headers to the document
   */
  applyHeaders(): void {
    // Apply CSP via meta tag (fallback if server headers aren't available)
    this.applyCspMetaTag();
    
    // Apply other security measures
    this.applyFrameProtection();
    this.applyContentTypeProtection();
    this.applyReferrerPolicy();
  }

  /**
   * Apply CSP via meta tag
   */
  private applyCspMetaTag(): void {
    const existingCsp = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    if (existingCsp) {
      existingCsp.remove();
    }

    const cspMeta = document.createElement('meta');
    cspMeta.httpEquiv = 'Content-Security-Policy';
    cspMeta.content = generateCSPHeader(this.config.csp);
    document.head.appendChild(cspMeta);
  }

  /**
   * Apply frame protection
   */
  private applyFrameProtection(): void {
    // This would typically be done via server headers
    // Client-side protection is limited but we can add some measures
    if (window.top !== window.self) {
      console.warn('Page loaded in frame - potential clickjacking attempt');
      // In production, you might want to break out of frames
      // window.top.location = window.self.location;
    }
  }

  /**
   * Apply content type protection
   */
  private applyContentTypeProtection(): void {
    // This is primarily a server-side header
    // Client-side we can validate content types for dynamic content
    console.debug('Content type protection enabled');
  }

  /**
   * Apply referrer policy
   */
  private applyReferrerPolicy(): void {
    const existingReferrer = document.querySelector('meta[name="referrer"]');
    if (existingReferrer) {
      existingReferrer.remove();
    }

    const referrerMeta = document.createElement('meta');
    referrerMeta.name = 'referrer';
    referrerMeta.content = this.config.referrerPolicy;
    document.head.appendChild(referrerMeta);
  }

  /**
   * Update security configuration
   */
  updateConfig(newConfig: Partial<SecurityConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.applyHeaders();
  }

  /**
   * Get current configuration
   */
  getConfig(): SecurityConfig {
    return { ...this.config };
  }
}

// Global security headers manager
export const securityHeaders = new SecurityHeadersManager();

/**
 * Initialize security headers
 */
export const initializeSecurity = (): void => {
  // Apply security headers
  securityHeaders.applyHeaders();

  // Add additional security measures
  addSecurityEventListeners();
  
  console.info('Security headers initialized');
};

/**
 * Add security-related event listeners
 */
const addSecurityEventListeners = (): void => {
  // Detect and log potential XSS attempts
  window.addEventListener('error', (event) => {
    if (event.message.includes('script') || event.message.includes('eval')) {
      console.warn('Potential XSS attempt detected:', event.message);
    }
  });

  // Monitor for suspicious activity
  let rapidClicks = 0;
  let lastClickTime = 0;

  document.addEventListener('click', () => {
    const now = Date.now();
    if (now - lastClickTime < 100) {
      rapidClicks++;
      if (rapidClicks > 10) {
        console.warn('Suspicious rapid clicking detected');
        rapidClicks = 0;
      }
    } else {
      rapidClicks = 0;
    }
    lastClickTime = now;
  });
};

/**
 * Validate URL for security
 */
export const validateUrl = (url: string): boolean => {
  try {
    const parsedUrl = new URL(url);
    
    // Block javascript: and data: URLs in links
    if (parsedUrl.protocol === 'javascript:' || parsedUrl.protocol === 'data:') {
      return false;
    }
    
    // Allow only HTTPS in production
    if (process.env.NODE_ENV === 'production' && parsedUrl.protocol !== 'https:') {
      return false;
    }
    
    return true;
  } catch {
    return false;
  }
};

/**
 * Sanitize URL for safe usage
 */
export const sanitizeUrl = (url: string): string => {
  if (!validateUrl(url)) {
    return '#';
  }
  return url;
};
