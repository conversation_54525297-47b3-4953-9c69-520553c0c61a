/**
 * Global Teardown for E2E Tests
 * Cleans up the testing environment after running tests
 */

import { chromium, FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global teardown for E2E tests...');

  // Launch browser for teardown
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Clean up test data
    console.log('🗑️ Cleaning up test data...');
    
    // Clear browser storage
    await page.goto('http://localhost:5173');
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
      
      // Clear any IndexedDB data if used
      if ('indexedDB' in window) {
        indexedDB.databases?.().then(databases => {
          databases.forEach(db => {
            if (db.name) {
              indexedDB.deleteDatabase(db.name);
            }
          });
        });
      }
    });

    // You can add API calls here to clean up test data
    // For example, deleting test users, posts, etc.
    
    console.log('✅ Test data cleanup completed');

    // Generate test report summary
    console.log('📊 Generating test report summary...');
    
    // You can add logic here to generate custom reports
    // or send notifications about test results
    
    console.log('✅ Global teardown completed successfully');

  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error in teardown to avoid masking test failures
  } finally {
    await context.close();
    await browser.close();
  }
}

export default globalTeardown;
