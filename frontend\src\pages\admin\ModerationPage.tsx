/**
 * Moderation Page - Admin and Moderator Access
 * Content moderation and community management
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import {
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  Flag,
  MessageSquare,
  Users,
  Clock,
  Filter,
  Search,
  MoreHorizontal,
  Ban,
  UserCheck,
  FileText,
  Image as ImageIcon,
  Video,
  Hash
} from 'lucide-react';

interface ModerationItem {
  id: string;
  type: 'post' | 'comment' | 'user' | 'business_plan' | 'business_idea';
  content: string;
  author: {
    id: string;
    username: string;
    avatar?: string;
  };
  reportedBy: {
    id: string;
    username: string;
  };
  reason: string;
  status: 'pending' | 'approved' | 'rejected' | 'escalated';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  reportedAt: string;
  category: string;
}

const ModerationPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [loading, setLoading] = useState(true);
  const [moderationItems, setModerationItems] = useState<ModerationItem[]>([]);
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedPriority, setSelectedPriority] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  useEffect(() => {
    loadModerationData();
  }, [selectedFilter, selectedPriority]);

  const loadModerationData = async () => {
    setLoading(true);
    try {
      // Simulate API call - replace with real API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setModerationItems([
        {
          id: '1',
          type: 'post',
          content: 'This is a sample post that has been reported for inappropriate content...',
          author: { id: 'user1', username: 'john_doe', avatar: '/avatars/user1.jpg' },
          reportedBy: { id: 'user2', username: 'jane_smith' },
          reason: 'Inappropriate content',
          status: 'pending',
          priority: 'high',
          createdAt: '2024-01-15T10:30:00Z',
          reportedAt: '2024-01-15T14:20:00Z',
          category: 'Community'
        },
        {
          id: '2',
          type: 'comment',
          content: 'This comment contains spam links and promotional content...',
          author: { id: 'user3', username: 'spammer123' },
          reportedBy: { id: 'user4', username: 'moderator_user' },
          reason: 'Spam/Promotional',
          status: 'pending',
          priority: 'medium',
          createdAt: '2024-01-15T09:15:00Z',
          reportedAt: '2024-01-15T13:45:00Z',
          category: 'Community'
        },
        {
          id: '3',
          type: 'business_plan',
          content: 'Business plan contains potentially fraudulent information...',
          author: { id: 'user5', username: 'entrepreneur_x' },
          reportedBy: { id: 'user6', username: 'investor_y' },
          reason: 'Fraudulent information',
          status: 'escalated',
          priority: 'urgent',
          createdAt: '2024-01-14T16:20:00Z',
          reportedAt: '2024-01-15T08:30:00Z',
          category: 'Business'
        },
        {
          id: '4',
          type: 'user',
          content: 'User profile contains inappropriate images and false credentials...',
          author: { id: 'user7', username: 'fake_profile' },
          reportedBy: { id: 'user8', username: 'community_member' },
          reason: 'Fake profile/credentials',
          status: 'pending',
          priority: 'high',
          createdAt: '2024-01-13T12:00:00Z',
          reportedAt: '2024-01-15T11:15:00Z',
          category: 'User Management'
        }
      ]);
    } catch (error) {
      console.error('Error loading moderation data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'post':
        return <MessageSquare className="w-4 h-4" />;
      case 'comment':
        return <MessageSquare className="w-4 h-4" />;
      case 'user':
        return <Users className="w-4 h-4" />;
      case 'business_plan':
        return <FileText className="w-4 h-4" />;
      case 'business_idea':
        return <Hash className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'escalated':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-500';
      case 'high':
        return 'bg-orange-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  const handleApprove = (itemId: string) => {
    setModerationItems(items =>
      items.map(item =>
        item.id === itemId ? { ...item, status: 'approved' as const } : item
      )
    );
  };

  const handleReject = (itemId: string) => {
    setModerationItems(items =>
      items.map(item =>
        item.id === itemId ? { ...item, status: 'rejected' as const } : item
      )
    );
  };

  const handleEscalate = (itemId: string) => {
    setModerationItems(items =>
      items.map(item =>
        item.id === itemId ? { ...item, status: 'escalated' as const } : item
      )
    );
  };

  const filteredItems = moderationItems.filter(item => {
    const matchesFilter = selectedFilter === 'all' || item.status === selectedFilter;
    const matchesPriority = selectedPriority === 'all' || item.priority === selectedPriority;
    const matchesSearch = searchQuery === '' || 
      item.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.author.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.reason.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesFilter && matchesPriority && matchesSearch;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 text-white p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
          <span className="ml-4 text-lg">{t('Loading moderation queue...')}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className={`flex items-center justify-between mb-8 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="w-12 h-12 bg-purple-600/30 rounded-lg flex items-center justify-center">
              <Shield className="w-6 h-6 text-purple-400" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">{t('Content Moderation')}</h1>
              <p className="text-purple-200">{t('Review and manage reported content')}</p>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 mb-6">
          <div className={`flex flex-wrap items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-purple-400" />
              <select
                value={selectedFilter}
                onChange={(e) => setSelectedFilter(e.target.value)}
                className="bg-indigo-900/50 border border-indigo-700 rounded-lg px-3 py-2 text-white text-sm"
              >
                <option value="all">{t('All Status')}</option>
                <option value="pending">{t('Pending')}</option>
                <option value="approved">{t('Approved')}</option>
                <option value="rejected">{t('Rejected')}</option>
                <option value="escalated">{t('Escalated')}</option>
              </select>
            </div>

            <div className="flex items-center gap-2">
              <AlertTriangle className="w-4 h-4 text-purple-400" />
              <select
                value={selectedPriority}
                onChange={(e) => setSelectedPriority(e.target.value)}
                className="bg-indigo-900/50 border border-indigo-700 rounded-lg px-3 py-2 text-white text-sm"
              >
                <option value="all">{t('All Priorities')}</option>
                <option value="urgent">{t('Urgent')}</option>
                <option value="high">{t('High')}</option>
                <option value="medium">{t('Medium')}</option>
                <option value="low">{t('Low')}</option>
              </select>
            </div>

            <div className="flex items-center gap-2 flex-1 max-w-md">
              <Search className="w-4 h-4 text-purple-400" />
              <input
                type="text"
                placeholder={t('Search content, users, or reasons...')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 bg-indigo-900/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-purple-300 text-sm"
              />
            </div>
          </div>
        </div>

        {/* Moderation Queue */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg border border-indigo-800/50 overflow-hidden">
          <div className="p-6 border-b border-indigo-800/50">
            <h2 className="text-xl font-semibold flex items-center gap-2">
              <Flag className="w-5 h-5 text-purple-400" />
              {t('Moderation Queue')} ({filteredItems.length})
            </h2>
          </div>
          
          <div className="divide-y divide-indigo-800/50">
            {filteredItems.map((item) => (
              <div key={item.id} className="p-6 hover:bg-indigo-900/20">
                <div className={`flex items-start gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  {/* Priority Indicator */}
                  <div className={`w-1 h-16 rounded-full ${getPriorityColor(item.priority)}`} />
                  
                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className={`flex items-center gap-3 mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className="flex items-center gap-2 text-purple-400">
                        {getTypeIcon(item.type)}
                        <span className="text-sm font-medium">{t(item.type)}</span>
                      </div>
                      
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(item.status)}`}>
                        {t(item.status)}
                      </span>
                      
                      <span className="text-xs text-purple-300">{item.category}</span>
                    </div>
                    
                    <div className="mb-3">
                      <p className="text-sm text-gray-300 line-clamp-2">{item.content}</p>
                    </div>
                    
                    <div className={`flex items-center gap-4 text-xs text-purple-200 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span>{t('Author')}: <span className="text-white">{item.author.username}</span></span>
                      <span>{t('Reported by')}: <span className="text-white">{item.reportedBy.username}</span></span>
                      <span>{t('Reason')}: <span className="text-yellow-400">{item.reason}</span></span>
                      <span>{t('Priority')}: <span className="text-orange-400">{t(item.priority)}</span></span>
                    </div>
                  </div>
                  
                  {/* Actions */}
                  {item.status === 'pending' && (
                    <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <button
                        onClick={() => handleApprove(item.id)}
                        className="flex items-center gap-1 px-3 py-1 bg-green-600 hover:bg-green-700 rounded-lg text-xs transition-colors"
                        title={t('Approve')}
                      >
                        <CheckCircle className="w-3 h-3" />
                        {t('Approve')}
                      </button>
                      
                      <button
                        onClick={() => handleReject(item.id)}
                        className="flex items-center gap-1 px-3 py-1 bg-red-600 hover:bg-red-700 rounded-lg text-xs transition-colors"
                        title={t('Reject')}
                      >
                        <XCircle className="w-3 h-3" />
                        {t('Reject')}
                      </button>
                      
                      <button
                        onClick={() => handleEscalate(item.id)}
                        className="flex items-center gap-1 px-3 py-1 bg-purple-600 hover:bg-purple-700 rounded-lg text-xs transition-colors"
                        title={t('Escalate')}
                      >
                        <AlertTriangle className="w-3 h-3" />
                        {t('Escalate')}
                      </button>
                      
                      <button
                        className="flex items-center gap-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded-lg text-xs transition-colors"
                        title={t('View Details')}
                      >
                        <Eye className="w-3 h-3" />
                        {t('View')}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
            
            {filteredItems.length === 0 && (
              <div className="p-12 text-center">
                <Shield className="w-12 h-12 text-purple-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">{t('No items to moderate')}</h3>
                <p className="text-purple-200">{t('All content has been reviewed or no items match your filters.')}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModerationPage;
