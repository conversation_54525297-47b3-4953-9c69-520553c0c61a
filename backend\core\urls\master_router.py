"""
🎯 MASTER ROUTER CONFIGURATION
Centralized router management to eliminate duplicate DefaultRouter instances

CONSOLIDATION BENEFITS:
- Single source of truth for all DRF routers
- Eliminates duplicate router configurations
- Organized by functional areas
- Easy to maintain and extend
- Clear separation of concerns

USAGE:
Instead of creating multiple DefaultRouter instances across different apps,
import and use the appropriate router from this file.
"""

from rest_framework.routers import DefaultRouter

# ========================================
# MASTER ROUTERS BY FUNCTIONAL AREA
# ========================================

# Main API Router - Core platform features
main_api_router = DefaultRouter()

# Business Platform Router - Incubator and business features
business_router = DefaultRouter()

# Admin Router - Administrative features
admin_router = DefaultRouter()

# AI Router - AI and ML services
ai_router = DefaultRouter()

# Forum Router - Community and discussion features
forum_router = DefaultRouter()

# User Router - User management and profiles
user_router = DefaultRouter()

# Search Router - Search and discovery features
search_router = DefaultRouter()

# ========================================
# ROUTER REGISTRATION FUNCTIONS
# ========================================

def register_main_api_routes():
    """Register main API routes (events, resources, posts, etc.)"""
    from api.views import (
        EventViewSet, ResourceViewSet, PostViewSet, CommentViewSet,
        TagViewSet, MembershipApplicationViewSet
    )
    
    main_api_router.register(r'events', EventViewSet)
    main_api_router.register(r'resources', ResourceViewSet)
    main_api_router.register(r'posts', PostViewSet)
    main_api_router.register(r'comments', CommentViewSet)
    main_api_router.register(r'tags', TagViewSet)
    main_api_router.register(r'membership-applications', MembershipApplicationViewSet)

def register_business_routes():
    """Register business incubator routes"""
    from incubator.views import (
        BusinessIdeaViewSet, ProgressUpdateViewSet,
        IncubatorResourceViewSet, MentorshipApplicationViewSet,
        InvestorProfileViewSet, FundingOpportunityViewSet,
        FundingApplicationViewSet, InvestmentViewSet,
        MentorProfileViewSet, MentorExpertiseViewSet, MentorshipMatchViewSet
    )
    from incubator.views_milestone import (
        BusinessMilestoneViewSet, BusinessGoalViewSet,
        MentorRecommendationViewSet, BusinessAnalyticsViewSet
    )
    from incubator.views_session import MentorshipSessionViewSet
    from incubator.views_feedback import MentorshipFeedbackViewSet
    from incubator.views_business_plan import (
        BusinessPlanViewSet, BusinessPlanSectionViewSet
    )
    from incubator.views_template_customization import (
        BusinessPlanTemplateViewSet, CustomBusinessPlanTemplateViewSet,
        TemplateSectionDefinitionViewSet
    )
    from incubator.views_analytics import (
        EnhancedBusinessAnalyticsViewSet, MetricDefinitionViewSet
    )
    from incubator.views_template_analytics import TemplateAnalyticsViewSet
    from incubator.views_business_plan_analytics import BusinessPlanAnalyticsViewSet
    
    # Core business features
    business_router.register(r'business-ideas', BusinessIdeaViewSet)
    business_router.register(r'progress-updates', ProgressUpdateViewSet)
    business_router.register(r'resources', IncubatorResourceViewSet)
    business_router.register(r'mentorship-applications', MentorshipApplicationViewSet)
    
    # Investment and funding
    business_router.register(r'investor-profiles', InvestorProfileViewSet)
    business_router.register(r'funding-opportunities', FundingOpportunityViewSet)
    business_router.register(r'funding-applications', FundingApplicationViewSet)
    business_router.register(r'investments', InvestmentViewSet)
    
    # Mentorship
    business_router.register(r'mentor-profiles', MentorProfileViewSet)
    business_router.register(r'mentor-expertise', MentorExpertiseViewSet)
    business_router.register(r'mentorship-matches', MentorshipMatchViewSet)
    business_router.register(r'mentorship-sessions', MentorshipSessionViewSet)
    business_router.register(r'mentorship-feedback', MentorshipFeedbackViewSet)
    
    # Milestones and goals
    business_router.register(r'business-milestones', BusinessMilestoneViewSet)
    business_router.register(r'business-goals', BusinessGoalViewSet)
    business_router.register(r'mentor-recommendations', MentorRecommendationViewSet)
    business_router.register(r'business-analytics', BusinessAnalyticsViewSet)
    
    # Business plans
    business_router.register(r'business-plan-templates', BusinessPlanTemplateViewSet)
    business_router.register(r'business-plans', BusinessPlanViewSet)
    business_router.register(r'business-plan-sections', BusinessPlanSectionViewSet)
    business_router.register(r'custom-templates', CustomBusinessPlanTemplateViewSet, basename='custom-templates')
    business_router.register(r'template-sections', TemplateSectionDefinitionViewSet)
    
    # Analytics
    business_router.register(r'enhanced-analytics', EnhancedBusinessAnalyticsViewSet, basename='enhanced-analytics')
    business_router.register(r'metric-definitions', MetricDefinitionViewSet)
    business_router.register(r'template-analytics', TemplateAnalyticsViewSet, basename='template-analytics')
    business_router.register(r'business-plan-analytics', BusinessPlanAnalyticsViewSet, basename='business-plan-analytics')

def register_admin_routes():
    """Register super admin routes"""
    from superadmin.views import (
        SuperAdminSystemViewSet, SuperAdminContentViewSet,
        SuperAdminUserViewSet, SuperAdminForumViewSet,
        SuperAdminIncubatorViewSet, SuperAdminAIViewSet
    )
    
    admin_router.register(r'system', SuperAdminSystemViewSet, basename='superadmin-system')
    admin_router.register(r'content', SuperAdminContentViewSet, basename='superadmin-content')
    admin_router.register(r'users', SuperAdminUserViewSet, basename='superadmin-users')
    admin_router.register(r'forum', SuperAdminForumViewSet, basename='superadmin-forum')
    admin_router.register(r'incubator', SuperAdminIncubatorViewSet, basename='superadmin-incubator')
    admin_router.register(r'ai', SuperAdminAIViewSet, basename='superadmin-ai')

def register_forum_routes():
    """Register forum and community routes"""
    from forums.views import (
        ForumCategoryViewSet, ForumTopicViewSet, ForumThreadViewSet,
        ForumPostViewSet, UserReputationViewSet, ReputationActivityViewSet,
        ContentFlagViewSet, TopicSubscriptionViewSet,
        ThreadSubscriptionViewSet, ForumAttachmentViewSet
    )
    
    forum_router.register(r'categories', ForumCategoryViewSet, basename='forum-category')
    forum_router.register(r'topics', ForumTopicViewSet, basename='forum-topic')
    forum_router.register(r'threads', ForumThreadViewSet, basename='forum-thread')
    forum_router.register(r'posts', ForumPostViewSet, basename='forum-post')
    forum_router.register(r'reputation', UserReputationViewSet, basename='user-reputation')
    forum_router.register(r'reputation-activities', ReputationActivityViewSet, basename='reputation-activity')
    forum_router.register(r'content-flags', ContentFlagViewSet, basename='content-flag')
    forum_router.register(r'topic-subscriptions', TopicSubscriptionViewSet, basename='topic-subscription')
    forum_router.register(r'thread-subscriptions', ThreadSubscriptionViewSet, basename='thread-subscription')
    forum_router.register(r'attachments', ForumAttachmentViewSet, basename='forum-attachment')

def register_user_routes():
    """Register user management routes"""
    from users.views import UserViewSet, UserProfileViewSet
    from users.views.role_management_views import RoleManagementViewSet
    from users.views_approval import UserApprovalViewSet
    from users.views_role_applications import RoleApplicationViewSet
    
    user_router.register(r'users', UserViewSet)
    user_router.register(r'profiles', UserProfileViewSet)
    user_router.register(r'approvals', UserApprovalViewSet, basename='userapproval')
    user_router.register(r'role-management', RoleManagementViewSet, basename='rolemanagement')
    user_router.register(r'role-applications', RoleApplicationViewSet, basename='roleapplication')

def register_search_routes():
    """Register search and discovery routes"""
    from search.views import SearchViewSet
    
    search_router.register(r'documents', SearchViewSet)

def register_ai_routes():
    """Register AI service routes (currently using function-based views)"""
    # AI routes are currently handled by consolidated_ai_urls.py
    # This function is reserved for future AI ViewSets
    pass

# ========================================
# INITIALIZATION FUNCTION
# ========================================

def initialize_all_routers():
    """Initialize all routers with their respective routes"""
    register_main_api_routes()
    register_business_routes()
    register_admin_routes()
    register_forum_routes()
    register_user_routes()
    register_search_routes()
    register_ai_routes()

# ========================================
# ROUTER EXPORTS
# ========================================

# Export routers for use in URL configurations
__all__ = [
    'main_api_router',
    'business_router', 
    'admin_router',
    'ai_router',
    'forum_router',
    'user_router',
    'search_router',
    'initialize_all_routers'
]
