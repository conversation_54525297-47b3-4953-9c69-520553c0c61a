"""
🎯 UNIFIED ROLE SERVICE
Consolidated role management service that replaces scattered role logic
across the entire application with a single, consistent interface.

This service eliminates:
- Multiple permission classes (4+ files)
- Hardcoded role arrays (8+ instances)
- Inconsistent role checking patterns (6+ variations)
- Complex role management logic (1000+ lines)

Provides:
- Single source of truth for all role logic
- Consistent API across frontend and backend
- Database-driven role definitions
- Optimized caching and performance
"""

from django.core.cache import cache
from django.contrib.auth.models import User
from django.db.models import Q
from typing import List, Dict, Optional, Set, Union
import logging

# CONSOLIDATED: Import from unified role configuration
from core.config.role_config import (
    ROLE_HIERARCHY, PERMISSION_HIERARCHY, ROLE_PERMISSION_MAPPING,
    get_role_groups, get_role_level, get_permission_level, get_role_permission
)

logger = logging.getLogger(__name__)


class UnifiedRoleService:
    """
    🎯 UNIFIED ROLE SERVICE
    Single source of truth for all role management across the application
    """
    
    # Cache keys for performance optimization
    CACHE_PREFIX = 'unified_role_service'
    USER_ROLES_CACHE_KEY = f'{CACHE_PREFIX}:user_roles:{{user_id}}'
    AVAILABLE_ROLES_CACHE_KEY = f'{CACHE_PREFIX}:available_roles'
    ROLE_HIERARCHY_CACHE_KEY = f'{CACHE_PREFIX}:role_hierarchy'
    CACHE_TIMEOUT = 300  # 5 minutes
    
    def __init__(self):
        """Initialize the unified role service"""
        self._role_hierarchy = None
        self._role_groups = None
    
    # ========================================
    # CORE ROLE CHECKING METHODS
    # ========================================
    
    def has_role(self, user: User, role_name: str) -> bool:
        """
        Check if user has a specific role
        
        Args:
            user: Django User instance
            role_name: Name of the role to check
            
        Returns:
            bool: True if user has the role
        """
        if not user or not user.is_authenticated:
            return False
            
        user_roles = self.get_user_roles(user)
        return role_name in user_roles
    
    def has_any_role(self, user: User, role_names: List[str]) -> bool:
        """
        Check if user has any of the specified roles
        
        Args:
            user: Django User instance
            role_names: List of role names to check
            
        Returns:
            bool: True if user has any of the roles
        """
        if not user or not user.is_authenticated:
            return False
            
        user_roles = self.get_user_roles(user)
        return any(role in user_roles for role in role_names)
    
    def has_all_roles(self, user: User, role_names: List[str]) -> bool:
        """
        Check if user has all of the specified roles
        
        Args:
            user: Django User instance
            role_names: List of role names to check
            
        Returns:
            bool: True if user has all of the roles
        """
        if not user or not user.is_authenticated:
            return False
            
        user_roles = self.get_user_roles(user)
        return all(role in user_roles for role in role_names)
    
    def has_permission(self, user: User, permission_level: str) -> bool:
        """
        Check if user has a specific permission level
        
        Args:
            user: Django User instance
            permission_level: Permission level to check (read, write, moderate, admin, super_admin)
            
        Returns:
            bool: True if user has the permission level
        """
        if not user or not user.is_authenticated:
            return False
            
        user_permission = self.get_user_permission_level(user)
        hierarchy = self.get_permission_hierarchy()
        
        user_level = hierarchy.get(user_permission, 0)
        required_level = hierarchy.get(permission_level, 0)
        
        return user_level >= required_level
    
    # ========================================
    # ROLE DATA RETRIEVAL METHODS
    # ========================================
    
    def get_user_roles(self, user: User) -> List[str]:
        """
        Get all roles for a user with caching
        
        Args:
            user: Django User instance
            
        Returns:
            List[str]: List of role names
        """
        if not user or not user.is_authenticated:
            return ['anonymous']
            
        cache_key = self.USER_ROLES_CACHE_KEY.format(user_id=user.id)
        cached_roles = cache.get(cache_key)
        
        if cached_roles is not None:
            return cached_roles
        
        roles = self._fetch_user_roles_from_db(user)
        cache.set(cache_key, roles, self.CACHE_TIMEOUT)
        
        return roles
    
    def get_user_primary_role(self, user: User) -> str:
        """
        Get user's primary role

        Args:
            user: Django User instance

        Returns:
            str: Primary role name (database role name)
        """
        if not user or not user.is_authenticated:
            return 'anonymous'

        # Check Django built-in roles first
        if user.is_superuser:
            return 'super_admin'  # Database role name
        if user.is_staff:
            return 'admin'  # Database role name

        # Check profile-based roles
        try:
            from users.models import UserProfile, UserRoleAssignment
            profile = user.profile

            # Get active role assignments
            assignments = UserRoleAssignment.objects.filter(
                user_profile=profile,
                is_active=True
            ).select_related('role').order_by('-role__permission_level')

            if assignments.exists():
                # Return the highest priority role
                return assignments.first().role.name

        except (UserProfile.DoesNotExist, AttributeError):
            pass

        return 'user'  # Default role



    def get_user_permission_level(self, user: User) -> str:
        """
        Get user's highest permission level
        
        Args:
            user: Django User instance
            
        Returns:
            str: Permission level
        """
        if not user or not user.is_authenticated:
            return 'none'
            
        primary_role = self.get_user_primary_role(user)
        role_permissions = self.get_role_permission_mapping()
        
        return role_permissions.get(primary_role, 'read')
    
    # ========================================
    # ROLE HIERARCHY AND GROUPS
    # ========================================
    
    def get_role_hierarchy(self) -> Dict[str, int]:
        """
        Get role hierarchy with numeric levels

        Returns:
            Dict[str, int]: Role hierarchy mapping
        """
        # CONSOLIDATED: Use unified role configuration
        return ROLE_HIERARCHY.copy()
    
    def get_permission_hierarchy(self) -> Dict[str, int]:
        """
        Get permission level hierarchy

        Returns:
            Dict[str, int]: Permission hierarchy mapping
        """
        # CONSOLIDATED: Use unified role configuration
        return PERMISSION_HIERARCHY.copy()
    
    def get_role_groups(self) -> Dict[str, List[str]]:
        """
        Get predefined role groups
        
        Returns:
            Dict[str, List[str]]: Role groups mapping
        """
        if self._role_groups is None:
            available_roles = self.get_available_roles()
            role_names = [role['name'] for role in available_roles]
            
            # CONSOLIDATED: Use unified role configuration
            self._role_groups = get_role_groups(role_names)
        
        return self._role_groups
    
    def get_role_permission_mapping(self) -> Dict[str, str]:
        """
        Get role to permission level mapping

        Returns:
            Dict[str, str]: Role permission mapping
        """
        # CONSOLIDATED: Use unified role configuration
        mapping = ROLE_PERMISSION_MAPPING.copy()
        mapping['anonymous'] = 'none'  # Add anonymous mapping
        return mapping
    
    # ========================================
    # UTILITY METHODS
    # ========================================
    
    def get_available_roles(self) -> List[Dict]:
        """
        Get all available roles from database with caching
        
        Returns:
            List[Dict]: List of role dictionaries
        """
        cached_roles = cache.get(self.AVAILABLE_ROLES_CACHE_KEY)
        
        if cached_roles is not None:
            return cached_roles
        
        try:
            from users.models import UserRole
            roles = UserRole.objects.filter(is_active=True).values(
                'id', 'name', 'display_name', 'description', 
                'permission_level', 'requires_approval'
            )
            roles_list = list(roles)
            cache.set(self.AVAILABLE_ROLES_CACHE_KEY, roles_list, self.CACHE_TIMEOUT)
            return roles_list
            
        except Exception as e:
            logger.error(f"Error fetching available roles: {e}")
            return self._get_fallback_roles()
    
    def clear_user_cache(self, user: User) -> None:
        """
        Clear cached data for a specific user
        
        Args:
            user: Django User instance
        """
        cache_key = self.USER_ROLES_CACHE_KEY.format(user_id=user.id)
        cache.delete(cache_key)
    
    def clear_all_cache(self) -> None:
        """Clear all role-related cache"""
        cache.delete(self.AVAILABLE_ROLES_CACHE_KEY)
        cache.delete(self.ROLE_HIERARCHY_CACHE_KEY)
        # Clear role groups cache by resetting the instance variable
        self._role_groups = None
    
    # ========================================
    # PRIVATE HELPER METHODS
    # ========================================
    
    def _fetch_user_roles_from_db(self, user: User) -> List[str]:
        """
        Fetch user roles from database
        
        Args:
            user: Django User instance
            
        Returns:
            List[str]: List of role names
        """
        roles = []
        
        # Check Django built-in roles
        if user.is_superuser:
            roles.append('super_admin')
        elif user.is_staff:
            roles.append('admin')
        
        # Check profile-based roles
        try:
            from users.models import UserProfile, UserRoleAssignment
            profile = user.profile
            
            # Get active role assignments
            assignments = UserRoleAssignment.objects.filter(
                user_profile=profile,
                is_active=True,
                is_approved=True
            ).select_related('role')
            
            for assignment in assignments:
                if assignment.role.name not in roles:
                    roles.append(assignment.role.name)
                    
        except Exception as e:
            logger.warning(f"Error fetching user roles for {user.username}: {e}")
        
        # Default to 'user' if no roles found
        if not roles:
            roles.append('user')
            
        return roles
    
    def _get_fallback_roles(self) -> List[Dict]:
        """
        Get fallback roles if database fetch fails
        
        Returns:
            List[Dict]: Fallback role definitions
        """
        return [
            {'id': 1, 'name': 'admin', 'display_name': 'Admin', 'description': 'Administrator', 'permission_level': 'admin', 'requires_approval': True},
            {'id': 2, 'name': 'super_admin', 'display_name': 'Super Admin', 'description': 'Super administrator', 'permission_level': 'super_admin', 'requires_approval': True},
            {'id': 3, 'name': 'moderator', 'display_name': 'Moderator', 'description': 'Content moderator', 'permission_level': 'moderate', 'requires_approval': True},
            {'id': 4, 'name': 'entrepreneur', 'display_name': 'Entrepreneur', 'description': 'Business founder', 'permission_level': 'write', 'requires_approval': True},
            {'id': 5, 'name': 'mentor', 'display_name': 'Mentor', 'description': 'Business mentor', 'permission_level': 'write', 'requires_approval': True},
            {'id': 6, 'name': 'investor', 'display_name': 'Investor', 'description': 'Business investor', 'permission_level': 'write', 'requires_approval': True},
            {'id': 7, 'name': 'user', 'display_name': 'User', 'description': 'Regular user', 'permission_level': 'read', 'requires_approval': False}
        ]


# ========================================
# GLOBAL INSTANCE
# ========================================

# Create global instance for use across the application
unified_role_service = UnifiedRoleService()

# Import consolidated AI service for easy access
from .consolidated_ai_service import consolidated_ai_service


# ========================================
# CONVENIENCE FUNCTIONS
# ========================================

def has_role(user: User, role_name: str) -> bool:
    """Convenience function for role checking"""
    return unified_role_service.has_role(user, role_name)

def has_any_role(user: User, role_names: List[str]) -> bool:
    """Convenience function for multiple role checking"""
    return unified_role_service.has_any_role(user, role_names)

def has_permission(user: User, permission_level: str) -> bool:
    """Convenience function for permission checking"""
    return unified_role_service.has_permission(user, permission_level)

def get_user_roles(user: User) -> List[str]:
    """Convenience function for getting user roles"""
    return unified_role_service.get_user_roles(user)

def get_user_primary_role(user: User) -> str:
    """Convenience function for getting primary role"""
    return unified_role_service.get_user_primary_role(user)
