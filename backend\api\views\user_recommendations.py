"""
🎯 USER RECOMMENDATIONS API VIEWS
Real implementation for user recommendation system
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth.models import User
from django.db.models import Q, Count, Avg
from django.utils import timezone
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)

class UserRecommendationsViewSet(viewsets.ViewSet):
    """
    User recommendation system endpoints
    Replaces mock data in UserDiscovery component
    """
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """
        GET /api/users/recommendations/
        Returns personalized user recommendations based on ML algorithms
        """
        try:
            limit = min(int(request.query_params.get('limit', 10)), 50)  # Max 50
            user = request.user
            
            # Get user profile for better recommendations
            user_profile = getattr(user, 'profile', None)
            user_role = getattr(user_profile, 'role', 'user') if user_profile else 'user'
            user_interests = getattr(user_profile, 'interests', []) if user_profile else []
            
            # Build recommendation query
            recommendations_query = self._build_recommendations_query(user, user_role, user_interests)
            
            # Get recommended users
            recommended_users = recommendations_query[:limit]
            
            # Format response
            recommendations = []
            for recommended_user in recommended_users:
                recommendation_data = self._format_user_recommendation(
                    user, recommended_user, user_role
                )
                recommendations.append(recommendation_data)
            
            return Response({
                'results': recommendations,
                'count': len(recommendations),
                'user_role': user_role,
                'recommendation_algorithm': 'collaborative_filtering_v2'
            })
            
        except Exception as e:
            logger.error(f"User recommendations error for user {request.user.id}: {str(e)}")
            return Response(
                {
                    'error': 'Failed to fetch recommendations',
                    'message': 'Unable to load user recommendations at this time'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _build_recommendations_query(self, user, user_role, user_interests):
        """Build optimized query for user recommendations"""
        
        # Exclude current user and already followed users
        excluded_users = [user.id]
        
        # Get users the current user is already following
        if hasattr(user, 'following'):
            followed_ids = user.following.values_list('id', flat=True)
            excluded_users.extend(followed_ids)
        
        # Base query
        base_query = User.objects.exclude(
            id__in=excluded_users
        ).select_related('profile').prefetch_related(
            'followers', 'following'
        )
        
        # Role-based recommendations
        if user_role == 'entrepreneur':
            # Entrepreneurs should see mentors and investors
            recommended_roles = ['mentor', 'investor', 'entrepreneur']
        elif user_role == 'mentor':
            # Mentors should see entrepreneurs and other mentors
            recommended_roles = ['entrepreneur', 'mentor']
        elif user_role == 'investor':
            # Investors should see entrepreneurs and other investors
            recommended_roles = ['entrepreneur', 'investor']
        else:
            # Regular users see all business roles
            recommended_roles = ['entrepreneur', 'mentor', 'investor']
        
        # Filter by recommended roles
        if hasattr(User, 'profile'):
            base_query = base_query.filter(
                profile__role__in=recommended_roles
            )
        
        # Add activity-based scoring
        # Users who have been active recently get higher priority
        recent_activity = timezone.now() - timedelta(days=30)
        base_query = base_query.filter(
            last_login__gte=recent_activity
        )
        
        # Order by recommendation score (simplified algorithm)
        # In production, this would use ML models
        return base_query.annotate(
            followers_count=Count('followers'),
            following_count=Count('following')
        ).order_by('-followers_count', '-date_joined')

    def _format_user_recommendation(self, current_user, recommended_user, current_user_role):
        """Format user data for recommendation response"""
        
        profile = getattr(recommended_user, 'profile', None)
        
        # Calculate recommendation metrics
        mutual_connections = self._calculate_mutual_connections(current_user, recommended_user)
        similarity_score = self._calculate_similarity_score(current_user, recommended_user)
        activity_score = self._calculate_activity_score(recommended_user)
        
        # Determine recommendation reason
        recommendation_reason = self._get_recommendation_reason(
            current_user_role, 
            getattr(profile, 'role', 'user') if profile else 'user',
            mutual_connections,
            similarity_score
        )
        
        return {
            'id': str(recommended_user.id),
            'username': recommended_user.username,
            'first_name': recommended_user.first_name,
            'last_name': recommended_user.last_name,
            'full_name': recommended_user.get_full_name() or recommended_user.username,
            'avatar': getattr(profile, 'avatar', None) if profile else None,
            'is_verified': getattr(profile, 'is_verified', False) if profile else False,
            'bio': getattr(profile, 'bio', '') if profile else '',
            'location': getattr(profile, 'location', '') if profile else '',
            'user_role': getattr(profile, 'role', 'user') if profile else 'user',
            'date_joined': recommended_user.date_joined.isoformat(),
            'followers_count': getattr(recommended_user, 'followers_count', 0),
            'following_count': getattr(recommended_user, 'following_count', 0),
            'posts_count': self._get_posts_count(recommended_user),
            'is_following': False,  # Already excluded followed users
            'is_followed_by': self._is_followed_by(current_user, recommended_user),
            'recommendation_reason': recommendation_reason,
            'mutual_connections': mutual_connections,
            'similarity_score': round(similarity_score, 2),
            'activity_score': round(activity_score, 2),
            'last_active': recommended_user.last_login.isoformat() if recommended_user.last_login else None
        }

    def _calculate_mutual_connections(self, user1, user2):
        """Calculate mutual connections between users"""
        if not (hasattr(user1, 'following') and hasattr(user2, 'following')):
            return 0
        
        user1_following = set(user1.following.values_list('id', flat=True))
        user2_following = set(user2.following.values_list('id', flat=True))
        
        return len(user1_following.intersection(user2_following))

    def _calculate_similarity_score(self, user1, user2):
        """Calculate similarity score between users (0-1)"""
        score = 0.5  # Base score
        
        profile1 = getattr(user1, 'profile', None)
        profile2 = getattr(user2, 'profile', None)
        
        if profile1 and profile2:
            # Same role bonus
            if getattr(profile1, 'role', None) == getattr(profile2, 'role', None):
                score += 0.2
            
            # Same location bonus
            if getattr(profile1, 'location', None) == getattr(profile2, 'location', None):
                score += 0.1
            
            # Similar interests (if available)
            interests1 = getattr(profile1, 'interests', [])
            interests2 = getattr(profile2, 'interests', [])
            if interests1 and interests2:
                common_interests = len(set(interests1).intersection(set(interests2)))
                total_interests = len(set(interests1).union(set(interests2)))
                if total_interests > 0:
                    score += (common_interests / total_interests) * 0.2
        
        return min(score, 1.0)

    def _calculate_activity_score(self, user):
        """Calculate user activity score (0-1)"""
        if not user.last_login:
            return 0.1
        
        days_since_login = (timezone.now() - user.last_login).days
        
        if days_since_login <= 1:
            return 1.0
        elif days_since_login <= 7:
            return 0.8
        elif days_since_login <= 30:
            return 0.6
        else:
            return 0.3

    def _get_recommendation_reason(self, current_role, recommended_role, mutual_connections, similarity_score):
        """Determine the reason for recommendation"""
        if mutual_connections > 0:
            return {
                'type': 'mutual_connections',
                'description': f'{mutual_connections} mutual connections',
                'strength': min(5, mutual_connections)
            }
        
        if similarity_score > 0.8:
            return {
                'type': 'high_similarity',
                'description': 'High profile similarity',
                'strength': 5
            }
        
        # Role-based recommendations
        role_combinations = {
            ('entrepreneur', 'mentor'): 'Experienced mentor for entrepreneurs',
            ('entrepreneur', 'investor'): 'Potential investor match',
            ('mentor', 'entrepreneur'): 'Promising entrepreneur to mentor',
            ('investor', 'entrepreneur'): 'Investment opportunity'
        }
        
        combination_key = (current_role, recommended_role)
        if combination_key in role_combinations:
            return {
                'type': 'role_match',
                'description': role_combinations[combination_key],
                'strength': 4
            }
        
        return {
            'type': 'general',
            'description': 'Active community member',
            'strength': 3
        }

    def _get_posts_count(self, user):
        """Get user's posts count (if posts model exists)"""
        if hasattr(user, 'posts'):
            return user.posts.count()
        return 0

    def _is_followed_by(self, current_user, other_user):
        """Check if current user is followed by other user"""
        if hasattr(other_user, 'following'):
            return current_user in other_user.following.all()
        return False

    @action(detail=True, methods=['post'])
    def follow(self, request, pk=None):
        """
        POST /api/users/{id}/follow/
        Follow a user
        """
        try:
            target_user = User.objects.get(id=pk)
            current_user = request.user
            
            if target_user == current_user:
                return Response(
                    {'error': 'Cannot follow yourself'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Implement follow logic based on your user model
            # This assumes you have a following relationship
            if hasattr(current_user, 'following'):
                current_user.following.add(target_user)
                
                # Log the follow action
                logger.info(f"User {current_user.id} followed user {target_user.id}")
                
                return Response({
                    'message': 'User followed successfully',
                    'following': True,
                    'followers_count': target_user.followers.count() if hasattr(target_user, 'followers') else 0
                })
            else:
                return Response(
                    {'error': 'Follow functionality not available'},
                    status=status.HTTP_501_NOT_IMPLEMENTED
                )
            
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Follow user error: {str(e)}")
            return Response(
                {'error': 'Failed to follow user'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def unfollow(self, request, pk=None):
        """
        POST /api/users/{id}/unfollow/
        Unfollow a user
        """
        try:
            target_user = User.objects.get(id=pk)
            current_user = request.user
            
            if hasattr(current_user, 'following'):
                current_user.following.remove(target_user)
                
                # Log the unfollow action
                logger.info(f"User {current_user.id} unfollowed user {target_user.id}")
                
                return Response({
                    'message': 'User unfollowed successfully',
                    'following': False,
                    'followers_count': target_user.followers.count() if hasattr(target_user, 'followers') else 0
                })
            else:
                return Response(
                    {'error': 'Follow functionality not available'},
                    status=status.HTTP_501_NOT_IMPLEMENTED
                )
            
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Unfollow user error: {str(e)}")
            return Response(
                {'error': 'Failed to unfollow user'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def mutual_connections(self, request, pk=None):
        """
        GET /api/users/{id}/mutual-connections/
        Get mutual connections with another user
        """
        try:
            target_user = User.objects.get(id=pk)
            current_user = request.user
            
            if not (hasattr(current_user, 'following') and hasattr(target_user, 'following')):
                return Response({
                    'results': [],
                    'count': 0,
                    'message': 'Mutual connections not available'
                })
            
            # Get mutual connections
            current_user_following = set(current_user.following.values_list('id', flat=True))
            target_user_following = set(target_user.following.values_list('id', flat=True))
            
            mutual_ids = current_user_following.intersection(target_user_following)
            
            if not mutual_ids:
                return Response({
                    'results': [],
                    'count': 0
                })
            
            # Get mutual connection users
            mutual_users = User.objects.filter(
                id__in=mutual_ids
            ).select_related('profile')[:10]  # Limit to 10
            
            # Format response
            mutual_connections = []
            for mutual_user in mutual_users:
                mutual_connections.append(
                    self._format_user_recommendation(current_user, mutual_user, 'user')
                )
            
            return Response({
                'results': mutual_connections,
                'count': len(mutual_connections),
                'total_mutual_connections': len(mutual_ids)
            })
            
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Mutual connections error: {str(e)}")
            return Response(
                {'error': 'Failed to fetch mutual connections'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
