[tool:pytest]
# Pytest configuration for community app testing

# Django settings
DJANGO_SETTINGS_MODULE = yasmeen_ai.settings
python_files = tests.py test_*.py *_tests.py
python_classes = Test* *Tests
python_functions = test_*

# Test discovery
testpaths = community/tests

# Markers
markers =
    security: Security-related tests
    integration: Integration tests
    unit: Unit tests
    slow: Slow tests that might take longer
    api: API endpoint tests
    models: Model tests
    views: View tests
    middleware: Middleware tests
    serializers: Serializer tests

# Output options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --disable-warnings
    --reuse-db
    --nomigrations
    --maxfail=10

# Coverage options
[coverage:run]
source = community
omit = 
    */migrations/*
    */tests/*
    */venv/*
    */env/*
    */__pycache__/*
    */settings/*
    manage.py
    */conftest.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

show_missing = True
skip_covered = False
precision = 2

[coverage:html]
directory = htmlcov
title = Community App Coverage Report
