"""
Community Security Middleware
Provides rate limiting, security headers, and request validation for community endpoints
"""

import time
import json
import logging
from typing import Dict, Optional, Tuple
from django.http import JsonResponse, HttpRequest, HttpResponse
from django.core.cache import cache
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin
from django.utils.cache import patch_cache_control
from django.contrib.auth.models import User
from .security import security_logger

# Configure logging
logger = logging.getLogger(__name__)

# Rate limiting configurations for community endpoints
COMMUNITY_RATE_LIMITS = {
    # Post operations
    '/api/community/posts/': {
        'GET': {'requests': 100, 'window': 3600},  # 100 requests per hour
        'POST': {'requests': 10, 'window': 3600},  # 10 posts per hour
    },
    '/api/community/posts/*/like/': {
        'POST': {'requests': 200, 'window': 3600},  # 200 likes per hour
    },
    '/api/community/posts/*/save/': {
        'POST': {'requests': 100, 'window': 3600},  # 100 saves per hour
    },
    '/api/community/posts/*/share/': {
        'POST': {'requests': 50, 'window': 3600},  # 50 shares per hour
    },
    '/api/community/posts/*/add_comment/': {
        'POST': {'requests': 50, 'window': 3600},  # 50 comments per hour
    },
    
    # Comment operations
    '/api/community/comments/': {
        'GET': {'requests': 200, 'window': 3600},  # 200 requests per hour
        'POST': {'requests': 50, 'window': 3600},  # 50 comments per hour
    },
    
    # Search and discovery
    '/api/community/hashtags/trending/': {
        'GET': {'requests': 60, 'window': 3600},  # 60 requests per hour
    },
    '/api/community/users/recommendations/': {
        'GET': {'requests': 30, 'window': 3600},  # 30 requests per hour
    },
    
    # Stats (more lenient for dashboard)
    '/api/community/stats/': {
        'GET': {'requests': 120, 'window': 3600},  # 120 requests per hour
    },
}

# Security headers for API responses
SECURITY_HEADERS = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'X-Permitted-Cross-Domain-Policies': 'none',
}

# HTTP caching configuration for community endpoints
COMMUNITY_CACHE_HEADERS = {
    '/api/community/posts/': {
        'GET': {
            'max_age': 300,      # 5 minutes
            'public': True,
            'must_revalidate': True,
        }
    },
    '/api/community/stats/': {
        'GET': {
            'max_age': 3600,     # 1 hour
            'public': True,
            's_maxage': 1800,    # 30 minutes for shared caches
        }
    },
    '/api/community/hashtags/': {
        'GET': {
            'max_age': 1800,     # 30 minutes
            'public': True,
            's_maxage': 900,     # 15 minutes for shared caches
        }
    },
    '/api/community/user-recommendations/': {
        'GET': {
            'max_age': 7200,     # 2 hours
            'private': True,     # User-specific data
            'must_revalidate': True,
        }
    },
    '/api/community/feed/': {
        'GET': {
            'max_age': 600,      # 10 minutes
            'private': True,     # User-specific data
            'must_revalidate': True,
        }
    },
}


class CommunitySecurityMiddleware(MiddlewareMixin):
    """
    Security middleware for community endpoints
    Provides rate limiting, security headers, and request validation
    """
    
    def __init__(self, get_response):
        super().__init__(get_response)
        self.get_response = get_response
    
    def process_request(self, request: HttpRequest) -> Optional[HttpResponse]:
        """
        Process incoming requests for security validation
        """
        # Only apply to community API endpoints
        if not request.path.startswith('/api/community/'):
            return None
        
        # Check rate limits
        rate_limit_response = self._check_rate_limits(request)
        if rate_limit_response:
            return rate_limit_response
        
        # Validate request content
        validation_response = self._validate_request_content(request)
        if validation_response:
            return validation_response
        
        return None
    
    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        """
        Process outgoing responses to add security headers and caching
        """
        # Only apply to community API endpoints
        if not request.path.startswith('/api/community/'):
            return response

        # Add security headers
        for header, value in SECURITY_HEADERS.items():
            response[header] = value

        # Add HTTP caching headers for GET requests
        if request.method == 'GET' and response.status_code == 200:
            self._add_cache_headers(request, response)
        else:
            # For non-GET requests or error responses, prevent caching
            response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response['Pragma'] = 'no-cache'
            response['Expires'] = '0'

        # Add CORS headers if needed
        if hasattr(settings, 'CORS_ALLOW_ALL_ORIGINS') and settings.CORS_ALLOW_ALL_ORIGINS:
            response['Access-Control-Allow-Origin'] = '*'
        elif hasattr(settings, 'CORS_ALLOWED_ORIGINS'):
            origin = request.META.get('HTTP_ORIGIN')
            if origin in settings.CORS_ALLOWED_ORIGINS:
                response['Access-Control-Allow-Origin'] = origin

        return response

    def _add_cache_headers(self, request: HttpRequest, response: HttpResponse):
        """
        Add appropriate HTTP caching headers based on endpoint
        """
        # Find matching cache configuration
        cache_config = None
        for endpoint_pattern, methods in COMMUNITY_CACHE_HEADERS.items():
            if request.path.startswith(endpoint_pattern.replace('*', '')):
                cache_config = methods.get(request.method)
                break

        if not cache_config:
            # Default caching for unspecified endpoints
            cache_config = {
                'max_age': 300,  # 5 minutes
                'private': True,
                'must_revalidate': True,
            }

        # Build Cache-Control header
        cache_control_parts = []

        if cache_config.get('public'):
            cache_control_parts.append('public')
        elif cache_config.get('private'):
            cache_control_parts.append('private')

        if 'max_age' in cache_config:
            cache_control_parts.append(f"max-age={cache_config['max_age']}")

        if 's_maxage' in cache_config:
            cache_control_parts.append(f"s-maxage={cache_config['s_maxage']}")

        if cache_config.get('must_revalidate'):
            cache_control_parts.append('must-revalidate')

        if cache_config.get('no_cache'):
            cache_control_parts.append('no-cache')

        if cache_config.get('no_store'):
            cache_control_parts.append('no-store')

        # Set Cache-Control header
        if cache_control_parts:
            response['Cache-Control'] = ', '.join(cache_control_parts)

        # Set ETag for better caching
        if hasattr(response, 'content') and response.content:
            import hashlib
            etag = hashlib.md5(response.content).hexdigest()
            response['ETag'] = f'"{etag}"'

        # Set Vary header for user-specific content
        if cache_config.get('private') or 'user' in request.path.lower():
            response['Vary'] = 'Authorization, Accept-Language'

    def _check_rate_limits(self, request: HttpRequest) -> Optional[JsonResponse]:
        """
        Check rate limits for the request
        """
        # Get rate limit configuration for this endpoint
        rate_config = self._get_rate_limit_config(request.path, request.method)
        if not rate_config:
            return None
        
        # Generate cache key for rate limiting
        cache_key = self._generate_rate_limit_key(request, request.path, request.method)
        
        # Check current request count
        current_count = cache.get(cache_key, 0)
        
        if current_count >= rate_config['requests']:
            # Rate limit exceeded
            self._log_rate_limit_exceeded(request, request.path)
            
            return JsonResponse({
                'error': 'Rate limit exceeded',
                'message': f'Too many requests. Limit: {rate_config["requests"]} per {rate_config["window"]} seconds',
                'retry_after': rate_config['window']
            }, status=429)
        
        # Increment request count
        cache.set(cache_key, current_count + 1, rate_config['window'])
        
        return None
    
    def _get_rate_limit_config(self, path: str, method: str) -> Optional[Dict]:
        """
        Get rate limit configuration for a specific endpoint and method
        """
        # Try exact path match first
        if path in COMMUNITY_RATE_LIMITS:
            config = COMMUNITY_RATE_LIMITS[path]
            if method in config:
                return config[method]
        
        # Try pattern matching for dynamic paths (e.g., /api/community/posts/123/like/)
        for pattern, config in COMMUNITY_RATE_LIMITS.items():
            if '*' in pattern:
                # Simple pattern matching - replace * with any characters
                import re
                regex_pattern = pattern.replace('*', r'[^/]+')
                if re.match(regex_pattern, path):
                    if method in config:
                        return config[method]
        
        return None
    
    def _generate_rate_limit_key(self, request: HttpRequest, path: str, method: str) -> str:
        """
        Generate cache key for rate limiting
        """
        # Use user ID if authenticated, otherwise use IP address
        if request.user.is_authenticated:
            identifier = f"user_{request.user.id}"
        else:
            identifier = f"ip_{self._get_client_ip(request)}"
        
        return f"rate_limit:community:{identifier}:{path}:{method}"
    
    def _get_client_ip(self, request: HttpRequest) -> str:
        """
        Get client IP address from request
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip or 'unknown'
    
    def _validate_request_content(self, request: HttpRequest) -> Optional[JsonResponse]:
        """
        Validate request content for security issues
        """
        # Only validate POST/PUT/PATCH requests with content
        if request.method not in ['POST', 'PUT', 'PATCH']:
            return None
        
        # Check content length
        content_length = request.META.get('CONTENT_LENGTH')
        if content_length:
            try:
                content_length = int(content_length)
                # Limit request size to 10MB
                if content_length > 10 * 1024 * 1024:
                    security_logger.log_suspicious_activity(
                        user_id=request.user.id if request.user.is_authenticated else None,
                        activity_type='large_request',
                        details={'content_length': content_length, 'path': request.path}
                    )
                    return JsonResponse({
                        'error': 'Request too large',
                        'message': 'Request size exceeds maximum allowed limit'
                    }, status=413)
            except ValueError:
                pass
        
        # Validate JSON content if present
        if request.content_type == 'application/json':
            try:
                if hasattr(request, 'body') and request.body:
                    json.loads(request.body)
            except json.JSONDecodeError:
                return JsonResponse({
                    'error': 'Invalid JSON',
                    'message': 'Request body contains invalid JSON'
                }, status=400)
        
        return None
    
    def _log_rate_limit_exceeded(self, request: HttpRequest, path: str):
        """
        Log rate limit exceeded events
        """
        user_id = request.user.id if request.user.is_authenticated else None
        ip_address = self._get_client_ip(request)
        
        security_logger.log_rate_limit_exceeded(
            user_id=user_id,
            endpoint=path,
            ip_address=ip_address
        )


class CommunityAuthenticationMiddleware(MiddlewareMixin):
    """
    Enhanced authentication middleware for community endpoints
    """
    
    def process_request(self, request: HttpRequest) -> Optional[HttpResponse]:
        """
        Process authentication for community endpoints
        """
        # Only apply to community API endpoints
        if not request.path.startswith('/api/community/'):
            return None
        
        # Check for suspicious authentication patterns
        self._check_suspicious_auth_patterns(request)
        
        return None
    
    def _check_suspicious_auth_patterns(self, request: HttpRequest):
        """
        Check for suspicious authentication patterns
        """
        # Check for multiple failed authentication attempts
        if hasattr(request, 'user') and not request.user.is_authenticated:
            auth_header = request.META.get('HTTP_AUTHORIZATION')
            if auth_header and auth_header.startswith('Bearer '):
                # Log failed JWT authentication attempt
                ip_address = self._get_client_ip(request)
                cache_key = f"failed_auth:{ip_address}"
                
                failed_attempts = cache.get(cache_key, 0)
                cache.set(cache_key, failed_attempts + 1, 3600)  # 1 hour window
                
                if failed_attempts > 10:  # More than 10 failed attempts in an hour
                    security_logger.log_suspicious_activity(
                        user_id=None,
                        activity_type='multiple_failed_auth',
                        details={'ip_address': ip_address, 'attempts': failed_attempts}
                    )
    
    def _get_client_ip(self, request: HttpRequest) -> str:
        """
        Get client IP address from request
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip or 'unknown'
