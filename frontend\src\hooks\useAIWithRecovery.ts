/**
 * 🎯 AI HOOK WITH RECOVERY
 * React hook for AI interactions with automatic error recovery and retry logic
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { unifiedAIService, AIResponse, AIError } from '../services/unifiedAIService';
import { useUnifiedRoles } from './useUnifiedRoles';

interface AIState {
  isLoading: boolean;
  response: AIResponse | null;
  error: AIError | null;
  retryCount: number;
  rateLimitInfo: {
    remaining: number;
    resetTime: number;
    limit: number;
  } | null;
}

interface UseAIOptions {
  maxRetries?: number;
  retryDelay?: number;
  autoRetry?: boolean;
  onSuccess?: (response: AIResponse) => void;
  onError?: (error: AIError) => void;
  onRateLimit?: (info: any) => void;
}

export function useAIWithRecovery(options: UseAIOptions = {}) {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    autoRetry = true,
    onSuccess,
    onError,
    onRateLimit
  } = options;

  const { primaryRole, user } = useUnifiedRoles();
  const [state, setState] = useState<AIState>({
    isLoading: false,
    response: null,
    error: null,
    retryCount: 0,
    rateLimitInfo: null
  });

  const retryTimeoutRef = useRef<NodeJS.Timeout>();
  const abortControllerRef = useRef<AbortController>();

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Update rate limit info
  useEffect(() => {
    if (user?.id) {
      const rateLimitInfo = unifiedAIService.getRateLimitInfo(user.id, primaryRole);
      setState(prev => ({ ...prev, rateLimitInfo }));
    }
  }, [user?.id, primaryRole]);

  const executeAIRequest = useCallback(async (
    requestFn: () => Promise<AIResponse>,
    retryCount: number = 0
  ): Promise<AIResponse> => {
    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = new AbortController();

    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null,
      retryCount
    }));

    try {
      const response = await requestFn();
      
      setState(prev => ({
        ...prev,
        isLoading: false,
        response,
        error: null,
        retryCount: 0
      }));

      // Update rate limit info after successful request
      if (user?.id) {
        const rateLimitInfo = unifiedAIService.getRateLimitInfo(user.id, primaryRole);
        setState(prev => ({ ...prev, rateLimitInfo }));
      }

      onSuccess?.(response);
      return response;

    } catch (error: any) {
      const aiError = error as AIError;
      
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: aiError,
        retryCount
      }));

      // Handle rate limiting
      if (aiError.code === 'RATE_LIMIT_EXCEEDED') {
        onRateLimit?.(aiError.details?.rateLimitInfo);
        
        if (autoRetry && aiError.retryAfter) {
          const retryDelay = aiError.retryAfter - Date.now();
          if (retryDelay > 0 && retryDelay < 300000) { // Max 5 minutes
            retryTimeoutRef.current = setTimeout(() => {
              executeAIRequest(requestFn, retryCount);
            }, retryDelay);
            return Promise.reject(aiError);
          }
        }
      }

      // Handle retryable errors
      if (aiError.retryable && autoRetry && retryCount < maxRetries) {
        const delay = retryDelay * Math.pow(2, retryCount); // Exponential backoff
        
        retryTimeoutRef.current = setTimeout(() => {
          executeAIRequest(requestFn, retryCount + 1);
        }, delay);
        
        return Promise.reject(aiError);
      }

      onError?.(aiError);
      throw aiError;
    }
  }, [maxRetries, retryDelay, autoRetry, onSuccess, onError, onRateLimit, user?.id, primaryRole]);

  // ========================================
  // AI REQUEST METHODS
  // ========================================

  const sendChatMessage = useCallback(async (
    message: string,
    context?: Record<string, any>
  ): Promise<AIResponse> => {
    return executeAIRequest(() => 
      unifiedAIService.sendChatMessage(message, context, user?.id, primaryRole)
    );
  }, [executeAIRequest, user?.id, primaryRole]);

  const generateBusinessPlan = useCallback(async (
    businessIdea: string,
    requirements: Record<string, any>
  ): Promise<AIResponse> => {
    return executeAIRequest(() => 
      unifiedAIService.generateBusinessPlan(businessIdea, requirements, user?.id, primaryRole)
    );
  }, [executeAIRequest, user?.id, primaryRole]);

  const analyzeBusinessIdea = useCallback(async (
    businessIdea: string
  ): Promise<AIResponse> => {
    return executeAIRequest(() => 
      unifiedAIService.analyzeBusinessIdea(businessIdea, user?.id, primaryRole)
    );
  }, [executeAIRequest, user?.id, primaryRole]);

  const getRecommendations = useCallback(async (
    type: 'business' | 'mentor' | 'investor' | 'template',
    context: Record<string, any>
  ): Promise<AIResponse> => {
    return executeAIRequest(() => 
      unifiedAIService.getRecommendations(type, context, user?.id, primaryRole)
    );
  }, [executeAIRequest, user?.id, primaryRole]);

  // ========================================
  // UTILITY METHODS
  // ========================================

  const retry = useCallback(() => {
    if (state.error && state.error.retryable) {
      // Retry the last request - this would need to store the last request
      console.log('Manual retry requested');
    }
  }, [state.error]);

  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }
    setState(prev => ({
      ...prev,
      isLoading: false,
      error: null
    }));
  }, []);

  const reset = useCallback(() => {
    cancel();
    setState({
      isLoading: false,
      response: null,
      error: null,
      retryCount: 0,
      rateLimitInfo: null
    });
  }, [cancel]);

  const checkHealth = useCallback(async () => {
    try {
      const health = await unifiedAIService.healthCheck();
      return health;
    } catch (error) {
      return {
        status: 'unhealthy' as const,
        details: { error: 'Health check failed' }
      };
    }
  }, []);

  return {
    // State
    isLoading: state.isLoading,
    response: state.response,
    error: state.error,
    retryCount: state.retryCount,
    rateLimitInfo: state.rateLimitInfo,
    
    // AI Methods
    sendChatMessage,
    generateBusinessPlan,
    analyzeBusinessIdea,
    getRecommendations,
    
    // Utility Methods
    retry,
    cancel,
    reset,
    checkHealth,
    
    // Status Checks
    canMakeRequest: state.rateLimitInfo ? state.rateLimitInfo.remaining > 0 : true,
    hasError: Boolean(state.error),
    isRetryable: Boolean(state.error?.retryable),
    isRateLimited: state.error?.code === 'RATE_LIMIT_EXCEEDED'
  };
}

// ========================================
// SPECIALIZED HOOKS
// ========================================

export function useAIChat(options?: UseAIOptions) {
  const ai = useAIWithRecovery(options);
  
  const [messages, setMessages] = useState<Array<{
    id: string;
    content: string;
    role: 'user' | 'assistant';
    timestamp: string;
  }>>([]);

  const sendMessage = useCallback(async (message: string, context?: Record<string, any>) => {
    const userMessage = {
      id: `msg_${Date.now()}_user`,
      content: message,
      role: 'user' as const,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);

    try {
      const response = await ai.sendChatMessage(message, context);
      
      const assistantMessage = {
        id: `msg_${Date.now()}_assistant`,
        content: response.content,
        role: 'assistant' as const,
        timestamp: response.timestamp
      };

      setMessages(prev => [...prev, assistantMessage]);
      return response;
    } catch (error) {
      // Error is already handled by the base hook
      throw error;
    }
  }, [ai]);

  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  return {
    ...ai,
    messages,
    sendMessage,
    clearMessages
  };
}

export function useBusinessPlanAI(options?: UseAIOptions) {
  const ai = useAIWithRecovery(options);
  
  const [generatedPlans, setGeneratedPlans] = useState<AIResponse[]>([]);

  const generatePlan = useCallback(async (
    businessIdea: string,
    requirements: Record<string, any>
  ) => {
    try {
      const response = await ai.generateBusinessPlan(businessIdea, requirements);
      setGeneratedPlans(prev => [...prev, response]);
      return response;
    } catch (error) {
      throw error;
    }
  }, [ai]);

  const clearPlans = useCallback(() => {
    setGeneratedPlans([]);
  }, []);

  return {
    ...ai,
    generatedPlans,
    generatePlan,
    clearPlans
  };
}

export default useAIWithRecovery;
