import { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useToast } from './useToast';
import { useAuth } from './useAuth';

export interface UseCommunityModalsReturn {
  // Modal states
  showCreateModal: boolean;
  showAdvancedSearch: boolean;
  showUserDiscovery: boolean;
  showPostAnalytics: string | null;
  
  // Modal actions
  openCreateModal: () => void;
  closeCreateModal: () => void;
  openAdvancedSearch: () => void;
  closeAdvancedSearch: () => void;
  openUserDiscovery: () => void;
  closeUserDiscovery: () => void;
  openPostAnalytics: (postId: string) => void;
  closePostAnalytics: () => void;
  
  // Modal handlers
  handleCreatePostClick: () => void;
  handleAdvancedSearchResults: (results: any[]) => void;
}

export const useCommunityModals = (
  onSearchResults?: (results: any[]) => void,
  onSetSearchQuery?: (query: string) => void
): UseCommunityModalsReturn => {
  const { t } = useTranslation();
  const { isAuthenticated } = useAuth();
  const { showInfo } = useToast();
  
  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [showUserDiscovery, setShowUserDiscovery] = useState(false);
  const [showPostAnalytics, setShowPostAnalytics] = useState<string | null>(null);

  // Create modal actions
  const openCreateModal = useCallback(() => {
    setShowCreateModal(true);
  }, []);

  const closeCreateModal = useCallback(() => {
    setShowCreateModal(false);
  }, []);

  // Advanced search modal actions
  const openAdvancedSearch = useCallback(() => {
    setShowAdvancedSearch(true);
  }, []);

  const closeAdvancedSearch = useCallback(() => {
    setShowAdvancedSearch(false);
  }, []);

  // User discovery modal actions
  const openUserDiscovery = useCallback(() => {
    setShowUserDiscovery(true);
  }, []);

  const closeUserDiscovery = useCallback(() => {
    setShowUserDiscovery(false);
  }, []);

  // Post analytics modal actions
  const openPostAnalytics = useCallback((postId: string) => {
    setShowPostAnalytics(postId);
  }, []);

  const closePostAnalytics = useCallback(() => {
    setShowPostAnalytics(null);
  }, []);

  // Handle create post click with authentication check
  const handleCreatePostClick = useCallback(() => {
    if (!isAuthenticated) {
      showInfo(t('community.messages.loginToCreate'));
      return;
    }
    openCreateModal();
  }, [isAuthenticated, showInfo, t, openCreateModal]);

  // Handle advanced search results
  const handleAdvancedSearchResults = useCallback((results: any[]) => {
    if (onSearchResults) {
      onSearchResults(results);
    }
    if (onSetSearchQuery) {
      onSetSearchQuery('advanced search results');
    }
    closeAdvancedSearch();
  }, [onSearchResults, onSetSearchQuery, closeAdvancedSearch]);

  return {
    // Modal states
    showCreateModal,
    showAdvancedSearch,
    showUserDiscovery,
    showPostAnalytics,
    
    // Modal actions
    openCreateModal,
    closeCreateModal,
    openAdvancedSearch,
    closeAdvancedSearch,
    openUserDiscovery,
    closeUserDiscovery,
    openPostAnalytics,
    closePostAnalytics,
    
    // Modal handlers
    handleCreatePostClick,
    handleAdvancedSearchResults,
  };
};
