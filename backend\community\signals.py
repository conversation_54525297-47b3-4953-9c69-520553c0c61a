"""
Community Signals
Automatic updates for community features
"""

from django.db.models.signals import post_save, post_delete, m2m_changed
from django.dispatch import receiver
from django.contrib.auth.models import User
from .models import CommunityPost, CommunityComment, UserActivity, OnlineUser


@receiver(post_save, sender=CommunityComment)
def update_post_comment_count(sender, instance, created, **kwargs):
    """Update post comment count when comment is created"""
    if created:
        post = instance.post
        post.comments_count = post.comments.count()
        post.save(update_fields=['comments_count'])


@receiver(post_delete, sender=CommunityComment)
def update_post_comment_count_on_delete(sender, instance, **kwargs):
    """Update post comment count when comment is deleted"""
    post = instance.post
    post.comments_count = post.comments.count()
    post.save(update_fields=['comments_count'])


@receiver(post_save, sender=User)
def update_user_online_status(sender, instance, created, **kwargs):
    """Create or update user online status"""
    if created:
        OnlineUser.objects.get_or_create(user=instance)


@receiver(m2m_changed, sender=CommunityPost.likes.through)
def handle_post_like_activity(sender, instance, action, pk_set, **kwargs):
    """Create activity when post is liked"""
    if action == 'post_add' and pk_set:
        for user_id in pk_set:
            try:
                user = User.objects.get(id=user_id)
                UserActivity.objects.create(
                    user=user,
                    activity_type='post_liked',
                    content_type='post',
                    object_id=instance.id
                )
            except User.DoesNotExist:
                pass
