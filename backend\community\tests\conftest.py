"""
Pytest Configuration for Community Tests
Fixtures and test setup for community app testing
"""

import pytest
import os
import django
from django.conf import settings
from django.test.utils import get_runner
from django.contrib.auth.models import User
from django.core.cache import cache
from rest_framework.test import APIClient

# Configure Django settings for testing
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')

def pytest_configure():
    """Configure Django for pytest"""
    if not settings.configured:
        django.setup()


@pytest.fixture(scope='session')
def django_db_setup():
    """Setup test database"""
    settings.DATABASES['default'] = {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }


@pytest.fixture(autouse=True)
def enable_db_access_for_all_tests(db):
    """Enable database access for all tests"""
    pass


@pytest.fixture(autouse=True)
def clear_cache():
    """Clear cache before each test"""
    cache.clear()
    yield
    cache.clear()


@pytest.fixture
def api_client():
    """Provide API client for testing"""
    return APIClient()


@pytest.fixture
def user():
    """Create a test user"""
    return User.objects.create_user(
        username='testuser',
        email='<EMAIL>',
        password='testpass123',
        first_name='Test',
        last_name='User'
    )


@pytest.fixture
def inactive_user():
    """Create an inactive test user"""
    return User.objects.create_user(
        username='inactiveuser',
        email='<EMAIL>',
        password='testpass123',
        is_active=False
    )


@pytest.fixture
def admin_user():
    """Create an admin user"""
    return User.objects.create_user(
        username='admin',
        email='<EMAIL>',
        password='adminpass123',
        is_staff=True,
        is_superuser=True
    )


@pytest.fixture
def other_user():
    """Create another test user"""
    return User.objects.create_user(
        username='otheruser',
        email='<EMAIL>',
        password='testpass123',
        first_name='Other',
        last_name='User'
    )


@pytest.fixture
def authenticated_client(api_client, user):
    """Provide authenticated API client"""
    api_client.force_authenticate(user=user)
    return api_client


@pytest.fixture
def admin_client(api_client, admin_user):
    """Provide admin authenticated API client"""
    api_client.force_authenticate(user=admin_user)
    return api_client


@pytest.fixture
def sample_post_data():
    """Provide sample post data for testing"""
    return {
        'title': 'Test Post Title',
        'content': 'This is a test post content for testing purposes.',
        'visibility': 'public',
        'allow_comments': True,
        'tag_names': ['test', 'django', 'python']
    }


@pytest.fixture
def sample_comment_data():
    """Provide sample comment data for testing"""
    return {
        'content': 'This is a test comment for testing purposes.'
    }


@pytest.fixture
def malicious_post_data():
    """Provide malicious post data for security testing"""
    return {
        'title': '<script>alert("xss")</script>Malicious Title',
        'content': '<script>alert("xss")</script><iframe src="evil.com"></iframe>Malicious content',
        'visibility': 'public',
        'allow_comments': True
    }


@pytest.fixture
def malicious_comment_data():
    """Provide malicious comment data for security testing"""
    return {
        'content': '<script>alert("xss")</script>Malicious comment'
    }


@pytest.fixture
def long_content_data():
    """Provide data with content exceeding limits"""
    return {
        'title': 'x' * 201,  # Exceeds title limit
        'content': 'x' * 5001,  # Exceeds content limit
        'visibility': 'public',
        'allow_comments': True
    }


@pytest.fixture
def community_post(user):
    """Create a sample community post"""
    from community.models import CommunityPost
    return CommunityPost.objects.create(
        title='Sample Post',
        content='This is a sample post for testing.',
        author=user,
        visibility='public',
        allow_comments=True
    )


@pytest.fixture
def private_post(user):
    """Create a private community post"""
    from community.models import CommunityPost
    return CommunityPost.objects.create(
        title='Private Post',
        content='This is a private post for testing.',
        author=user,
        visibility='private',
        allow_comments=True
    )


@pytest.fixture
def community_comment(community_post, other_user):
    """Create a sample community comment"""
    from community.models import CommunityComment
    return CommunityComment.objects.create(
        content='This is a sample comment for testing.',
        author=other_user,
        post=community_post
    )


@pytest.fixture
def community_tag():
    """Create a sample community tag"""
    from api.models import Tag
    return Tag.objects.create(name='test-tag')


@pytest.fixture
def community_like(community_post, other_user):
    """Create a sample community like"""
    # Use the many-to-many relationship instead of separate model
    community_post.likes.add(other_user)
    return community_post


@pytest.fixture
def mock_request(user):
    """Create a mock request object"""
    from django.test import RequestFactory
    factory = RequestFactory()
    request = factory.get('/')
    request.user = user
    return request


@pytest.fixture
def security_test_data():
    """Provide various security test data"""
    return {
        'xss_script': '<script>alert("xss")</script>',
        'iframe_embed': '<iframe src="evil.com"></iframe>',
        'javascript_url': 'javascript:alert("xss")',
        'onclick_handler': '<div onclick="alert(\'xss\')">Click me</div>',
        'sql_injection': "'; DROP TABLE users; --",
        'long_string': 'x' * 10000,
        'unicode_attack': '\u003cscript\u003ealert("xss")\u003c/script\u003e',
        'html_entities': '&lt;script&gt;alert("xss")&lt;/script&gt;',
    }


@pytest.fixture
def rate_limit_test_data():
    """Provide data for rate limiting tests"""
    return {
        'endpoints': [
            '/api/community/posts/',
            '/api/community/comments/',
            '/api/community/posts/1/like/',
            '/api/community/posts/1/add_comment/',
        ],
        'methods': ['GET', 'POST'],
        'limits': {
            'posts_get': 100,
            'posts_post': 10,
            'comments_get': 200,
            'comments_post': 20,
            'likes_post': 50,
        }
    }


# Pytest markers for organizing tests
pytest_plugins = []

def pytest_collection_modifyitems(config, items):
    """Add markers to tests based on their location"""
    for item in items:
        # Add security marker to security tests
        if 'security' in item.nodeid:
            item.add_marker(pytest.mark.security)
        
        # Add integration marker to integration tests
        if 'integration' in item.nodeid:
            item.add_marker(pytest.mark.integration)
        
        # Add slow marker to tests that might be slow
        if any(keyword in item.nodeid for keyword in ['middleware', 'rate_limit', 'comprehensive']):
            item.add_marker(pytest.mark.slow)


# Custom pytest markers
def pytest_configure(config):
    """Register custom markers"""
    config.addinivalue_line(
        "markers", "security: mark test as security-related"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as potentially slow"
    )
    config.addinivalue_line(
        "markers", "unit: mark test as unit test"
    )
    config.addinivalue_line(
        "markers", "api: mark test as API test"
    )


# Performance monitoring for tests
@pytest.fixture(autouse=True)
def monitor_test_performance(request):
    """Monitor test performance and log slow tests"""
    import time
    start_time = time.time()
    yield
    duration = time.time() - start_time
    
    if duration > 1.0:  # Log tests taking more than 1 second
        print(f"\nSlow test detected: {request.node.nodeid} took {duration:.2f}s")


# Database state verification
@pytest.fixture
def verify_db_state():
    """Verify database state after tests"""
    from community.models import CommunityPost, CommunityComment, PostSave, UserFollow
    from api.models import Tag

    def _verify():
        return {
            'posts': CommunityPost.objects.count(),
            'comments': CommunityComment.objects.count(),
            'tags': Tag.objects.count(),
            'saves': PostSave.objects.count(),
            'follows': UserFollow.objects.count(),
        }

    return _verify
