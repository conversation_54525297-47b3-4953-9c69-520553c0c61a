import React, { memo, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Plus, TrendingUp, Users, BarChart3 } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { useAuth } from '../../hooks/useAuth';
import { CommunityStats, UserProfile, communityApi } from '../../services/communityApi';

interface CommunitySidebarProps {
  stats: CommunityStats | null;
  onCreatePost: () => void;
  isAuthenticated: boolean;
}

const CommunitySidebar: React.FC<CommunitySidebarProps> = memo(({
  stats,
  onCreatePost,
  isAuthenticated
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAuth(); // Get user info from Redux

  // State for API data
  const [trendingTopics, setTrendingTopics] = useState<Array<{ tag: string; count: number; trending: boolean }>>([]);
  const [suggestedUsers, setSuggestedUsers] = useState<UserProfile[]>([]);
  const [loadingTrending, setLoadingTrending] = useState(true);
  const [loadingUsers, setLoadingUsers] = useState(true);

  // Fetch trending hashtags from API
  useEffect(() => {
    const fetchTrendingTopics = async () => {
      try {
        setLoadingTrending(true);
        const trending = await communityApi.getTrendingHashtags();
        setTrendingTopics(trending.slice(0, 5)); // Show top 5
      } catch (error) {
        console.error('Failed to fetch trending topics:', error);
        // Fallback to empty array on error
        setTrendingTopics([]);
      } finally {
        setLoadingTrending(false);
      }
    };

    fetchTrendingTopics();
  }, []);

  // Fetch suggested users from API - only if authenticated
  useEffect(() => {
    const fetchSuggestedUsers = async () => {
      try {
        setLoadingUsers(true);
        const users = await communityApi.getUserRecommendations({
          activity_level: 'high',
          verification_status: 'all'
        });
        setSuggestedUsers(users.slice(0, 3)); // Show top 3
      } catch (error) {
        console.error('❌ Failed to fetch suggested users:', error);
        // Set empty array on error - no fallback mock data
        setSuggestedUsers([]);
      } finally {
        setLoadingUsers(false);
      }
    };

    // Only fetch if authenticated, otherwise show empty state
    if (isAuthenticated) {
      fetchSuggestedUsers();
    } else {
      setLoadingUsers(false);
      setSuggestedUsers([]);
    }
  }, [isAuthenticated]); // Re-run when auth state changes

  return (
    <div className={`lg:col-span-1 order-2 ${isRTL ? 'lg:order-2' : 'lg:order-1'}`}>
      <div className="space-y-6">
        {/* Trending Topics */}
        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-purple-400" />
            {t('community.sidebar.trendingTopics')}
          </h3>
          <div className="space-y-3 min-h-[140px]">
            {loadingTrending ? (
              <div className="space-y-3">
                {[...Array(5)].map((_, index) => (
                  <div key={index} className="flex items-center justify-between h-6">
                    <div className="h-4 bg-white/10 rounded animate-pulse flex-1 mr-3"></div>
                    <div className="h-6 w-8 bg-white/10 rounded-full animate-pulse"></div>
                  </div>
                ))}
              </div>
            ) : trendingTopics.length > 0 ? (
              trendingTopics.map((topic, index) => (
                <div key={index} className="flex items-center justify-between h-6">
                  <span className="text-gray-300 hover:text-white cursor-pointer transition-colors">
                    #{topic.tag}
                  </span>
                  <span className="text-xs text-gray-500 bg-white/10 px-2 py-1 rounded-full">
                    {topic.count}
                  </span>
                </div>
              ))
            ) : (
              <div className="text-gray-400 text-sm text-center py-4 flex items-center justify-center h-full">
                {t('community.sidebar.noTrendingTopics', 'No trending topics yet')}
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4">
            {t('community.sidebar.quickActions')}
          </h3>
          <div className="space-y-2">
            <button
              onClick={() => {
                if (!isAuthenticated) {
                  // Show login message - this will be handled by the parent component
                  return;
                }
                onCreatePost();
              }}
              className="w-full flex items-center gap-3 px-4 py-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-all"
            >
              <Plus className="w-4 h-4" />
              <span className="text-sm">{t('community.actions.newPost')}</span>
            </button>
          </div>
        </div>

        {/* Community Stats */}
        {stats && (
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-blue-400" />
              {t('community.sidebar.communityStats')}
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-sm">{t('community.stats.totalPosts')}</span>
                <span className="text-white font-medium">{stats.total_posts?.toLocaleString() || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-sm">{t('community.stats.totalUsers')}</span>
                <span className="text-white font-medium">{stats.total_users?.toLocaleString() || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-sm">{t('community.stats.postsToday')}</span>
                <span className="text-white font-medium">{stats.posts_today?.toLocaleString() || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-sm">{t('community.stats.activeUsers')}</span>
                <span className="text-white font-medium">{stats.active_users?.toLocaleString() || 0}</span>
              </div>
            </div>
          </div>
        )}

        {/* Suggested Users */}
        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Users className="w-5 h-5 text-green-400" />
            {t('community.sidebar.suggestedUsers')}
          </h3>
          <div className="space-y-3 min-h-[180px]">
            {loadingUsers ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, index) => (
                  <div key={index} className="flex items-center gap-3 h-12">
                    <div className="w-8 h-8 bg-white/10 rounded-full animate-pulse flex-shrink-0"></div>
                    <div className="flex-1 min-w-0">
                      <div className="h-4 bg-white/10 rounded animate-pulse mb-1"></div>
                      <div className="h-3 bg-white/10 rounded animate-pulse w-2/3"></div>
                    </div>
                    <div className="h-6 w-16 bg-white/10 rounded-full animate-pulse flex-shrink-0"></div>
                  </div>
                ))}
              </div>
            ) : suggestedUsers.length > 0 ? (
              suggestedUsers.map((user, index) => (
                <div key={user.id || index} className="flex items-center gap-3 h-12 hover:bg-gray-800/30 rounded-lg p-2 transition-colors">
                  <div
                    className="w-8 h-8 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-white font-medium text-sm flex-shrink-0 cursor-pointer hover:ring-2 hover:ring-purple-500 transition-all"
                    onClick={() => {
                      // TODO: Navigate to user profile
                    }}
                    title={isRTL ? `عرض ملف ${user.full_name}` : `View ${user.full_name}'s profile`}
                  >
                    {user.avatar ? (
                      <img src={user.avatar} alt={user.full_name} className="w-full h-full rounded-full object-cover" />
                    ) : (
                      user.first_name?.[0] || user.username?.[0] || 'U'
                    )}
                  </div>
                  <div
                    className="flex-1 min-w-0 cursor-pointer"
                    onClick={() => {
                      // TODO: Navigate to user profile
                    }}
                  >
                    <div className="text-sm font-medium text-white truncate hover:text-purple-300 transition-colors">
                      {user.full_name || user.first_name || user.username}
                    </div>
                    <div className="text-xs text-gray-400 truncate">
                      {user.user_role || 'Member'}
                    </div>
                    {user.location && (
                      <div className="text-xs text-gray-500 truncate">
                        📍 {user.location}
                      </div>
                    )}
                  </div>
                  <button
                    className="text-xs bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded-full transition-colors flex-shrink-0"
                    onClick={async (event) => {
                      try {
                        // Call follow API
                        await communityApi.followUser(user.id);

                        // Show success message
                        const button = event.target as HTMLButtonElement;
                        const originalText = button.textContent;
                        button.textContent = isRTL ? 'تمت المتابعة!' : 'Following!';
                        button.disabled = true;
                        button.className = 'text-xs bg-green-600 text-white px-3 py-1 rounded-full transition-colors flex-shrink-0 cursor-not-allowed';

                        // Reset after 2 seconds
                        setTimeout(() => {
                          button.textContent = isRTL ? 'تتم المتابعة' : 'Following';
                          button.className = 'text-xs bg-gray-600 text-gray-300 px-3 py-1 rounded-full transition-colors flex-shrink-0 cursor-not-allowed';
                        }, 2000);

                      } catch (error) {
                        console.error('❌ Failed to follow user:', error);
                        // Show error message
                        const button = event.target as HTMLButtonElement;
                        const originalText = button.textContent;
                        button.textContent = isRTL ? 'خطأ!' : 'Error!';
                        button.className = 'text-xs bg-red-600 text-white px-3 py-1 rounded-full transition-colors flex-shrink-0';

                        // Reset after 2 seconds
                        setTimeout(() => {
                          button.textContent = originalText;
                          button.className = 'text-xs bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded-full transition-colors flex-shrink-0';
                        }, 2000);
                      }
                    }}
                  >
                    {t('community.actions.follow')}
                  </button>
                </div>
              ))
            ) : (
              <div className="text-gray-400 text-sm text-center py-4 flex items-center justify-center h-full">
                {t('community.sidebar.noSuggestedUsers', 'No suggested users yet')}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});

CommunitySidebar.displayName = 'CommunitySidebar';

export default CommunitySidebar;
