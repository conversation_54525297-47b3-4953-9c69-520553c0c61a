"""
OPTIMIZED INCUBATOR VIEWS
High-performance views for business ideas and plans
"""

from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Count, Prefetch, Q, Avg
from django.core.cache import cache
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from .models import BusinessIdea, ProgressUpdate
from .models_business_plan import BusinessPlan
from .serializers import BusinessIdeaSerializer, ProgressUpdateSerializer
from .serializers_business_plan import BusinessPlanSerializer


class OptimizedBusinessIdeaViewSet(viewsets.ModelViewSet):
    """
    Optimized Business Ideas ViewSet
    Eliminates N+1 queries and implements caching
    """
    serializer_class = BusinessIdeaSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    
    def get_queryset(self):
        """
        Optimized queryset that eliminates N+1 queries
        """
        queryset = BusinessIdea.objects.select_related(
            'owner',  # Load owner in same query
            'owner__userprofile',  # Load owner profile
            'moderated_by',  # Load moderator info
        ).prefetch_related(
            # Efficiently load progress updates
            Prefetch(
                'progress_updates',
                queryset=ProgressUpdate.objects.select_related('created_by').order_by('-created_at')
            ),
            # Efficiently load collaborators
            'collaborators',
            # Efficiently load tags
            'tags',
            # Load business plans
            Prefetch(
                'business_plans',
                queryset=BusinessPlan.objects.select_related('owner')
            )
        ).annotate(
            # Pre-calculate counts
            progress_count=Count('progress_updates', distinct=True),
            collaborator_count=Count('collaborators', distinct=True),
            business_plan_count=Count('business_plans', distinct=True)
        ).order_by('-created_at')
        
        # Apply filters
        stage = self.request.query_params.get('stage', None)
        if stage:
            queryset = queryset.filter(current_stage=stage)
        
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(moderation_status=status_filter)
        
        owner = self.request.query_params.get('owner', None)
        if owner:
            queryset = queryset.filter(owner__username=owner)
        
        return queryset
    
    def list(self, request, *args, **kwargs):
        """
        Cached list view for better performance
        """
        # Create cache key based on query parameters
        cache_key = f"business_ideas_{request.GET.urlencode()}"
        
        # Try to get from cache first (cache for 10 minutes)
        cached_data = cache.get(cache_key)
        if cached_data:
            return Response(cached_data)
        
        # If not in cache, get from database
        queryset = self.filter_queryset(self.get_queryset())
        
        # Limit to reasonable number for performance
        page_size = min(int(request.query_params.get('page_size', 20)), 50)
        queryset = queryset[:page_size]
        
        serializer = self.get_serializer(queryset, many=True)
        
        # Cache the result
        cache.set(cache_key, serializer.data, 600)  # 10 minutes
        
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """
        Optimized stats endpoint
        """
        cache_key = "business_ideas_stats"
        cached_stats = cache.get(cache_key)
        
        if cached_stats:
            return Response(cached_stats)
        
        # Use efficient aggregation
        stats = BusinessIdea.objects.aggregate(
            total_ideas=Count('id'),
            approved_ideas=Count('id', filter=Q(moderation_status='approved')),
            pending_ideas=Count('id', filter=Q(moderation_status='pending')),
            concept_stage=Count('id', filter=Q(current_stage='concept')),
            validation_stage=Count('id', filter=Q(current_stage='validation')),
            development_stage=Count('id', filter=Q(current_stage='development')),
            scaling_stage=Count('id', filter=Q(current_stage='scaling')),
            established_stage=Count('id', filter=Q(current_stage='established'))
        )
        
        # Add progress update stats
        progress_stats = ProgressUpdate.objects.aggregate(
            total_updates=Count('id'),
            recent_updates=Count('id', filter=Q(created_at__gte=timezone.now() - timedelta(days=7)))
        )
        
        stats.update(progress_stats)
        
        # Cache for 15 minutes
        cache.set(cache_key, stats, 900)
        
        return Response(stats)
    
    @action(detail=True, methods=['get'])
    def progress(self, request, pk=None):
        """
        Get progress updates for a business idea
        """
        business_idea = self.get_object()
        cache_key = f"business_idea_{pk}_progress"
        
        cached_progress = cache.get(cache_key)
        if cached_progress:
            return Response(cached_progress)
        
        # Get progress updates efficiently
        progress_updates = ProgressUpdate.objects.filter(
            business_idea=business_idea
        ).select_related('created_by').order_by('-created_at')[:10]
        
        serializer = ProgressUpdateSerializer(progress_updates, many=True)
        
        # Cache for 5 minutes
        cache.set(cache_key, serializer.data, 300)
        
        return Response(serializer.data)


class OptimizedBusinessPlanViewSet(viewsets.ModelViewSet):
    """
    Optimized Business Plans ViewSet
    """
    serializer_class = BusinessPlanSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    
    def get_queryset(self):
        """
        Optimized queryset for business plans
        """
        return BusinessPlan.objects.select_related(
            'owner',
            'owner__userprofile',
            'business_idea',
            'template'
        ).prefetch_related(
            'sections',
            'collaborations__collaborator'
        ).annotate(
            section_count=Count('sections', distinct=True),
            collaboration_count=Count('collaborations', distinct=True)
        ).order_by('-created_at')
    
    def list(self, request, *args, **kwargs):
        """
        Cached list view
        """
        cache_key = f"business_plans_{request.GET.urlencode()}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return Response(cached_data)
        
        queryset = self.filter_queryset(self.get_queryset())
        
        # Apply filters
        status_filter = request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        business_idea = request.query_params.get('business_idea', None)
        if business_idea:
            queryset = queryset.filter(business_idea_id=business_idea)
        
        # Limit results
        page_size = min(int(request.query_params.get('page_size', 20)), 50)
        queryset = queryset[:page_size]
        
        serializer = self.get_serializer(queryset, many=True)
        
        # Cache for 10 minutes
        cache.set(cache_key, serializer.data, 600)
        
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """
        Business plan statistics
        """
        cache_key = "business_plans_stats"
        cached_stats = cache.get(cache_key)
        
        if cached_stats:
            return Response(cached_stats)
        
        stats = BusinessPlan.objects.aggregate(
            total_plans=Count('id'),
            draft_plans=Count('id', filter=Q(status='draft')),
            review_plans=Count('id', filter=Q(status='review')),
            approved_plans=Count('id', filter=Q(status='approved')),
            published_plans=Count('id', filter=Q(status='published')),
            avg_completion=Avg('completion_percentage')
        )
        
        # Cache for 15 minutes
        cache.set(cache_key, stats, 900)
        
        return Response(stats)


class OptimizedProgressUpdateViewSet(viewsets.ModelViewSet):
    """
    Optimized Progress Updates ViewSet
    """
    serializer_class = ProgressUpdateSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    
    def get_queryset(self):
        """
        Optimized queryset for progress updates
        """
        return ProgressUpdate.objects.select_related(
            'business_idea',
            'business_idea__owner',
            'created_by'
        ).order_by('-created_at')
    
    def list(self, request, *args, **kwargs):
        """
        List progress updates with caching
        """
        business_idea_id = request.query_params.get('business_idea', None)
        cache_key = f"progress_updates_{business_idea_id or 'all'}"
        
        cached_data = cache.get(cache_key)
        if cached_data:
            return Response(cached_data)
        
        queryset = self.get_queryset()
        
        if business_idea_id:
            queryset = queryset.filter(business_idea_id=business_idea_id)
        
        # Limit results
        page_size = min(int(request.query_params.get('page_size', 20)), 50)
        queryset = queryset[:page_size]
        
        serializer = self.get_serializer(queryset, many=True)
        
        # Cache for 5 minutes
        cache.set(cache_key, serializer.data, 300)
        
        return Response(serializer.data)


# Cache management utilities
def invalidate_incubator_caches():
    """
    Invalidate all incubator-related caches
    """
    cache.delete_many([
        "business_ideas_stats",
        "business_plans_stats",
    ])
    
    # Delete pattern-based cache keys
    try:
        if hasattr(cache, 'delete_pattern'):
            cache.delete_pattern("business_ideas_*")
            cache.delete_pattern("business_plans_*")
            cache.delete_pattern("progress_updates_*")
            cache.delete_pattern("business_idea_*_progress")
    except:
        pass


def warm_incubator_caches():
    """
    Pre-warm incubator caches
    """
    from django.test import RequestFactory
    
    factory = RequestFactory()
    
    # Warm up business ideas cache
    request = factory.get('/api/incubator/business-ideas/')
    viewset = OptimizedBusinessIdeaViewSet()
    viewset.request = request
    viewset.list(request)
    
    # Warm up stats caches
    viewset.stats(request)
    
    print("[CACHE] Incubator caches warmed up")
