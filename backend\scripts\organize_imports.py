#!/usr/bin/env python3
"""
Script to organize imports in Python files according to PEP 8
"""

import os
import re
import sys
from pathlib import Path


def organize_imports_in_file(file_path):
    """Organize imports in a single Python file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    
    # Find import section
    import_start = None
    import_end = None
    
    for i, line in enumerate(lines):
        stripped = line.strip()
        if stripped.startswith(('import ', 'from ')) and import_start is None:
            import_start = i
        elif import_start is not None and not stripped.startswith(('import ', 'from ')) and stripped and not stripped.startswith('#'):
            import_end = i
            break
    
    if import_start is None:
        return False  # No imports found
    
    if import_end is None:
        import_end = len(lines)
    
    # Extract imports
    import_lines = lines[import_start:import_end]
    
    # Categorize imports
    stdlib_imports = []
    third_party_imports = []
    local_imports = []
    
    stdlib_modules = {
        'os', 'sys', 'json', 'datetime', 'time', 'logging', 'typing', 'functools',
        'collections', 'itertools', 'hashlib', 'uuid', 're', 'math', 'random',
        'pathlib', 'urllib', 'http', 'email', 'html', 'xml', 'csv', 'sqlite3'
    }
    
    for line in import_lines:
        stripped = line.strip()
        if not stripped or stripped.startswith('#'):
            continue
        
        # Extract module name
        if stripped.startswith('from '):
            module = stripped.split()[1].split('.')[0]
        elif stripped.startswith('import '):
            module = stripped.split()[1].split('.')[0].split(',')[0]
        else:
            continue
        
        if module in stdlib_modules:
            stdlib_imports.append(line)
        elif module.startswith('.') or module in ['community', 'users', 'core']:
            local_imports.append(line)
        else:
            third_party_imports.append(line)
    
    # Sort imports within each category
    stdlib_imports.sort(key=lambda x: x.strip())
    third_party_imports.sort(key=lambda x: x.strip())
    local_imports.sort(key=lambda x: x.strip())
    
    # Reconstruct file
    new_lines = lines[:import_start]
    
    if stdlib_imports:
        new_lines.extend(stdlib_imports)
        new_lines.append('')
    
    if third_party_imports:
        new_lines.extend(third_party_imports)
        new_lines.append('')
    
    if local_imports:
        new_lines.extend(local_imports)
        new_lines.append('')
    
    new_lines.extend(lines[import_end:])
    
    # Remove extra blank lines
    while len(new_lines) > 1 and new_lines[-1] == '' and new_lines[-2] == '':
        new_lines.pop()
    
    new_content = '\n'.join(new_lines)
    
    if new_content != content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        return True
    
    return False


def main():
    """Main function to organize imports in community app"""
    if len(sys.argv) > 1:
        target_dir = sys.argv[1]
    else:
        # Default to community app
        script_dir = Path(__file__).parent
        target_dir = script_dir.parent / 'community'
    
    target_path = Path(target_dir)
    if not target_path.exists():
        print(f"Directory {target_path} does not exist")
        return
    
    files_modified = 0
    
    for py_file in target_path.rglob('*.py'):
        if organize_imports_in_file(py_file):
            print(f"Organized imports in {py_file}")
            files_modified += 1
    
    print(f"\nOrganized imports in {files_modified} files")


if __name__ == '__main__':
    main()
