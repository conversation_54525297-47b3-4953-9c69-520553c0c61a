/**
 * Mock Data Audit Utility
 * Temporary utility for tracking mock data usage during development
 * TODO: Remove this file when all mock data is replaced with real API calls
 */

export function logMockDataUsage(component: string, dataType: string, details?: any): void {
  // In production, this should be removed entirely
  if (process.env.NODE_ENV === 'development') {
    console.warn(`🚨 Mock Data Usage: ${component} is using mock ${dataType}`, details);
  }
}

export function trackMockDataUsage(component: string, action: string): void {
  // Placeholder for tracking mock data usage
  if (process.env.NODE_ENV === 'development') {
    console.warn(`📊 Mock Data Tracking: ${component} performed ${action} with mock data`);
  }
}
