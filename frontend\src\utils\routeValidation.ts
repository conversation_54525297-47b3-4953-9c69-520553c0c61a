/**
 * 🎯 ROUTE VALIDATION UTILITY
 * Validates routing system consistency and identifies conflicts
 */

import { unifiedRouteService } from '../services/unifiedRouteService';
import { unifiedRoleService } from '../services/unifiedRoleService';

export interface RouteValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

export class RouteValidator {
  /**
   * Validate the entire routing system for consistency
   */
  public static validateRoutingSystem(): RouteValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Validate route service consistency
    const routeValidation = unifiedRouteService.validateRoutingConsistency();
    if (!routeValidation.isValid) {
      errors.push(...routeValidation.issues);
    }

    // Validate role-route consistency
    const roleRouteValidation = this.validateRoleRouteConsistency();
    errors.push(...roleRouteValidation.errors);
    warnings.push(...roleRouteValidation.warnings);

    // Check for deprecated components usage
    const deprecationCheck = this.checkDeprecatedComponents();
    warnings.push(...deprecationCheck);

    // Validate user role restrictions
    const userRoleValidation = this.validateUserRoleRestrictions();
    if (userRoleValidation.length > 0) {
      errors.push(...userRoleValidation);
    }

    // Generate suggestions
    if (warnings.length > 0) {
      suggestions.push('Consider updating deprecated components to use the unified routing system');
    }

    if (errors.length === 0 && warnings.length === 0) {
      suggestions.push('Routing system is consistent and well-configured');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * Validate role-route consistency
   */
  private static validateRoleRouteConsistency(): { errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Test common routes for each role
    const testRoutes = [
      '/user/home',
      '/user/dashboard',
      '/entrepreneur/dashboard',
      '/admin/users',
      '/super_admin/system'
    ];

    const roles = ['user', 'entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'];

    for (const role of roles) {
      for (const route of testRoutes) {
        const canAccess = unifiedRouteService.canAccessRoute(role, route, true);
        const shouldAccess = this.shouldRoleAccessRoute(role, route);

        if (canAccess !== shouldAccess) {
          const issue = `Role '${role}' ${canAccess ? 'can' : 'cannot'} access '${route}' but ${shouldAccess ? 'should' : 'should not'}`;
          if (this.isCriticalMismatch(role, route)) {
            errors.push(issue);
          } else {
            warnings.push(issue);
          }
        }
      }
    }

    return { errors, warnings };
  }

  /**
   * Check if a role should be able to access a route
   */
  private static shouldRoleAccessRoute(role: string, route: string): boolean {
    // Users should only access user-specific routes
    if (role === 'user') {
      return route.startsWith('/user/') && !route.includes('/dashboard');
    }

    // Business roles should not access user-specific routes
    if (route.startsWith('/user/')) {
      return false;
    }

    // Admin routes
    if (route.includes('/users') || route.includes('/content')) {
      return ['admin', 'super_admin'].includes(role);
    }

    // Super admin routes
    if (route.includes('/system')) {
      return role === 'super_admin';
    }

    // Dashboard routes for business roles
    if (route.includes('/dashboard')) {
      return ['entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'].includes(role);
    }

    return true;
  }

  /**
   * Check if a role-route mismatch is critical
   */
  private static isCriticalMismatch(role: string, route: string): boolean {
    // Critical: Users accessing dashboard
    if (role === 'user' && route.includes('/dashboard')) {
      return true;
    }

    // Critical: Non-admin accessing admin routes
    if ((route.includes('/users') || route.includes('/content')) && !['admin', 'super_admin'].includes(role)) {
      return true;
    }

    // Critical: Non-super-admin accessing system routes
    if (route.includes('/system') && role !== 'super_admin') {
      return true;
    }

    return false;
  }

  /**
   * Check for usage of deprecated routing components
   */
  private static checkDeprecatedComponents(): string[] {
    const warnings: string[] = [];

    // ✅ FIXED: EnhancedProtectedRoute has been removed, no more deprecated components
    // All routing now uses the unified ConsolidatedProtectedRoute system

    return warnings;
  }

  /**
   * Validate user role restrictions are properly enforced
   */
  private static validateUserRoleRestrictions(): string[] {
    const errors: string[] = [];

    // Check that users cannot access dashboard
    if (unifiedRouteService.canAccessRoute('user', '/user/dashboard', true)) {
      errors.push('User role should not have access to dashboard routes');
    }

    // Check that users cannot access business routes
    const businessRoutes = [
      '/entrepreneur/dashboard',
      '/mentor/dashboard',
      '/admin/users'
    ];

    for (const route of businessRoutes) {
      if (unifiedRouteService.canAccessRoute('user', route, true)) {
        errors.push(`User role should not have access to business route: ${route}`);
      }
    }

    return errors;
  }

  /**
   * Generate routing system health report
   */
  public static generateHealthReport(): string {
    const validation = this.validateRoutingSystem();
    
    let report = '🎯 ROUTING SYSTEM HEALTH REPORT\n';
    report += '=====================================\n\n';

    if (validation.isValid) {
      report += '✅ STATUS: HEALTHY\n';
    } else {
      report += '❌ STATUS: ISSUES DETECTED\n';
    }

    if (validation.errors.length > 0) {
      report += '\n🚨 CRITICAL ERRORS:\n';
      validation.errors.forEach(error => {
        report += `  - ${error}\n`;
      });
    }

    if (validation.warnings.length > 0) {
      report += '\n⚠️ WARNINGS:\n';
      validation.warnings.forEach(warning => {
        report += `  - ${warning}\n`;
      });
    }

    if (validation.suggestions.length > 0) {
      report += '\n💡 SUGGESTIONS:\n';
      validation.suggestions.forEach(suggestion => {
        report += `  - ${suggestion}\n`;
      });
    }

    return report;
  }
}

// Export validation function for easy use
export const validateRouting = () => RouteValidator.validateRoutingSystem();
export const generateRoutingHealthReport = () => RouteValidator.generateHealthReport();
