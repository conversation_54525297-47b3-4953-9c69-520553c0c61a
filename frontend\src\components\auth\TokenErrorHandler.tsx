/**
 * Token Error Handler Component
 * Handles token-related errors and provides recovery options
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { AlertTriangle, RefreshCw, Home, LogIn } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';

interface TokenErrorHandlerProps {
  error: string;
  onRetry?: () => void;
  showRetryButton?: boolean;
  showHomeButton?: boolean;
  showLoginButton?: boolean;
}

const TokenErrorHandler: React.FC<TokenErrorHandlerProps> = ({
  error,
  onRetry,
  showRetryButton = true,
  showHomeButton = true,
  showLoginButton = true,
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetry = async () => {
    if (onRetry) {
      setIsRetrying(true);
      try {
        await onRetry();
      } catch (error) {
        console.error('Retry failed:', error);
      } finally {
        setIsRetrying(false);
      }
    }
  };

  const handleGoHome = () => {
    navigate('/');
  };

  const handleGoToLogin = () => {
    navigate('/login');
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-700 p-8 text-center">
        {/* Error Icon */}
        <div className="flex justify-center mb-6">
          <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center">
            <AlertTriangle size={32} className="text-red-400" />
          </div>
        </div>

        {/* Error Title */}
        <h1 className="text-2xl font-bold text-white mb-4">
          {t('auth.tokenError.title', 'Authentication Error')}
        </h1>

        {/* Error Message */}
        <p className="text-gray-300 mb-6 leading-relaxed">
          {t('auth.tokenError.message', 'There was an issue with your authentication token.')}
        </p>

        {/* Technical Error Details */}
        <div className="bg-gray-900/50 rounded-lg p-4 mb-6">
          <p className="text-sm text-gray-400 font-mono break-words">
            {error}
          </p>
        </div>

        {/* Action Buttons */}
        <div className={`flex flex-col gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
          {showRetryButton && onRetry && (
            <button
              onClick={handleRetry}
              disabled={isRetrying}
              className={`flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors ${
                isRTL ? 'flex-row-reverse' : ''
              }`}
            >
              <RefreshCw size={20} className={isRetrying ? 'animate-spin' : ''} />
              {isRetrying 
                ? t('auth.tokenError.retrying', 'Retrying...') 
                : t('auth.tokenError.retry', 'Try Again')
              }
            </button>
          )}

          {showLoginButton && (
            <button
              onClick={handleGoToLogin}
              className={`flex items-center justify-center gap-2 px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors ${
                isRTL ? 'flex-row-reverse' : ''
              }`}
            >
              <LogIn size={20} />
              {t('auth.tokenError.login', 'Go to Login')}
            </button>
          )}

          {showHomeButton && (
            <button
              onClick={handleGoHome}
              className={`flex items-center justify-center gap-2 px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors ${
                isRTL ? 'flex-row-reverse' : ''
              }`}
            >
              <Home size={20} />
              {t('auth.tokenError.home', 'Go Home')}
            </button>
          )}
        </div>

        {/* Help Text */}
        <p className="text-sm text-gray-400 mt-6">
          {t('auth.tokenError.help', 'If this problem persists, please contact support.')}
        </p>
      </div>
    </div>
  );
};

export default TokenErrorHandler;
