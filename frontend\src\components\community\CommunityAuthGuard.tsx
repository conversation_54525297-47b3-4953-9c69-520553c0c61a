import React, { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { LogIn, UserPlus } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { useLanguage } from '../../hooks/useLanguage';

interface CommunityAuthGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
  action?: string;
  showLoginPrompt?: boolean;
}

/**
 * Authentication guard for community features
 * Shows login prompt or custom fallback when user is not authenticated
 */
export const CommunityAuthGuard: React.FC<CommunityAuthGuardProps> = ({
  children,
  fallback,
  action = 'access this feature',
  showLoginPrompt = true
}) => {
  const { isAuthenticated } = useAuth();
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // If authenticated, show the protected content
  if (isAuthenticated) {
    return <>{children}</>;
  }

  // If custom fallback provided, use it
  if (fallback) {
    return <>{fallback}</>;
  }

  // If login prompt disabled, return null
  if (!showLoginPrompt) {
    return null;
  }

  // Default login prompt
  return (
    <div className="flex flex-col items-center justify-center p-8 text-center bg-gray-800/30 rounded-lg border border-gray-700/50">
      <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mb-4">
        <LogIn className="w-8 h-8 text-white" />
      </div>
      
      <h3 className="text-lg font-semibold text-white mb-2">
        {t('community.auth.loginRequired', 'Login Required')}
      </h3>
      
      <p className="text-gray-400 mb-6 max-w-md">
        {t('community.auth.loginToAccess', `Please log in to ${action}`)}
      </p>
      
      <div className={`flex gap-3 ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
        <button
          onClick={() => {
            // Navigate to login - you can customize this
            window.location.href = '/login';
          }}
          className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
        >
          <LogIn className="w-4 h-4" />
          {t('auth.login', 'Login')}
        </button>
        
        <button
          onClick={() => {
            // Navigate to register - you can customize this
            window.location.href = '/register';
          }}
          className="flex items-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
        >
          <UserPlus className="w-4 h-4" />
          {t('auth.register', 'Register')}
        </button>
      </div>
    </div>
  );
};

/**
 * Inline authentication guard for smaller UI elements
 */
export const InlineCommunityAuthGuard: React.FC<{
  children: ReactNode;
  fallback?: ReactNode;
  onClick?: () => void;
}> = ({ children, fallback, onClick }) => {
  const { isAuthenticated } = useAuth();
  const { t } = useTranslation();

  if (isAuthenticated) {
    return <>{children}</>;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  return (
    <button
      onClick={onClick || (() => window.location.href = '/login')}
      className="text-gray-400 hover:text-purple-400 transition-colors text-sm"
      title={t('community.auth.loginRequired', 'Login Required')}
    >
      {t('community.auth.loginToInteract', 'Login to interact')}
    </button>
  );
};

export default CommunityAuthGuard;
