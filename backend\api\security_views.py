"""
API Views for Security Dashboard and Management
"""
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.utils import timezone
from django.contrib.auth.models import User
from django.db.models import Count
from datetime import datetime, timedelta
from core.security_service import SecurityService
from core.security_models import AuditLog, SecurityThreat, ComplianceCheck
from users.permissions import IsSuperAdminUser
import logging

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([IsSuperAdminUser])
def security_dashboard(request):
    """
    Get comprehensive security dashboard data
    """
    try:
        security_service = SecurityService()
        dashboard_data = security_service.get_security_dashboard_data()
        
        return Response(dashboard_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error in security dashboard: {str(e)}")
        return Response(
            {'error': 'Failed to fetch security dashboard data'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsSuperAdminUser])
def audit_logs(request):
    """
    Get audit logs with filtering and pagination
    """
    try:
        # Extract filters from query parameters
        filters = {}
        if request.GET.get('user_id'):
            filters['user_id'] = request.GET.get('user_id')
        if request.GET.get('action_type'):
            filters['action_type'] = request.GET.get('action_type')
        if request.GET.get('severity'):
            filters['severity'] = request.GET.get('severity')
        if request.GET.get('start_date'):
            filters['start_date'] = datetime.fromisoformat(request.GET.get('start_date'))
        if request.GET.get('end_date'):
            filters['end_date'] = datetime.fromisoformat(request.GET.get('end_date'))
        if request.GET.get('is_suspicious') == 'true':
            filters['is_suspicious'] = True
        
        # Pagination parameters
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 50))
        
        security_service = SecurityService()
        logs_data = security_service.get_audit_logs(filters, page, page_size)
        
        return Response(logs_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error fetching audit logs: {str(e)}")
        return Response(
            {'error': 'Failed to fetch audit logs'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsSuperAdminUser])
def security_threats(request):
    """
    Get security threats with filtering
    """
    try:
        # Extract filters from query parameters
        filters = {}
        if request.GET.get('severity'):
            filters['severity'] = request.GET.get('severity')
        if request.GET.get('status'):
            filters['status'] = request.GET.get('status')
        if request.GET.get('threat_type'):
            filters['threat_type'] = request.GET.get('threat_type')
        if request.GET.get('start_date'):
            filters['start_date'] = datetime.fromisoformat(request.GET.get('start_date'))
        if request.GET.get('end_date'):
            filters['end_date'] = datetime.fromisoformat(request.GET.get('end_date'))
        
        security_service = SecurityService()
        threats_data = security_service.get_security_threats(filters)
        
        return Response({'threats': threats_data}, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error fetching security threats: {str(e)}")
        return Response(
            {'error': 'Failed to fetch security threats'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsSuperAdminUser])
def resolve_threat(request, threat_id):
    """
    Resolve a security threat
    """
    try:
        resolution_notes = request.data.get('resolution_notes', '')
        
        if not resolution_notes:
            return Response(
                {'error': 'Resolution notes are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        security_service = SecurityService()
        success = security_service.resolve_threat(threat_id, resolution_notes, request.user)
        
        if success:
            return Response({'message': 'Threat resolved successfully'}, status=status.HTTP_200_OK)
        else:
            return Response(
                {'error': 'Failed to resolve threat'},
                status=status.HTTP_404_NOT_FOUND
            )
        
    except Exception as e:
        logger.error(f"Error resolving threat {threat_id}: {str(e)}")
        return Response(
            {'error': 'Failed to resolve threat'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsSuperAdminUser])
def run_compliance_check(request):
    """
    Run a compliance check
    """
    try:
        compliance_type = request.data.get('compliance_type')
        check_name = request.data.get('check_name')
        
        if not compliance_type or not check_name:
            return Response(
                {'error': 'compliance_type and check_name are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        security_service = SecurityService()
        compliance_check = security_service.run_compliance_check(
            compliance_type, check_name, request.user
        )
        
        if compliance_check:
            return Response({
                'message': 'Compliance check completed',
                'check_id': str(compliance_check.id),
                'status': compliance_check.status,
                'score': compliance_check.compliance_score
            }, status=status.HTTP_200_OK)
        else:
            return Response(
                {'error': 'Failed to run compliance check'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
    except Exception as e:
        logger.error(f"Error running compliance check: {str(e)}")
        return Response(
            {'error': 'Failed to run compliance check'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsSuperAdminUser])
def compliance_reports(request):
    """
    Get compliance reports and checks
    """
    try:
        # Get recent compliance checks
        recent_checks = ComplianceCheck.objects.filter(
            check_date__gte=timezone.now() - timedelta(days=90)
        ).order_by('-check_date')[:50]
        
        compliance_data = []
        for check in recent_checks:
            compliance_data.append({
                'id': str(check.id),
                'check_date': check.check_date.isoformat(),
                'compliance_type': check.compliance_type,
                'check_name': check.check_name,
                'status': check.status,
                'compliance_score': check.compliance_score,
                'remediation_required': check.remediation_required,
                'checked_by': check.checked_by.username if check.checked_by else 'System',
                'issues_count': len(check.issues_found),
                'recommendations_count': len(check.recommendations),
            })
        
        # Get compliance summary by type
        compliance_summary = {}
        for check in recent_checks:
            comp_type = check.compliance_type
            if comp_type not in compliance_summary:
                compliance_summary[comp_type] = {
                    'total_checks': 0,
                    'avg_score': 0,
                    'last_check': None,
                    'status': 'unknown'
                }
            
            compliance_summary[comp_type]['total_checks'] += 1
            if not compliance_summary[comp_type]['last_check'] or check.check_date > compliance_summary[comp_type]['last_check']:
                compliance_summary[comp_type]['last_check'] = check.check_date.isoformat()
                compliance_summary[comp_type]['status'] = check.status
        
        # Calculate average scores
        for comp_type in compliance_summary:
            type_checks = [c for c in recent_checks if c.compliance_type == comp_type]
            if type_checks:
                compliance_summary[comp_type]['avg_score'] = sum(c.compliance_score for c in type_checks) / len(type_checks)
        
        return Response({
            'recent_checks': compliance_data,
            'compliance_summary': compliance_summary
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error fetching compliance reports: {str(e)}")
        return Response(
            {'error': 'Failed to fetch compliance reports'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsSuperAdminUser])
def security_analytics(request):
    """
    Get security analytics and insights
    """
    try:
        now = timezone.now()
        last_30d = now - timedelta(days=30)
        
        # Threat analytics
        threat_analytics = {
            'threats_by_type': list(SecurityThreat.objects.filter(
                detected_at__gte=last_30d
            ).values('threat_type').annotate(
                count=Count('id')
            ).order_by('-count')),
            
            'threats_by_severity': list(SecurityThreat.objects.filter(
                detected_at__gte=last_30d
            ).values('severity').annotate(
                count=Count('id')
            ).order_by('-count')),
            
            'top_source_ips': list(SecurityThreat.objects.filter(
                detected_at__gte=last_30d
            ).exclude(source_ip__isnull=True).values('source_ip').annotate(
                count=Count('id')
            ).order_by('-count')[:10]),
        }
        
        # Audit analytics
        audit_analytics = {
            'actions_by_type': list(AuditLog.objects.filter(
                timestamp__gte=last_30d
            ).values('action_type').annotate(
                count=Count('id')
            ).order_by('-count')),
            
            'suspicious_activities': AuditLog.objects.filter(
                timestamp__gte=last_30d,
                is_suspicious=True
            ).count(),
            
            'high_risk_events': AuditLog.objects.filter(
                timestamp__gte=last_30d,
                risk_score__gte=70
            ).count(),
        }
        
        # User activity analytics
        user_analytics = {
            'most_active_users': list(AuditLog.objects.filter(
                timestamp__gte=last_30d,
                user__isnull=False
            ).values('user__username').annotate(
                count=Count('id')
            ).order_by('-count')[:10]),
            
            'failed_login_users': list(AuditLog.objects.filter(
                timestamp__gte=last_30d,
                action_type='login',
                severity__in=['warning', 'error']
            ).values('user__username').annotate(
                count=Count('id')
            ).order_by('-count')[:10]),
        }
        
        return Response({
            'threat_analytics': threat_analytics,
            'audit_analytics': audit_analytics,
            'user_analytics': user_analytics,
            'period': '30_days'
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error fetching security analytics: {str(e)}")
        return Response(
            {'error': 'Failed to fetch security analytics'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsSuperAdminUser])
def export_security_data(request):
    """
    Export security data for compliance or analysis
    """
    try:
        export_type = request.data.get('export_type', 'audit_logs')
        start_date = request.data.get('start_date')
        end_date = request.data.get('end_date')
        
        if start_date:
            start_date = datetime.fromisoformat(start_date)
        else:
            start_date = timezone.now() - timedelta(days=30)
        
        if end_date:
            end_date = datetime.fromisoformat(end_date)
        else:
            end_date = timezone.now()
        
        if export_type == 'audit_logs':
            logs = AuditLog.objects.filter(
                timestamp__gte=start_date,
                timestamp__lte=end_date
            ).order_by('-timestamp')
            
            export_data = [{
                'timestamp': log.timestamp.isoformat(),
                'user': log.user.username if log.user else 'System',
                'action_type': log.action_type,
                'severity': log.severity,
                'event_description': log.event_description,
                'resource_type': log.resource_type,
                'resource_id': log.resource_id,
                'ip_address': log.ip_address,
                'is_suspicious': log.is_suspicious,
                'risk_score': log.risk_score,
            } for log in logs]
            
        elif export_type == 'security_threats':
            threats = SecurityThreat.objects.filter(
                detected_at__gte=start_date,
                detected_at__lte=end_date
            ).order_by('-detected_at')
            
            export_data = [{
                'detected_at': threat.detected_at.isoformat(),
                'threat_type': threat.threat_type,
                'severity': threat.severity,
                'status': threat.status,
                'source_ip': threat.source_ip,
                'description': threat.description,
                'risk_score': threat.risk_score,
                'confidence_level': threat.confidence_level,
                'resolved_at': threat.resolved_at.isoformat() if threat.resolved_at else None,
            } for threat in threats]
            
        else:
            return Response(
                {'error': 'Invalid export_type'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Log the export action
        AuditLog.log_action(
            user=request.user,
            action_type='data_export',
            event_description=f'Security data export: {export_type}',
            severity='info',
            event_data={
                'export_type': export_type,
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'record_count': len(export_data)
            }
        )
        
        return Response({
            'export_data': export_data,
            'metadata': {
                'export_type': export_type,
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'record_count': len(export_data),
                'exported_by': request.user.username,
                'export_timestamp': timezone.now().isoformat()
            }
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error exporting security data: {str(e)}")
        return Response(
            {'error': 'Failed to export security data'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
