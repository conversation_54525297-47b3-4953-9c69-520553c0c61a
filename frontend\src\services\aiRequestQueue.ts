/**
 * 🎯 AI REQUEST QUEUE MANAGER
 * Advanced queue management for AI requests with priority handling and retry logic
 */

import { AIRequest, AIResponse, AIError } from './unifiedAIService';

// ========================================
// QUEUE CONFIGURATION
// ========================================

export interface QueueConfig {
  maxConcurrentRequests: number;
  maxQueueSize: number;
  defaultTimeout: number;
  retryAttempts: number;
  retryDelay: number;
  priorityLevels: number;
}

export interface QueuedRequest extends AIRequest {
  queueId: string;
  priority: number;
  timeout: number;
  retryCount: number;
  maxRetries: number;
  createdAt: number;
  startedAt?: number;
  completedAt?: number;
  resolve: (response: AIResponse) => void;
  reject: (error: AIError) => void;
}

export interface QueueMetrics {
  totalRequests: number;
  completedRequests: number;
  failedRequests: number;
  queuedRequests: number;
  activeRequests: number;
  averageWaitTime: number;
  averageProcessingTime: number;
  throughput: number; // requests per minute
}

// ========================================
// AI REQUEST QUEUE CLASS
// ========================================

export class AIRequestQueue {
  private config: QueueConfig;
  private queues: Map<number, QueuedRequest[]> = new Map();
  private activeRequests: Map<string, QueuedRequest> = new Map();
  private completedRequests: QueuedRequest[] = [];
  private metrics: QueueMetrics;
  private isProcessing = false;
  private processingInterval?: NodeJS.Timeout;

  constructor(config: Partial<QueueConfig> = {}) {
    this.config = {
      maxConcurrentRequests: 5,
      maxQueueSize: 100,
      defaultTimeout: 30000, // 30 seconds
      retryAttempts: 3,
      retryDelay: 1000, // 1 second
      priorityLevels: 5,
      ...config
    };

    // Initialize priority queues
    for (let i = 0; i < this.config.priorityLevels; i++) {
      this.queues.set(i, []);
    }

    this.metrics = {
      totalRequests: 0,
      completedRequests: 0,
      failedRequests: 0,
      queuedRequests: 0,
      activeRequests: 0,
      averageWaitTime: 0,
      averageProcessingTime: 0,
      throughput: 0
    };

    this.startProcessing();
  }

  // ========================================
  // PUBLIC METHODS
  // ========================================

  async enqueue(
    request: Omit<AIRequest, 'id' | 'timestamp'>,
    priority: number = 2,
    timeout?: number,
    maxRetries?: number
  ): Promise<AIResponse> {
    return new Promise((resolve, reject) => {
      // Validate queue capacity
      if (this.getTotalQueuedRequests() >= this.config.maxQueueSize) {
        reject(this.createError('QUEUE_FULL', 'Request queue is at maximum capacity'));
        return;
      }

      // Validate priority
      const validPriority = Math.max(0, Math.min(priority, this.config.priorityLevels - 1));

      // Create queued request
      const queuedRequest: QueuedRequest = {
        ...request,
        id: this.generateRequestId(),
        timestamp: new Date().toISOString(),
        queueId: this.generateQueueId(),
        priority: validPriority,
        timeout: timeout || this.config.defaultTimeout,
        retryCount: 0,
        maxRetries: maxRetries || this.config.retryAttempts,
        createdAt: Date.now(),
        resolve,
        reject
      };

      // Add to appropriate priority queue
      const priorityQueue = this.queues.get(validPriority)!;
      priorityQueue.push(queuedRequest);

      // Update metrics
      this.metrics.totalRequests++;
      this.updateQueueMetrics();

      console.log(`🔄 Request queued: ${queuedRequest.queueId} (Priority: ${validPriority})`);
    });
  }

  getMetrics(): QueueMetrics {
    this.updateQueueMetrics();
    return { ...this.metrics };
  }

  getQueueStatus(): {
    queues: Record<number, number>;
    active: number;
    total: number;
  } {
    const queueStatus: Record<number, number> = {};
    
    this.queues.forEach((queue, priority) => {
      queueStatus[priority] = queue.length;
    });

    return {
      queues: queueStatus,
      active: this.activeRequests.size,
      total: this.getTotalQueuedRequests()
    };
  }

  clearQueue(priority?: number): void {
    if (priority !== undefined) {
      const queue = this.queues.get(priority);
      if (queue) {
        // Reject all requests in this priority queue
        queue.forEach(request => {
          request.reject(this.createError('QUEUE_CLEARED', 'Queue was cleared'));
        });
        queue.length = 0;
      }
    } else {
      // Clear all queues
      this.queues.forEach(queue => {
        queue.forEach(request => {
          request.reject(this.createError('QUEUE_CLEARED', 'All queues were cleared'));
        });
        queue.length = 0;
      });
    }

    this.updateQueueMetrics();
  }

  pauseProcessing(): void {
    this.isProcessing = false;
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }
  }

  resumeProcessing(): void {
    if (!this.isProcessing) {
      this.startProcessing();
    }
  }

  // ========================================
  // PRIVATE METHODS
  // ========================================

  private startProcessing(): void {
    this.isProcessing = true;
    this.processingInterval = setInterval(() => {
      this.processQueue();
    }, 100); // Check every 100ms
  }

  private async processQueue(): Promise<void> {
    if (!this.isProcessing) return;

    // Check if we can process more requests
    if (this.activeRequests.size >= this.config.maxConcurrentRequests) {
      return;
    }

    // Get next request from highest priority queue
    const nextRequest = this.getNextRequest();
    if (!nextRequest) {
      return;
    }

    // Move request to active processing
    this.activeRequests.set(nextRequest.queueId, nextRequest);
    nextRequest.startedAt = Date.now();

    console.log(`🚀 Processing request: ${nextRequest.queueId}`);

    try {
      // Process the request
      const response = await this.executeRequest(nextRequest);
      
      // Request completed successfully
      nextRequest.completedAt = Date.now();
      nextRequest.resolve(response);
      
      this.metrics.completedRequests++;
      this.completedRequests.push(nextRequest);
      
      console.log(`✅ Request completed: ${nextRequest.queueId}`);

    } catch (error) {
      // Request failed
      await this.handleRequestFailure(nextRequest, error as AIError);
    } finally {
      // Remove from active requests
      this.activeRequests.delete(nextRequest.queueId);
      this.updateQueueMetrics();
    }
  }

  private getNextRequest(): QueuedRequest | null {
    // Check queues from highest to lowest priority
    for (let priority = this.config.priorityLevels - 1; priority >= 0; priority--) {
      const queue = this.queues.get(priority)!;
      if (queue.length > 0) {
        return queue.shift()!;
      }
    }
    return null;
  }

  private async executeRequest(request: QueuedRequest): Promise<AIResponse> {
    // Create timeout promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(this.createError('TIMEOUT', `Request timed out after ${request.timeout}ms`));
      }, request.timeout);
    });

    // Execute the actual AI request (this would call your AI service)
    const requestPromise = this.callAIService(request);

    // Race between request and timeout
    return Promise.race([requestPromise, timeoutPromise]);
  }

  private async callAIService(request: QueuedRequest): Promise<AIResponse> {
    // This is a mock implementation - replace with actual AI service call
    const delay = Math.random() * 3000 + 1000; // 1-4 seconds
    
    await new Promise(resolve => setTimeout(resolve, delay));
    
    // Simulate occasional failures
    if (Math.random() < 0.1) { // 10% failure rate
      throw this.createError('AI_SERVICE_ERROR', 'AI service returned an error');
    }

    return {
      id: this.generateRequestId(),
      requestId: request.id,
      content: `AI response for: ${request.prompt}`,
      confidence: 0.85,
      processingTime: delay,
      tokens: {
        input: request.prompt.length,
        output: 150,
        total: request.prompt.length + 150
      },
      metadata: {
        model: 'gpt-4',
        version: '1.0',
        temperature: 0.7,
        maxTokens: 1000
      },
      timestamp: new Date().toISOString()
    };
  }

  private async handleRequestFailure(request: QueuedRequest, error: AIError): Promise<void> {
    request.retryCount++;

    // Check if we should retry
    if (request.retryCount <= request.maxRetries && error.retryable) {
      console.log(`🔄 Retrying request: ${request.queueId} (Attempt ${request.retryCount}/${request.maxRetries})`);
      
      // Add delay before retry
      await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * request.retryCount));
      
      // Re-queue the request with same priority
      const priorityQueue = this.queues.get(request.priority)!;
      priorityQueue.unshift(request); // Add to front of queue for faster retry
      
      return;
    }

    // Max retries exceeded or non-retryable error
    request.completedAt = Date.now();
    request.reject(error);
    
    this.metrics.failedRequests++;
    this.completedRequests.push(request);
    
    console.log(`❌ Request failed: ${request.queueId} - ${error.message}`);
  }

  private updateQueueMetrics(): void {
    this.metrics.queuedRequests = this.getTotalQueuedRequests();
    this.metrics.activeRequests = this.activeRequests.size;

    // Calculate average wait time
    const completedWithWaitTime = this.completedRequests.filter(r => r.startedAt);
    if (completedWithWaitTime.length > 0) {
      const totalWaitTime = completedWithWaitTime.reduce((sum, request) => {
        return sum + (request.startedAt! - request.createdAt);
      }, 0);
      this.metrics.averageWaitTime = totalWaitTime / completedWithWaitTime.length;
    }

    // Calculate average processing time
    const completedWithProcessingTime = this.completedRequests.filter(r => r.completedAt && r.startedAt);
    if (completedWithProcessingTime.length > 0) {
      const totalProcessingTime = completedWithProcessingTime.reduce((sum, request) => {
        return sum + (request.completedAt! - request.startedAt!);
      }, 0);
      this.metrics.averageProcessingTime = totalProcessingTime / completedWithProcessingTime.length;
    }

    // Calculate throughput (requests per minute)
    const oneMinuteAgo = Date.now() - 60000;
    const recentCompletedRequests = this.completedRequests.filter(r => 
      r.completedAt && r.completedAt > oneMinuteAgo
    );
    this.metrics.throughput = recentCompletedRequests.length;
  }

  private getTotalQueuedRequests(): number {
    let total = 0;
    this.queues.forEach(queue => {
      total += queue.length;
    });
    return total;
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateQueueId(): string {
    return `queue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private createError(code: string, message: string, retryable: boolean = false): AIError {
    return {
      code,
      message,
      retryable,
      details: { timestamp: new Date().toISOString() }
    };
  }
}

// ========================================
// SINGLETON INSTANCE
// ========================================

export const aiRequestQueue = new AIRequestQueue({
  maxConcurrentRequests: 3,
  maxQueueSize: 50,
  defaultTimeout: 30000,
  retryAttempts: 2,
  retryDelay: 1000,
  priorityLevels: 5
});

export default aiRequestQueue;
