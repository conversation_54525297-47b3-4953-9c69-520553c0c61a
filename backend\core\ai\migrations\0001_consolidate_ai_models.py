"""
🎯 AI MODELS CONSOLIDATION MIGRATION
Safely migrates AI models from separate apps to core.ai.models

This migration:
1. Creates new consolidated model tables with proper db_table names
2. Preserves existing data by using the original table names
3. Maintains all relationships and constraints
4. Provides rollback capability

Models being consolidated:
- core.models: AIConfiguration, AIUsageLog, AIServiceStatus, AIRateLimit
- ai_core.models: AISession, AIInteraction  
- ai_recommendations.models: AIRecommendation, AIRecommendationFeedback
- ai_models.models: AIModelMetadata, PredictionLog
"""

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django.core.validators


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('incubator', '0001_initial'),  # For BusinessIdea foreign key
    ]

    operations = [
        # ========================================
        # CORE AI CONFIGURATION MODELS
        # ========================================
        
        migrations.CreateModel(
            name='AIConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('provider', models.CharField(choices=[('gemini', 'Google Gemini')], max_length=20)),
                ('config_type', models.CharField(choices=[('api_key', 'API Key'), ('model_config', 'Model Configuration'), ('rate_limit', 'Rate Limiting'), ('feature_toggle', 'Feature Toggle'), ('prompt_template', 'Prompt Template')], max_length=20)),
                ('key', models.CharField(help_text="Configuration key (e.g., 'api_key', 'default_model')", max_length=100)),
                ('value', models.TextField(help_text='Configuration value (encrypted for sensitive data)')),
                ('is_active', models.BooleanField(default=True)),
                ('is_sensitive', models.BooleanField(default=False, help_text='Whether this config contains sensitive data')),
                ('description', models.TextField(blank=True, help_text='Description of this configuration')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='ai_configs_created', to='auth.user')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='ai_configs_updated', to='auth.user')),
            ],
            options={
                'verbose_name': 'AI Configuration',
                'verbose_name_plural': 'AI Configurations',
                'db_table': 'core_aiconfiguration',
                'ordering': ['provider', 'config_type', 'key'],
            },
        ),
        
        migrations.CreateModel(
            name='AIUsageLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('provider', models.CharField(choices=[('gemini', 'Google Gemini')], max_length=20)),
                ('model', models.CharField(max_length=100)),
                ('endpoint', models.CharField(help_text='API endpoint used', max_length=100)),
                ('session_id', models.CharField(blank=True, max_length=100)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('tokens_used', models.IntegerField(default=0)),
                ('cost', models.DecimalField(decimal_places=6, default=0, max_digits=10)),
                ('response_time', models.FloatField(help_text='Response time in seconds')),
                ('request_data', models.JSONField(default=dict, help_text='Request parameters (sanitized)')),
                ('response_status', models.CharField(default='success', max_length=20)),
                ('error_message', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='auth.user')),
            ],
            options={
                'db_table': 'core_aiusagelog',
                'ordering': ['-created_at'],
            },
        ),
        
        migrations.CreateModel(
            name='AIServiceStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('provider', models.CharField(choices=[('gemini', 'Google Gemini')], max_length=20, unique=True)),
                ('status', models.CharField(choices=[('healthy', 'Healthy'), ('degraded', 'Degraded'), ('down', 'Down'), ('maintenance', 'Maintenance')], default='healthy', max_length=20)),
                ('response_time', models.FloatField(default=0, help_text='Average response time in seconds')),
                ('success_rate', models.FloatField(default=100, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('last_check', models.DateTimeField(auto_now=True)),
                ('error_count', models.IntegerField(default=0)),
                ('last_error', models.TextField(blank=True)),
                ('last_error_at', models.DateTimeField(blank=True, null=True)),
                ('is_configured', models.BooleanField(default=False)),
                ('configuration_issues', models.JSONField(default=list, help_text='List of configuration issues')),
            ],
            options={
                'verbose_name': 'AI Service Status',
                'verbose_name_plural': 'AI Service Statuses',
                'db_table': 'core_aiservicestatus',
            },
        ),
        
        migrations.CreateModel(
            name='AIRateLimit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('provider', models.CharField(choices=[('gemini', 'Google Gemini')], max_length=20)),
                ('limit_type', models.CharField(choices=[('user', 'Per User'), ('role', 'Per Role'), ('global', 'Global'), ('ip', 'Per IP Address')], max_length=20)),
                ('identifier', models.CharField(help_text="User ID, role name, or 'global'", max_length=100)),
                ('max_requests', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('period', models.CharField(choices=[('minute', 'Per Minute'), ('hour', 'Per Hour'), ('day', 'Per Day'), ('month', 'Per Month')], max_length=20)),
                ('max_tokens', models.IntegerField(blank=True, help_text='Optional token limit', null=True)),
                ('current_requests', models.IntegerField(default=0)),
                ('current_tokens', models.IntegerField(default=0)),
                ('period_start', models.DateTimeField(default=django.utils.timezone.now)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'core_airatelimit',
                'ordering': ['provider', 'limit_type', 'identifier'],
            },
        ),
        
        # ========================================
        # AI SESSION AND INTERACTION MODELS
        # ========================================
        
        migrations.CreateModel(
            name='AISession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_type', models.CharField(max_length=50)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('ended_at', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='auth.user')),
            ],
            options={
                'verbose_name': 'AI Session',
                'verbose_name_plural': 'AI Sessions',
                'db_table': 'ai_core_aisession',
                'ordering': ['-started_at'],
            },
        ),
        
        migrations.CreateModel(
            name='AIInteraction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_input', models.TextField()),
                ('ai_response', models.TextField()),
                ('interaction_type', models.CharField(max_length=50)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interactions', to='core.ai.aisession')),
            ],
            options={
                'verbose_name': 'AI Interaction',
                'verbose_name_plural': 'AI Interactions',
                'db_table': 'ai_core_aiinteraction',
                'ordering': ['created_at'],
            },
        ),
        
        # ========================================
        # AI RECOMMENDATION MODELS
        # ========================================
        
        migrations.CreateModel(
            name='AIRecommendation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('recommendation_type', models.CharField(choices=[('milestone', 'Milestone'), ('goal', 'Goal'), ('mentor', 'Mentor'), ('resource', 'Resource'), ('strategy', 'Strategy'), ('general', 'General')], default='general', max_length=20)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('reasoning', models.TextField(help_text="AI's reasoning for this recommendation")),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='medium', max_length=20)),
                ('action_items', models.TextField(help_text='Specific action items to implement')),
                ('expected_outcome', models.TextField(help_text='Expected outcome of implementing this recommendation')),
                ('relevance_score', models.FloatField(default=0.0, help_text='Relevance score from 0-100')),
                ('is_implemented', models.BooleanField(default=False)),
                ('implementation_notes', models.TextField(blank=True, null=True)),
                ('implemented_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business_idea', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ai_recommendations', to='incubator.businessidea')),
                ('implemented_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='implemented_recommendations', to='auth.user')),
            ],
            options={
                'db_table': 'ai_recommendations_airecommendation',
                'ordering': ['-created_at'],
            },
        ),
        
        migrations.CreateModel(
            name='AIRecommendationFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('feedback_type', models.CharField(choices=[('helpful', 'Helpful'), ('not_helpful', 'Not Helpful'), ('implemented', 'Implemented'), ('not_applicable', 'Not Applicable')], max_length=20)),
                ('comments', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('recommendation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feedback', to='core.ai.airecommendation')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='auth.user')),
            ],
            options={
                'db_table': 'ai_recommendations_airecommendationfeedback',
                'ordering': ['-created_at'],
            },
        ),
        
        # ========================================
        # AI MODEL METADATA AND PREDICTION MODELS
        # ========================================
        
        migrations.CreateModel(
            name='AIModelMetadata',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('version', models.CharField(max_length=20)),
                ('description', models.TextField()),
                ('model_type', models.CharField(max_length=50)),
                ('file_path', models.CharField(max_length=255)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'AI Model Metadata',
                'verbose_name_plural': 'AI Model Metadata',
                'db_table': 'ai_models_aimodelmetadata',
                'ordering': ['-created_at'],
            },
        ),
        
        migrations.CreateModel(
            name='PredictionLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('model_name', models.CharField(max_length=100)),
                ('input_data', models.JSONField()),
                ('prediction_result', models.JSONField()),
                ('confidence_score', models.FloatField(blank=True, null=True)),
                ('processing_time', models.FloatField(help_text='Processing time in seconds')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='auth.user')),
            ],
            options={
                'verbose_name': 'Prediction Log',
                'verbose_name_plural': 'Prediction Logs',
                'db_table': 'ai_models_predictionlog',
                'ordering': ['-created_at'],
            },
        ),
        
        # ========================================
        # CONSTRAINTS AND INDEXES
        # ========================================
        
        migrations.AddConstraint(
            model_name='aiconfiguration',
            constraint=models.UniqueConstraint(fields=('provider', 'key'), name='core_ai_aiconfiguration_provider_key_unique'),
        ),
        
        migrations.AddConstraint(
            model_name='airatelimit',
            constraint=models.UniqueConstraint(fields=('provider', 'limit_type', 'identifier'), name='core_ai_airatelimit_provider_limit_type_identifier_unique'),
        ),
        
        migrations.AddConstraint(
            model_name='airecommendationfeedback',
            constraint=models.UniqueConstraint(fields=('recommendation', 'user'), name='core_ai_airecommendationfeedback_recommendation_user_unique'),
        ),
        
        # Add indexes for performance
        migrations.AddIndex(
            model_name='aiusagelog',
            index=models.Index(fields=['provider', 'created_at'], name='core_ai_aiusagelog_provider_created_at_idx'),
        ),
        
        migrations.AddIndex(
            model_name='aiusagelog',
            index=models.Index(fields=['user', 'created_at'], name='core_ai_aiusagelog_user_created_at_idx'),
        ),
        
        migrations.AddIndex(
            model_name='aiusagelog',
            index=models.Index(fields=['created_at'], name='core_ai_aiusagelog_created_at_idx'),
        ),
        
        migrations.AddIndex(
            model_name='airecommendation',
            index=models.Index(fields=['business_idea', 'recommendation_type'], name='core_ai_airecommendation_business_idea_type_idx'),
        ),
        
        migrations.AddIndex(
            model_name='airecommendation',
            index=models.Index(fields=['priority', 'relevance_score'], name='core_ai_airecommendation_priority_relevance_idx'),
        ),
        
        migrations.AddIndex(
            model_name='airecommendation',
            index=models.Index(fields=['is_implemented'], name='core_ai_airecommendation_is_implemented_idx'),
        ),
    ]
