/**
 * Input Sanitization Utility
 * 
 * Provides comprehensive input sanitization to prevent XSS, injection attacks,
 * and other security vulnerabilities in user-generated content.
 */

// HTML entities for escaping
const HTML_ENTITIES: Record<string, string> = {
  '&': '&amp;',
  '<': '&lt;',
  '>': '&gt;',
  '"': '&quot;',
  "'": '&#x27;',
  '/': '&#x2F;',
  '`': '&#96;',
  '=': '&#x3D;'
};

// Dangerous patterns to detect and remove
const DANGEROUS_PATTERNS = [
  // Script tags
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  // Event handlers
  /on\w+\s*=\s*["'][^"']*["']/gi,
  // JavaScript URLs
  /javascript\s*:/gi,
  // Data URLs with scripts
  /data\s*:\s*text\/html/gi,
  // Style with expressions
  /style\s*=\s*["'][^"']*expression\s*\([^"']*["']/gi,
  // Import statements
  /@import/gi,
  // Eval and similar functions
  /eval\s*\(/gi,
  /setTimeout\s*\(/gi,
  /setInterval\s*\(/gi,
  /Function\s*\(/gi
];

// Allowed HTML tags for rich text (if needed)
const ALLOWED_TAGS = [
  'p', 'br', 'strong', 'em', 'u', 'i', 'b',
  'ul', 'ol', 'li', 'blockquote', 'code', 'pre'
];

// Allowed attributes for HTML tags
const ALLOWED_ATTRIBUTES = ['class', 'id'];

/**
 * Escape HTML entities in a string
 */
export const escapeHtml = (text: string): string => {
  if (typeof text !== 'string') {
    return String(text);
  }

  return text.replace(/[&<>"'`=\/]/g, (match) => HTML_ENTITIES[match] || match);
};

/**
 * Unescape HTML entities in a string
 */
export const unescapeHtml = (text: string): string => {
  if (typeof text !== 'string') {
    return String(text);
  }

  const entityMap = Object.fromEntries(
    Object.entries(HTML_ENTITIES).map(([char, entity]) => [entity, char])
  );

  return text.replace(/&(?:amp|lt|gt|quot|#x27|#x2F|#96|#x3D);/g, (match) => 
    entityMap[match] || match
  );
};

/**
 * Remove dangerous patterns from text
 */
export const removeDangerousPatterns = (text: string): string => {
  if (typeof text !== 'string') {
    return String(text);
  }

  let sanitized = text;
  
  DANGEROUS_PATTERNS.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '');
  });

  return sanitized;
};

/**
 * Sanitize text content for safe display
 */
export const sanitizeText = (text: string, options: {
  allowHtml?: boolean;
  maxLength?: number;
  preserveNewlines?: boolean;
} = {}): string => {
  if (typeof text !== 'string') {
    return '';
  }

  let sanitized = text;

  // Trim whitespace
  sanitized = sanitized.trim();

  // Apply length limit
  if (options.maxLength && sanitized.length > options.maxLength) {
    sanitized = sanitized.substring(0, options.maxLength);
  }

  // Remove dangerous patterns first
  sanitized = removeDangerousPatterns(sanitized);

  // Handle HTML
  if (options.allowHtml) {
    sanitized = sanitizeHtml(sanitized);
  } else {
    sanitized = escapeHtml(sanitized);
  }

  // Preserve newlines if requested
  if (options.preserveNewlines && !options.allowHtml) {
    sanitized = sanitized.replace(/\n/g, '<br>');
  }

  return sanitized;
};

/**
 * Sanitize HTML content (basic implementation)
 */
export const sanitizeHtml = (html: string): string => {
  if (typeof html !== 'string') {
    return '';
  }

  // Remove dangerous patterns
  let sanitized = removeDangerousPatterns(html);

  // Simple tag filtering (in production, use a proper HTML sanitizer like DOMPurify)
  sanitized = sanitized.replace(/<(\/?)([\w-]+)([^>]*)>/gi, (match, slash, tagName, attributes) => {
    const tag = tagName.toLowerCase();
    
    if (!ALLOWED_TAGS.includes(tag)) {
      return '';
    }

    // Filter attributes
    const cleanAttributes = attributes.replace(/(\w+)\s*=\s*["']([^"']*)["']/g, (attrMatch: string, attrName: string, attrValue: string) => {
      if (ALLOWED_ATTRIBUTES.includes(attrName.toLowerCase())) {
        return `${attrName}="${escapeHtml(attrValue)}"`;
      }
      return '';
    });

    return `<${slash}${tag}${cleanAttributes}>`;
  });

  return sanitized;
};

/**
 * Sanitize URL for safe usage
 */
export const sanitizeUrl = (url: string): string => {
  if (typeof url !== 'string') {
    return '';
  }

  // Remove dangerous protocols
  const dangerousProtocols = ['javascript:', 'data:', 'vbscript:', 'file:', 'about:'];
  const lowerUrl = url.toLowerCase().trim();

  for (const protocol of dangerousProtocols) {
    if (lowerUrl.startsWith(protocol)) {
      return '';
    }
  }

  // Ensure URL is properly encoded
  try {
    const parsed = new URL(url);
    return parsed.toString();
  } catch {
    // If URL parsing fails, return empty string
    return '';
  }
};

/**
 * Validate and sanitize email address
 */
export const sanitizeEmail = (email: string): string => {
  if (typeof email !== 'string') {
    return '';
  }

  const sanitized = email.trim().toLowerCase();
  
  // Basic email validation regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  if (!emailRegex.test(sanitized)) {
    return '';
  }

  return sanitized;
};

/**
 * Sanitize username/display name
 */
export const sanitizeUsername = (username: string): string => {
  if (typeof username !== 'string') {
    return '';
  }

  // Remove dangerous characters and patterns
  let sanitized = username.trim();
  sanitized = removeDangerousPatterns(sanitized);
  sanitized = escapeHtml(sanitized);

  // Remove excessive whitespace
  sanitized = sanitized.replace(/\s+/g, ' ');

  // Limit length
  if (sanitized.length > 50) {
    sanitized = sanitized.substring(0, 50);
  }

  return sanitized;
};

/**
 * Sanitize search query
 */
export const sanitizeSearchQuery = (query: string): string => {
  if (typeof query !== 'string') {
    return '';
  }

  let sanitized = query.trim();
  
  // Remove dangerous patterns
  sanitized = removeDangerousPatterns(sanitized);
  
  // Escape HTML
  sanitized = escapeHtml(sanitized);
  
  // Remove excessive whitespace
  sanitized = sanitized.replace(/\s+/g, ' ');
  
  // Limit length
  if (sanitized.length > 200) {
    sanitized = sanitized.substring(0, 200);
  }

  return sanitized;
};

/**
 * Comprehensive input sanitizer for community posts
 */
export const sanitizePostContent = (content: string): string => {
  return sanitizeText(content, {
    allowHtml: false,
    maxLength: 5000,
    preserveNewlines: true
  });
};

/**
 * Comprehensive input sanitizer for comments
 */
export const sanitizeCommentContent = (content: string): string => {
  return sanitizeText(content, {
    allowHtml: false,
    maxLength: 1000,
    preserveNewlines: true
  });
};

/**
 * Validate and sanitize file names
 */
export const sanitizeFileName = (fileName: string): string => {
  if (typeof fileName !== 'string') {
    return '';
  }

  // Remove path traversal attempts
  let sanitized = fileName.replace(/[\/\\:*?"<>|]/g, '');
  
  // Remove dangerous patterns
  sanitized = removeDangerousPatterns(sanitized);
  
  // Limit length
  if (sanitized.length > 255) {
    sanitized = sanitized.substring(0, 255);
  }

  return sanitized;
};

/**
 * Input validation utilities
 */
export const validators = {
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  isValidUrl: (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  isValidUsername: (username: string): boolean => {
    const usernameRegex = /^[a-zA-Z0-9_-]{3,30}$/;
    return usernameRegex.test(username);
  },

  hasMinLength: (text: string, minLength: number): boolean => {
    return typeof text === 'string' && text.trim().length >= minLength;
  },

  hasMaxLength: (text: string, maxLength: number): boolean => {
    return typeof text === 'string' && text.length <= maxLength;
  }
};
