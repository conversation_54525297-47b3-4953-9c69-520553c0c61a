import React, { useState, useMemo } from 'react';
import { Plus, RefreshCw, Send, X, Calendar, Users, Globe, Lock, Eye } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../hooks/useLanguage';
import { communityApi } from '../services/communityApi';
import BaseModal from './common/BaseModal';
import { RTLText, RTLFlex } from './common';
import EnhancedRichTextEditor from './common/EnhancedRichTextEditor';
import EnhancedImageUpload from './common/EnhancedImageUpload';

// Utility function to decode HTML entities
const decodeHtmlEntities = (text: string): string => {
  return text
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&#96;/g, '`')
    .replace(/&nbsp;/g, ' ');
};

// Advanced Create Post Modal Component
interface CreatePostModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: {
    title?: string;
    content: string;
    tag_names?: string[];
    visibility?: 'public' | 'followers' | 'private';
    media?: string[];
  }) => void;
}

const CreatePostModal: React.FC<CreatePostModalProps> = ({ isOpen, onClose, onSubmit }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [tags, setTags] = useState('');
  const [visibility, setVisibility] = useState<'public' | 'followers' | 'private'>('public');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [images, setImages] = useState<any[]>([]);
  const [postType, setPostType] = useState<'text' | 'poll' | 'event'>('text');
  const [pollOptions, setPollOptions] = useState(['', '']);
  const [eventDate, setEventDate] = useState('');
  const [eventLocation, setEventLocation] = useState('');
  const [allowComments, setAllowComments] = useState(true);
  const [mentions, setMentions] = useState<Array<{ id: string; username: string; avatar?: string }>>([]);
  const [hashtags, setHashtags] = useState<Array<{ name: string; count: number }>>([]);
  const maxCharacters = 2000;

  // Calculate character count with useMemo to ensure it's properly defined
  const characterCount = useMemo(() => {
    const count = content.length;
    console.log('🎯 CHARACTER COUNT:', count, 'Content:', content.substring(0, 20));
    return count;
  }, [content]);

  // Handle mention search
  const handleMentionSearch = async (query: string) => {
    if (query.length < 2) {
      setMentions([]);
      return;
    }

    try {
      const results = await communityApi.searchUsers(query);
      setMentions(results.slice(0, 5));
    } catch (error) {
      console.error('Error searching mentions:', error);
    }
  };

  // Handle hashtag search
  const handleHashtagSearch = async (query: string) => {
    if (query.length < 1) {
      setHashtags([]);
      return;
    }

    try {
      const results = await communityApi.searchHashtags(query);
      setHashtags(results.slice(0, 5));
    } catch (error) {
      console.error('Error searching hashtags:', error);
    }
  };

  // Add poll option
  const addPollOption = () => {
    if (pollOptions.length < 6) {
      setPollOptions([...pollOptions, '']);
    }
  };

  // Remove poll option
  const removePollOption = (index: number) => {
    if (pollOptions.length > 2) {
      setPollOptions(pollOptions.filter((_, i) => i !== index));
    }
  };

  // Update poll option
  const updatePollOption = (index: number, value: string) => {
    const newOptions = [...pollOptions];
    newOptions[index] = value;
    setPollOptions(newOptions);
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!content.trim()) return;

    setIsSubmitting(true);

    try {
      const tagNames = tags.split(',').map(tag => tag.trim()).filter(tag => tag);
      const mediaUrls = images.filter(img => img.uploaded).map(img => img.url);

      // Clean and sanitize content to prevent HTML encoding issues
      const cleanContent = decodeHtmlEntities(content.trim());

      await onSubmit({
        title: title.trim() || undefined,
        content: cleanContent,
        tag_names: tagNames.length > 0 ? tagNames : undefined,
        visibility,
        media: mediaUrls.length > 0 ? mediaUrls : undefined
      });

      // Reset form and close modal on success
      setTitle('');
      setContent('');
      setTags('');
      setVisibility('public');
      setImages([]);
      onClose();
    } catch (error) {
      console.error('Failed to create post:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Visibility options with icons
  const visibilityOptions = [
    { value: 'public', label: t('community.visibility.public', 'Public'), icon: Globe },
    { value: 'followers', label: t('community.visibility.followers', 'Followers Only'), icon: Users },
    { value: 'private', label: t('community.visibility.private', 'Private'), icon: Lock }
  ];

  // Post type options
  const postTypeOptions = [
    { type: 'text', label: t('community.postType.text', 'Text'), icon: '📝' },
    { type: 'poll', label: t('community.postType.poll', 'Poll'), icon: '📊' },
    { type: 'event', label: t('community.postType.event', 'Event'), icon: '📅' }
  ];

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={t('community.createPost.title', 'Create New Post')}
      size="xl"
      maxHeight="95vh"
      className="overflow-hidden"
      footer={
        <RTLFlex className={`justify-between items-center w-full ${isRTL ? 'flex-row-reverse' : ''}`}>
          <RTLFlex className={`items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            {/* Schedule Option */}
            <button
              type="button"
              className="glass-morphism px-3 py-2 rounded-lg text-glass-muted hover:text-glass-primary transition-colors flex items-center gap-2"
            >
              <Calendar size={16} />
              <RTLText className="text-sm">{t('community.createPost.schedule', 'Schedule')}</RTLText>
            </button>

            {/* Draft Option */}
            <button
              type="button"
              className="glass-morphism px-3 py-2 rounded-lg text-glass-muted hover:text-glass-primary transition-colors flex items-center gap-2"
            >
              <span>💾</span>
              <RTLText className="text-sm">{t('community.createPost.saveDraft', 'Save Draft')}</RTLText>
            </button>
          </RTLFlex>

          <RTLFlex className={`items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 text-glass-muted hover:text-glass-primary transition-colors"
            >
              <RTLText>{t('common.cancel', 'Cancel')}</RTLText>
            </button>
            <button
              type="submit"
              form="create-post-form"
              disabled={!content.trim() || isSubmitting || (characterCount || 0) > maxCharacters}
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed px-8 py-2 rounded-lg font-medium text-white transition-all duration-200 flex items-center gap-2 hover:shadow-glow"
            >
              {isSubmitting && <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>}
              <RTLText>
                {isSubmitting
                  ? t('community.createPost.publishing', 'Publishing...')
                  : t('community.createPost.publish', 'Publish')
                }
              </RTLText>
            </button>
          </RTLFlex>
        </RTLFlex>
      }
    >

      <form id="create-post-form" onSubmit={handleSubmit} className="space-y-6">
        {/* Post Type Selector */}
        <div className="space-y-3">
          <RTLText as="label" className="block text-sm font-medium text-glass-primary">
            {t('community.createPost.postType', 'Post Type')}
          </RTLText>
          <RTLFlex className={`gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            {postTypeOptions.map((option) => (
              <button
                key={option.type}
                type="button"
                onClick={() => setPostType(option.type as 'text' | 'poll' | 'event')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all ${
                  postType === option.type
                    ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-glow'
                    : 'glass-light text-glass-secondary hover:bg-glass-hover border border-glass-border'
                } ${isRTL ? 'flex-row-reverse' : ''}`}
              >
                <span>{option.icon}</span>
                <RTLText className="text-sm font-medium">{option.label}</RTLText>
              </button>
            ))}
          </RTLFlex>
        </div>

        {/* Title */}
        <div className="space-y-2">
          <RTLText as="label" className="block text-sm font-medium text-glass-primary">
            {t('community.createPost.title', 'Title')}
            <span className="text-glass-muted ml-1">({t('common.optional', 'Optional')})</span>
          </RTLText>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder={t('community.createPost.titlePlaceholder', 'Add a title to your post...')}
            className="w-full glass-morphism border border-glass-border rounded-lg px-4 py-3 text-glass-primary placeholder-glass-muted focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all"
            dir={isRTL ? 'rtl' : 'ltr'}
          />
        </div>

        {/* Content */}
        <div className="space-y-2">
          <RTLText as="label" className="text-sm font-medium text-glass-primary">
            {t('community.createPost.content', 'Content')} <span className="text-red-400">*</span>
          </RTLText>
          <EnhancedRichTextEditor
            value={content}
            onChange={setContent}
            placeholder={t('community.createPost.contentPlaceholder', "What's on your mind?")}
            maxLength={maxCharacters}
            onMentionSearch={handleMentionSearch}
            onHashtagSearch={handleHashtagSearch}
            mentions={mentions}
            hashtags={hashtags}
            className="glass-morphism border border-glass-border rounded-lg"
          />

          {/* Character Counter */}
          <div className={`flex justify-end mt-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <span className={`text-xs ${
              (characterCount || 0) > maxCharacters
                ? 'text-red-400'
                : (characterCount || 0) > maxCharacters * 0.8
                  ? 'text-yellow-400'
                  : 'text-glass-secondary'
            }`}>
              {characterCount || 0}/{maxCharacters}
            </span>
          </div>
        </div>

        {/* Poll Options */}
        {postType === 'poll' && (
          <div className="space-y-3">
            <RTLText as="label" className="block text-sm font-medium text-glass-primary">
              {t('community.createPost.pollOptions', 'Poll Options')}
            </RTLText>
            <div className="space-y-3">
              {pollOptions.map((option, index) => (
                <RTLFlex key={index} className={`gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <input
                    type="text"
                    value={option}
                    onChange={(e) => updatePollOption(index, e.target.value)}
                    placeholder={`${t('community.createPost.option', 'Option')} ${index + 1}`}
                    className="flex-1 glass-morphism border border-glass-border rounded-lg px-3 py-2 text-glass-primary placeholder-glass-muted focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all"
                    dir={isRTL ? 'rtl' : 'ltr'}
                  />
                  {pollOptions.length > 2 && (
                    <button
                      type="button"
                      onClick={() => removePollOption(index)}
                      className="text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-400/10 transition-colors"
                    >
                      <X size={16} />
                    </button>
                  )}
                </RTLFlex>
              ))}
              {pollOptions.length < 6 && (
                <button
                  type="button"
                  onClick={addPollOption}
                  className="glass-light border border-glass-border rounded-lg px-4 py-2 text-glass-primary hover:bg-glass-hover transition-colors flex items-center gap-2"
                >
                  <Plus size={16} />
                  <RTLText className="text-sm">{t('community.createPost.addOption', 'Add Option')}</RTLText>
                </button>
              )}
            </div>
          </div>
        )}

        {/* Event Details */}
        {postType === 'event' && (
          <div className="space-y-4">
            <div className="space-y-2">
              <RTLText as="label" className="block text-sm font-medium text-glass-primary">
                {t('community.createPost.eventDate', 'Event Date')}
              </RTLText>
              <input
                type="datetime-local"
                value={eventDate}
                onChange={(e) => setEventDate(e.target.value)}
                className="w-full glass-morphism border border-glass-border rounded-lg px-4 py-3 text-glass-primary focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all"
              />
            </div>
            <div className="space-y-2">
              <RTLText as="label" className="block text-sm font-medium text-glass-primary">
                {t('community.createPost.eventLocation', 'Event Location')}
              </RTLText>
              <input
                type="text"
                value={eventLocation}
                onChange={(e) => setEventLocation(e.target.value)}
                placeholder={t('community.createPost.eventLocationPlaceholder', 'Where will the event take place?')}
                className="w-full glass-morphism border border-glass-border rounded-lg px-4 py-3 text-glass-primary placeholder-glass-muted focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all"
                dir={isRTL ? 'rtl' : 'ltr'}
              />
            </div>
          </div>
        )}

        {/* Tags */}
        <div className="space-y-2">
          <RTLText as="label" className="block text-sm font-medium text-glass-primary">
            {t('community.createPost.tags', 'Tags')}
            <span className="text-glass-muted ml-1">({t('community.createPost.commaSeparated', 'comma separated')})</span>
          </RTLText>
          <input
            type="text"
            value={tags}
            onChange={(e) => setTags(e.target.value)}
            placeholder={t('community.createPost.tagsPlaceholder', 'entrepreneurship, tech, investment')}
            className="w-full glass-morphism border border-glass-border rounded-lg px-4 py-3 text-glass-primary placeholder-glass-muted focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all"
            dir={isRTL ? 'rtl' : 'ltr'}
          />
        </div>

        {/* Media Upload */}
        <div className="space-y-3">
          <RTLText as="label" className="block text-sm font-medium text-glass-primary">
            {t('community.createPost.media', 'Media')}
          </RTLText>

          <EnhancedImageUpload
            images={images}
            onImagesChange={setImages}
            maxImages={4}
            maxFileSize={10}
            className="glass-morphism border border-glass-border rounded-lg"
          />
        </div>
        {/* Settings */}
        <div className="space-y-4">
          {/* Visibility */}
          <div className="space-y-2">
            <RTLText as="label" className="block text-sm font-medium text-glass-primary">
              {t('community.createPost.visibility', 'Visibility')}
            </RTLText>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
              {visibilityOptions.map((option) => {
                const IconComponent = option.icon;
                return (
                  <button
                    key={option.value}
                    type="button"
                    onClick={() => setVisibility(option.value as 'public' | 'followers' | 'private')}
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all text-sm ${
                      visibility === option.value
                        ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-glow'
                        : 'glass-light text-glass-secondary hover:bg-glass-hover border border-glass-border'
                    } ${isRTL ? 'flex-row-reverse' : ''}`}
                  >
                    <IconComponent size={16} />
                    <RTLText>{option.label}</RTLText>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Allow Comments */}
          <RTLFlex className={`items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <input
              type="checkbox"
              id="allow-comments"
              checked={allowComments}
              onChange={(e) => setAllowComments(e.target.checked)}
              className="w-4 h-4 text-purple-600 glass-morphism border-glass-border rounded focus:ring-purple-500 focus:ring-2"
            />
            <RTLText as="label" htmlFor="allow-comments" className="text-sm text-glass-secondary cursor-pointer">
              {t('community.createPost.allowComments', 'Allow comments')}
            </RTLText>
          </RTLFlex>
        </div>
      </form>
    </BaseModal>
  );
};

export default CreatePostModal;
