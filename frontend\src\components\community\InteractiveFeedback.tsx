/**
 * Interactive Feedback Components
 * Provides enhanced user feedback for community interactions
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { CheckCircle, AlertCircle, Info, X, Heart, Bookmark, Share2, MessageCircle } from 'lucide-react';

interface TooltipProps {
  content: string;
  children: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
  disabled?: boolean;
  className?: string;
}

export const Tooltip: React.FC<TooltipProps> = ({
  content,
  children,
  position = 'top',
  disabled = false,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const { isRTL } = useLanguage();

  if (disabled) return <>{children}</>;

  const positionClasses = {
    top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',
    bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
    left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',
    right: 'left-full top-1/2 transform -translate-y-1/2 ml-2'
  };

  const arrowClasses = {
    top: 'top-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900/90',
    bottom: 'bottom-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900/90',
    left: 'left-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-l-4 border-transparent border-l-gray-900/90',
    right: 'right-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-r-4 border-transparent border-r-gray-900/90'
  };

  return (
    <div 
      className={`relative inline-block ${className}`}
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      {isVisible && (
        <div className={`absolute ${positionClasses[position]} px-3 py-2 bg-gray-900/90 backdrop-blur-sm text-white text-xs rounded-lg opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50`}>
          {content}
          <div className={`absolute ${arrowClasses[position]} w-0 h-0`}></div>
        </div>
      )}
    </div>
  );
};

interface ActionButtonProps {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  count?: number;
  isActive?: boolean;
  isDisabled?: boolean;
  disabledReason?: string;
  onClick?: () => void;
  className?: string;
  variant?: 'like' | 'comment' | 'share' | 'save';
}

export const ActionButton: React.FC<ActionButtonProps> = ({
  icon: Icon,
  label,
  count,
  isActive = false,
  isDisabled = false,
  disabledReason,
  onClick,
  className = '',
  variant = 'like'
}) => {
  const { t } = useTranslation();

  const variantStyles = {
    like: {
      active: 'text-red-400 hover:text-red-300 hover:bg-red-400/10',
      inactive: 'text-gray-400 hover:text-red-400 hover:bg-red-400/10',
      disabled: 'text-gray-500 cursor-not-allowed opacity-50'
    },
    comment: {
      active: 'text-blue-400 hover:text-blue-300 hover:bg-blue-400/10',
      inactive: 'text-gray-400 hover:text-blue-400 hover:bg-blue-400/10',
      disabled: 'text-gray-500 cursor-not-allowed opacity-50'
    },
    share: {
      active: 'text-green-400 hover:text-green-300 hover:bg-green-400/10',
      inactive: 'text-gray-400 hover:text-green-400 hover:bg-green-400/10',
      disabled: 'text-gray-500 cursor-not-allowed opacity-50'
    },
    save: {
      active: 'text-yellow-400 hover:text-yellow-300 hover:bg-yellow-400/10',
      inactive: 'text-gray-400 hover:text-yellow-400 hover:bg-yellow-400/10',
      disabled: 'text-gray-500 cursor-not-allowed opacity-50'
    }
  };

  const getStyleClass = () => {
    if (isDisabled) return variantStyles[variant].disabled;
    return isActive ? variantStyles[variant].active : variantStyles[variant].inactive;
  };

  const buttonContent = (
    <button
      onClick={isDisabled ? undefined : onClick}
      disabled={isDisabled}
      className={`flex items-center gap-2 px-2 py-1 rounded-lg transition-all duration-200 hover:scale-105 ${getStyleClass()} ${className}`}
      aria-label={isDisabled ? disabledReason : label}
    >
      <Icon className={`w-4 h-4 transition-all duration-200 ${isActive && variant === 'like' ? 'fill-current animate-pulse' : ''} ${isActive && variant === 'save' ? 'fill-current animate-pulse' : ''}`} />
      {count !== undefined && (
        <span className="text-sm font-medium">{count}</span>
      )}
    </button>
  );

  if (isDisabled && disabledReason) {
    return (
      <Tooltip content={disabledReason}>
        {buttonContent}
      </Tooltip>
    );
  }

  return (
    <Tooltip content={label}>
      {buttonContent}
    </Tooltip>
  );
};

interface FeedbackToastProps {
  type: 'success' | 'error' | 'info' | 'warning';
  message: string;
  isVisible: boolean;
  onClose: () => void;
  duration?: number;
}

export const FeedbackToast: React.FC<FeedbackToastProps> = ({
  type,
  message,
  isVisible,
  onClose,
  duration = 3000
}) => {
  const { isRTL } = useLanguage();

  useEffect(() => {
    if (isVisible && duration > 0) {
      const timer = setTimeout(onClose, duration);
      return () => clearTimeout(timer);
    }
  }, [isVisible, duration, onClose]);

  if (!isVisible) return null;

  const typeConfig = {
    success: {
      icon: CheckCircle,
      bgColor: 'bg-green-600/90',
      borderColor: 'border-green-500',
      iconColor: 'text-green-200'
    },
    error: {
      icon: AlertCircle,
      bgColor: 'bg-red-600/90',
      borderColor: 'border-red-500',
      iconColor: 'text-red-200'
    },
    warning: {
      icon: AlertCircle,
      bgColor: 'bg-yellow-600/90',
      borderColor: 'border-yellow-500',
      iconColor: 'text-yellow-200'
    },
    info: {
      icon: Info,
      bgColor: 'bg-blue-600/90',
      borderColor: 'border-blue-500',
      iconColor: 'text-blue-200'
    }
  };

  const config = typeConfig[type];
  const Icon = config.icon;

  return (
    <div className={`fixed top-4 ${isRTL ? 'left-4' : 'right-4'} z-50 animate-slide-in-from-top`}>
      <div className={`${config.bgColor} backdrop-blur-sm border ${config.borderColor} rounded-lg p-4 shadow-lg max-w-sm`}>
        <div className="flex items-start gap-3">
          <Icon className={`w-5 h-5 ${config.iconColor} flex-shrink-0 mt-0.5`} />
          <div className="flex-1">
            <p className="text-white text-sm font-medium">{message}</p>
          </div>
          <button
            onClick={onClose}
            className="text-white/70 hover:text-white transition-colors"
            aria-label="Close notification"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

interface LoadingButtonProps {
  children: React.ReactNode;
  isLoading?: boolean;
  disabled?: boolean;
  onClick?: () => void;
  className?: string;
  variant?: 'primary' | 'secondary' | 'outline';
}

export const LoadingButton: React.FC<LoadingButtonProps> = ({
  children,
  isLoading = false,
  disabled = false,
  onClick,
  className = '',
  variant = 'primary'
}) => {
  const variantStyles = {
    primary: 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white',
    secondary: 'bg-gray-600 hover:bg-gray-700 text-white',
    outline: 'border border-white/20 hover:bg-white/10 text-white'
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled || isLoading}
      className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 ${variantStyles[variant]} ${disabled || isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'} ${className}`}
    >
      {isLoading && (
        <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full" />
      )}
      {children}
    </button>
  );
};

// Export all components
export const InteractiveFeedback = {
  Tooltip,
  ActionButton,
  FeedbackToast,
  LoadingButton
};

export default InteractiveFeedback;
