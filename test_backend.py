#!/usr/bin/env python3
"""
Simple script to test backend connectivity
"""
import requests
import json

def test_backend():
    base_url = "http://127.0.0.1:8000"
    
    print("Testing backend connectivity...")
    
    # Test 1: Basic health check
    try:
        response = requests.get(f"{base_url}/api/", timeout=5)
        print(f"✅ API root endpoint: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ API root endpoint failed: {e}")
        return False
    
    # Test 2: Community posts endpoint (without auth)
    try:
        response = requests.get(f"{base_url}/api/community/posts/", timeout=5)
        print(f"✅ Community posts endpoint: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Found {len(data.get('results', []))} posts")
    except requests.exceptions.RequestException as e:
        print(f"❌ Community posts endpoint failed: {e}")
    
    # Test 3: Admin endpoint (should require auth)
    try:
        response = requests.get(f"{base_url}/admin/", timeout=5)
        print(f"✅ Admin endpoint: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Admin endpoint failed: {e}")
    
    return True

if __name__ == "__main__":
    test_backend()
