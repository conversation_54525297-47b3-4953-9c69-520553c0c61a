import React, { memo, useMemo, useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Heart, MessageCircle, Share2, Bookmark, MoreHorizontal, Send, Edit3, Trash2, Flag } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { useAuth } from '../../hooks/useAuth';
import { usePostInteractions } from '../../hooks/usePostInteractions';
import { CommunityPost, CommunityComment } from '../../services/communityApi';
import EditPostModal from './EditPostModal';
import { SimpleCommentSection } from './SimpleCommentSection';
import { useUserNavigation } from '../../hooks/useUserNavigation';
import { FacebookAvatar } from './FacebookAvatar';

interface PostCardProps {
  post: CommunityPost;
  onLike: (postId: string) => void;
  onComment: (postId: string, content: string) => void;
  onShare: (postId: string) => void;
  onSave: (postId: string) => void;
  onEdit?: (postId: string, updatedPost: CommunityPost) => void;
  onDelete?: (postId: string) => void;
  onReport?: (postId: string) => void;
  // Removed complex comment handlers - using simple comments
  isAuthenticated: boolean;
}

const PostCard: React.FC<PostCardProps> = memo(({
  post,
  onLike,
  onComment,
  onShare,
  onSave,
  onEdit,
  onDelete,
  onReport,
  isAuthenticated
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAuth();
  const { handleUserClick } = useUserNavigation();

  // Local state for edit modal and dropdown
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showDropdown]);

  // Use the post interactions hook
  const {
    showCommentsFor,
    commentText,
    isSubmittingComment,
    handleToggleComments,
    handleCommentChange,
    handleSubmitComment,
  } = usePostInteractions();

  const showComments = showCommentsFor === post.id;

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmitComment(post.id);
    }
  };

  // Check if current user can edit this post
  const canEdit = user && post.author && user.id === post.author.id;

  // Check if dropdown should be shown (only if user has meaningful options)
  const shouldShowDropdown = isAuthenticated && (canEdit || true); // Always show for authenticated users (for report option)

  // Handle edit post
  const handleEditPost = () => {
    setShowEditModal(true);
    setShowDropdown(false);
  };

  // Handle delete post
  const handleDeletePost = () => {
    if (window.confirm(t('community.post.confirmDelete', 'Are you sure you want to delete this post?'))) {
      onDelete?.(post.id);
    }
    setShowDropdown(false);
  };

  // Handle report post
  const handleReportPost = () => {
    onReport?.(post.id);
    setShowDropdown(false);
  };

  // Handle post update from edit modal
  const handlePostUpdate = (updatedPost: CommunityPost) => {
    onEdit?.(post.id, updatedPost);
    setShowEditModal(false);
  };

  // Memoized time formatting to prevent unnecessary recalculations
  const formattedTime = useMemo(() => {
    const date = new Date(post.created_at);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return t('community.time.now');
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}${t('community.time.minutes')}`;
    if (diffInSeconds < 86400) return isRTL ? `${Math.floor(diffInSeconds / 3600)} س` : `${Math.floor(diffInSeconds / 3600)}h`;
    return isRTL ? `${Math.floor(diffInSeconds / 86400)} ي` : `${Math.floor(diffInSeconds / 86400)}d`;
  }, [post.created_at, isRTL]);

  // Memoized comment time formatting
  const formatCommentTime = useMemo(() => {
    return (dateString: string) => {
      const date = new Date(dateString);
      const now = new Date();
      const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

      if (diffInSeconds < 60) return t('community.time.now');
      if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}${t('community.time.minutes')}`;
      if (diffInSeconds < 86400) return isRTL ? `${Math.floor(diffInSeconds / 3600)} س` : `${Math.floor(diffInSeconds / 3600)}h`;
      return isRTL ? `${Math.floor(diffInSeconds / 86400)} ي` : `${Math.floor(diffInSeconds / 86400)}d`;
    };
  }, [isRTL]);

  return (
    <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-3 sm:p-4 lg:p-6 hover:bg-white/10 transition-all duration-300">
      {/* Post Header */}
      <div className="flex items-start justify-between mb-3 sm:mb-4">
        <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
          <FacebookAvatar
            username={post.author.username}
            userId={post.author.id}
            size="md"
            onClick={handleUserClick}
            clickable={true}
          />
          <div className="min-w-0 flex-1">
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleUserClick(post.author.username, post.author.id);
              }}
              className="font-medium text-white text-sm sm:text-base truncate hover:underline cursor-pointer bg-transparent border-none p-0 text-left"
            >
              {post.author.first_name || post.author.username}
            </button>
            <div className="text-xs sm:text-sm text-gray-400 flex items-center gap-2">
              <span>{formattedTime}</span>
              {post.visibility !== 'public' && (
                <span className="px-2 py-1 bg-purple-600/20 text-purple-300 text-xs rounded-full">
                  {t(`community.visibility.${post.visibility}`)}
                </span>
              )}
            </div>
          </div>
        </div>
        {shouldShowDropdown && (
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => setShowDropdown(!showDropdown)}
              className="text-gray-400 hover:text-white p-1 transition-colors rounded-full hover:bg-white/10"
              aria-label={t('community.post.moreOptions', 'More options')}
              aria-expanded={showDropdown}
              aria-haspopup="menu"
            >
              <MoreHorizontal className="w-4 h-4" />
            </button>

          {/* Dropdown Menu */}
          {showDropdown && (
            <div
              className="absolute right-0 top-8 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg shadow-lg py-2 min-w-[160px] z-10"
              role="menu"
              aria-label={t('community.post.optionsMenu', 'Post options menu')}
            >
              {canEdit && (
                <>
                  <button
                    onClick={handleEditPost}
                    className="w-full px-4 py-2 text-left text-white hover:bg-white/10 flex items-center gap-2 transition-colors"
                    role="menuitem"
                    aria-label={t('community.post.editAriaLabel', 'Edit this post')}
                  >
                    <Edit3 className="w-4 h-4" aria-hidden="true" />
                    {t('community.post.edit', 'Edit Post')}
                  </button>
                  <button
                    onClick={handleDeletePost}
                    className="w-full px-4 py-2 text-left text-red-400 hover:bg-white/10 flex items-center gap-2 transition-colors"
                    role="menuitem"
                    aria-label={t('community.post.deleteAriaLabel', 'Delete this post')}
                  >
                    <Trash2 className="w-4 h-4" aria-hidden="true" />
                    {t('community.post.delete', 'Delete Post')}
                  </button>
                  <div className="border-t border-white/10 my-1"></div>
                </>
              )}
              <button
                onClick={handleReportPost}
                className="w-full px-4 py-2 text-left text-yellow-400 hover:bg-white/10 flex items-center gap-2 transition-colors"
                role="menuitem"
                aria-label={t('community.post.reportAriaLabel', 'Report this post for inappropriate content')}
              >
                <Flag className="w-4 h-4" aria-hidden="true" />
                {t('community.post.report', 'Report Post')}
              </button>
            </div>
          )}
          </div>
        )}
      </div>

      {/* Post Content */}
      <div className="mb-3 sm:mb-4">
        {post.title && (
          <h3 className="text-base sm:text-lg font-semibold text-white mb-2">{post.title}</h3>
        )}
        <p className="text-gray-300 leading-relaxed text-sm sm:text-base">{post.content}</p>

        {/* Tags */}
        {post.tags && post.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 sm:gap-2 mt-2 sm:mt-3">
            {post.tags.map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-blue-600/20 text-blue-300 text-xs sm:text-sm rounded-full hover:bg-blue-600/30 cursor-pointer transition-colors"
              >
                #{tag.name}
              </span>
            ))}
          </div>
        )}
      </div>

      {/* Facebook-style Engagement Stats */}
      <div className="flex items-center justify-between py-2 text-sm text-gray-400 border-t border-gray-700/30">
        <div className="flex items-center gap-1">
          {post.like_count > 0 && (
            <>
              <div className="flex -space-x-1">
                <div className="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
                  <Heart className="w-2 h-2 text-white fill-current" />
                </div>
              </div>
              <span className="ml-1">{post.like_count}</span>
            </>
          )}
        </div>
        <div className="flex items-center gap-4">
          {post.comments_count > 0 && (
            <span>{post.comments_count} {post.comments_count === 1 ? 'comment' : 'comments'}</span>
          )}
          {post.shares_count > 0 && (
            <span>{post.shares_count} {post.shares_count === 1 ? 'share' : 'shares'}</span>
          )}
        </div>
      </div>

      {/* Facebook-style Action Buttons */}
      <div className="flex items-center border-t border-gray-700/30">
        <div className="flex items-center gap-3 sm:gap-4 lg:gap-6 flex-wrap">
          {/* Like Button with Tooltip */}
          <div className="relative group">
            <button
              onClick={() => isAuthenticated ? onLike(post.id) : null}
              className={`flex items-center gap-2 transition-all duration-200 ${
                post.is_liked
                  ? 'text-red-400 hover:text-red-300 hover:scale-105'
                  : 'text-gray-400 hover:text-red-400 hover:scale-105'
              } ${!isAuthenticated ? 'opacity-50 cursor-not-allowed' : 'hover:bg-red-400/10 px-2 py-1 rounded-lg'}`}
              disabled={!isAuthenticated}
              title={!isAuthenticated ? t('community.post.loginToLike', 'Login to like this post') : post.is_liked ? t('community.post.unlike', 'Unlike') : t('community.post.like', 'Like')}
              aria-label={!isAuthenticated ? t('community.post.loginToLike', 'Login to like this post') : post.is_liked ? t('community.post.unlike', 'Unlike') : t('community.post.like', 'Like')}
            >
              <Heart className={`w-4 h-4 transition-all duration-200 ${post.is_liked ? 'fill-current' : ''}`} />
              <span className="text-sm font-medium">Like</span>
            </button>
            {!isAuthenticated && (
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900/90 backdrop-blur-sm text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                {t('community.post.loginToLike', 'Login to like this post')}
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900/90"></div>
              </div>
            )}
          </div>

          {/* Comment Button */}
          <button
            onClick={() => handleToggleComments(post.id)}
            className="flex items-center gap-2 text-gray-400 hover:text-blue-400 hover:bg-blue-400/10 px-2 py-1 rounded-lg transition-all duration-200 hover:scale-105"
            title={showComments ? t('community.post.hideComments', 'Hide comments') : t('community.post.showComments', 'Show comments')}
            aria-label={showComments ? t('community.post.hideComments', 'Hide comments') : t('community.post.showComments', 'Show comments')}
          >
            <MessageCircle className="w-4 h-4 transition-all duration-200" />
            <span className="text-sm font-medium">Comment</span>
          </button>

          {/* Share Button with Tooltip */}
          <div className="relative group">
            <button
              onClick={() => isAuthenticated ? onShare(post.id) : null}
              className={`flex items-center gap-2 transition-all duration-200 ${
                !isAuthenticated
                  ? 'opacity-50 cursor-not-allowed text-gray-400'
                  : 'text-gray-400 hover:text-green-400 hover:bg-green-400/10 px-2 py-1 rounded-lg hover:scale-105'
              }`}
              disabled={!isAuthenticated}
              title={!isAuthenticated ? t('community.post.loginToShare', 'Login to share this post') : t('community.post.share', 'Share')}
              aria-label={!isAuthenticated ? t('community.post.loginToShare', 'Login to share this post') : t('community.post.share', 'Share')}
            >
              <Share2 className="w-4 h-4 transition-all duration-200" />
              <span className="text-sm font-medium">Share</span>
            </button>
            {!isAuthenticated && (
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900/90 backdrop-blur-sm text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                {t('community.post.loginToShare', 'Login to share this post')}
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900/90"></div>
              </div>
            )}
          </div>

          {/* Save Button with Tooltip */}
          <div className="relative group">
            <button
              onClick={() => isAuthenticated ? onSave(post.id) : null}
              className={`flex items-center gap-2 transition-all duration-200 ${
                post.is_saved
                  ? 'text-yellow-400 hover:text-yellow-300 hover:scale-105'
                  : 'text-gray-400 hover:text-yellow-400 hover:scale-105'
              } ${!isAuthenticated ? 'opacity-50 cursor-not-allowed' : 'hover:bg-yellow-400/10 px-2 py-1 rounded-lg'}`}
              disabled={!isAuthenticated}
              title={!isAuthenticated ? t('community.post.loginToSave', 'Login to save this post') : post.is_saved ? t('community.post.unsave', 'Remove from saved') : t('community.post.save', 'Save post')}
              aria-label={!isAuthenticated ? t('community.post.loginToSave', 'Login to save this post') : post.is_saved ? t('community.post.unsave', 'Remove from saved') : t('community.post.save', 'Save post')}
            >
              <Bookmark className={`w-4 h-4 transition-all duration-200 ${post.is_saved ? 'fill-current animate-pulse' : ''}`} />
            </button>
            {!isAuthenticated && (
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900/90 backdrop-blur-sm text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                {t('community.post.loginToSave', 'Login to save this post')}
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900/90"></div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Facebook-style Comments Section */}
      {showComments && (
        <div className="mt-3">
          <SimpleCommentSection
            postId={post.id}
            comments={post.comments || []}
            onAddComment={async (content: string, parentId?: string) => {
              await onComment(post.id, content);
            }}
            onLikeComment={async (commentId: string) => {
              // Handle comment like - you can add this to PostCard props if needed
              console.log('Like comment:', commentId);
            }}
          />
        </div>
      )}

      {/* Edit Post Modal */}
      {showEditModal && (
        <EditPostModal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          post={post}
          onUpdate={handlePostUpdate}
        />
      )}
    </div>
  );
});

PostCard.displayName = 'PostCard';

export default PostCard;
