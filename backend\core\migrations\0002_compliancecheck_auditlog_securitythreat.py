# Generated by Django 5.2.1 on 2025-07-26 20:12

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("core", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ComplianceCheck",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("check_date", models.DateTimeField(auto_now_add=True)),
                (
                    "compliance_type",
                    models.CharField(
                        choices=[
                            ("gdpr", "GDPR"),
                            ("hipaa", "HIPAA"),
                            ("sox", "SOX"),
                            ("pci_dss", "PCI DSS"),
                            ("iso27001", "ISO 27001"),
                            ("custom", "Custom Policy"),
                        ],
                        max_length=20,
                    ),
                ),
                ("check_name", models.CharField(max_length=200)),
                ("description", models.TextField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("compliant", "Compliant"),
                            ("non_compliant", "Non-Compliant"),
                            ("partial", "Partially Compliant"),
                            ("pending", "Pending Review"),
                            ("not_applicable", "Not Applicable"),
                        ],
                        max_length=20,
                    ),
                ),
                ("check_results", models.JSONField(default=dict)),
                ("compliance_score", models.IntegerField(default=0)),
                ("issues_found", models.JSONField(default=list)),
                ("recommendations", models.JSONField(default=list)),
                ("remediation_required", models.BooleanField(default=False)),
                ("remediation_deadline", models.DateTimeField(blank=True, null=True)),
                (
                    "remediation_status",
                    models.CharField(default="pending", max_length=20),
                ),
                ("remediation_notes", models.TextField(blank=True)),
                ("next_check_date", models.DateTimeField(blank=True, null=True)),
                (
                    "checked_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Compliance Check",
                "verbose_name_plural": "Compliance Checks",
                "ordering": ["-check_date"],
            },
        ),
        migrations.CreateModel(
            name="AuditLog",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("session_key", models.CharField(blank=True, max_length=40)),
                ("user_agent", models.TextField(blank=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("create", "Create"),
                            ("read", "Read"),
                            ("update", "Update"),
                            ("delete", "Delete"),
                            ("login", "Login"),
                            ("logout", "Logout"),
                            ("permission_check", "Permission Check"),
                            ("api_call", "API Call"),
                            ("file_access", "File Access"),
                            ("data_export", "Data Export"),
                            ("system_config", "System Configuration"),
                            ("security_event", "Security Event"),
                        ],
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("info", "Info"),
                            ("warning", "Warning"),
                            ("error", "Error"),
                            ("critical", "Critical"),
                        ],
                        db_index=True,
                        default="info",
                        max_length=10,
                    ),
                ),
                ("resource_type", models.CharField(blank=True, max_length=100)),
                ("resource_id", models.CharField(blank=True, max_length=100)),
                ("object_id", models.PositiveIntegerField(blank=True, null=True)),
                ("request_method", models.CharField(blank=True, max_length=10)),
                ("request_path", models.CharField(blank=True, max_length=500)),
                ("request_data", models.JSONField(blank=True, default=dict)),
                ("response_status", models.IntegerField(blank=True, null=True)),
                ("event_description", models.TextField()),
                ("event_data", models.JSONField(blank=True, default=dict)),
                ("changes", models.JSONField(blank=True, default=dict)),
                ("is_suspicious", models.BooleanField(db_index=True, default=False)),
                ("risk_score", models.IntegerField(db_index=True, default=0)),
                ("requires_review", models.BooleanField(default=False)),
                ("reviewed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "content_type",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "reviewed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="reviewed_audits",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Audit Log Entry",
                "verbose_name_plural": "Audit Log Entries",
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["timestamp", "severity"],
                        name="core_auditl_timesta_d48899_idx",
                    ),
                    models.Index(
                        fields=["user", "timestamp"],
                        name="core_auditl_user_id_7b678c_idx",
                    ),
                    models.Index(
                        fields=["action_type", "timestamp"],
                        name="core_auditl_action__64f4df_idx",
                    ),
                    models.Index(
                        fields=["is_suspicious", "timestamp"],
                        name="core_auditl_is_susp_74660c_idx",
                    ),
                    models.Index(
                        fields=["ip_address", "timestamp"],
                        name="core_auditl_ip_addr_1259d0_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="SecurityThreat",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("detected_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                (
                    "threat_type",
                    models.CharField(
                        choices=[
                            ("brute_force", "Brute Force Attack"),
                            ("suspicious_login", "Suspicious Login"),
                            ("data_breach", "Data Breach"),
                            ("unauthorized_access", "Unauthorized Access"),
                            ("malware", "Malware Detection"),
                            ("ddos", "DDoS Attack"),
                            ("sql_injection", "SQL Injection"),
                            ("xss", "Cross-Site Scripting"),
                            ("privilege_escalation", "Privilege Escalation"),
                            ("data_exfiltration", "Data Exfiltration"),
                            ("anomalous_behavior", "Anomalous Behavior"),
                        ],
                        db_index=True,
                        max_length=30,
                    ),
                ),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        db_index=True,
                        max_length=10,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("detected", "Detected"),
                            ("investigating", "Investigating"),
                            ("mitigated", "Mitigated"),
                            ("resolved", "Resolved"),
                            ("false_positive", "False Positive"),
                        ],
                        db_index=True,
                        default="detected",
                        max_length=20,
                    ),
                ),
                (
                    "source_ip",
                    models.GenericIPAddressField(blank=True, db_index=True, null=True),
                ),
                ("source_user_agent", models.TextField(blank=True)),
                ("description", models.TextField()),
                ("technical_details", models.JSONField(default=dict)),
                ("affected_resources", models.JSONField(default=list)),
                ("attack_vector", models.CharField(blank=True, max_length=200)),
                ("auto_mitigated", models.BooleanField(default=False)),
                ("mitigation_actions", models.JSONField(default=list)),
                ("resolved_at", models.DateTimeField(blank=True, null=True)),
                ("resolution_notes", models.TextField(blank=True)),
                ("risk_score", models.IntegerField(default=0)),
                ("confidence_level", models.IntegerField(default=50)),
                (
                    "assigned_to",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="assigned_threats",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "source_user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Security Threat",
                "verbose_name_plural": "Security Threats",
                "ordering": ["-detected_at"],
                "indexes": [
                    models.Index(
                        fields=["detected_at", "severity"],
                        name="core_securi_detecte_0677fb_idx",
                    ),
                    models.Index(
                        fields=["threat_type", "status"],
                        name="core_securi_threat__e409df_idx",
                    ),
                    models.Index(
                        fields=["source_ip", "detected_at"],
                        name="core_securi_source__8fa036_idx",
                    ),
                    models.Index(
                        fields=["status", "severity"],
                        name="core_securi_status_d0c58c_idx",
                    ),
                ],
            },
        ),
    ]
