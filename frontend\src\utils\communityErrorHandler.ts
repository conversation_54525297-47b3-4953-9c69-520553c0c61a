/**
 * Community-specific Error Handling
 * Provides specialized error handling for community features with specific error types,
 * user-friendly messages, and retry mechanisms
 */

import { TFunction } from 'react-i18next';

export type CommunityErrorType = 
  | 'NETWORK_ERROR'
  | 'AUTH_REQUIRED'
  | 'PERMISSION_DENIED'
  | 'RATE_LIMITED'
  | 'CONTENT_VALIDATION'
  | 'POST_NOT_FOUND'
  | 'USER_NOT_FOUND'
  | 'SERVER_ERROR'
  | 'CONNECTION_TIMEOUT'
  | 'CONTENT_TOO_LONG'
  | 'INVALID_MEDIA'
  | 'SPAM_DETECTED'
  | 'ACCOUNT_SUSPENDED'
  | 'FEATURE_DISABLED'
  | 'QUOTA_EXCEEDED'
  | 'UNKNOWN_ERROR';

export interface CommunityError {
  type: CommunityErrorType;
  message: string;
  userMessage: string;
  retryable: boolean;
  retryAfter?: number;
  statusCode?: number;
  originalError?: any;
  context?: string;
  timestamp: Date;
}

export interface RetryOptions {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

export class CommunityErrorHandler {
  private static defaultRetryOptions: RetryOptions = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffMultiplier: 2,
  };

  /**
   * Parse and classify API errors into community-specific error types
   */
  static parseError(error: any, context?: string): CommunityError {
    const timestamp = new Date();
    const statusCode = error?.status || error?.response?.status;
    const errorData = error?.data || error?.response?.data;
    const message = error?.message || 'Unknown error occurred';

    // Network/Connection errors
    if (!statusCode && (message.includes('fetch') || message.includes('network'))) {
      return {
        type: 'NETWORK_ERROR',
        message,
        userMessage: 'Unable to connect to the community service. Please check your internet connection.',
        retryable: true,
        originalError: error,
        context,
        timestamp,
      };
    }

    // Authentication errors
    if (statusCode === 401) {
      return {
        type: 'AUTH_REQUIRED',
        message,
        userMessage: 'Please log in to access community features.',
        retryable: false,
        statusCode,
        originalError: error,
        context,
        timestamp,
      };
    }

    // Permission errors
    if (statusCode === 403) {
      const isAccountSuspended = errorData?.code === 'ACCOUNT_SUSPENDED';
      const isFeatureDisabled = errorData?.code === 'FEATURE_DISABLED';
      
      if (isAccountSuspended) {
        return {
          type: 'ACCOUNT_SUSPENDED',
          message,
          userMessage: 'Your account has been suspended. Please contact support for assistance.',
          retryable: false,
          statusCode,
          originalError: error,
          context,
          timestamp,
        };
      }
      
      if (isFeatureDisabled) {
        return {
          type: 'FEATURE_DISABLED',
          message,
          userMessage: 'This feature is currently disabled. Please try again later.',
          retryable: false,
          statusCode,
          originalError: error,
          context,
          timestamp,
        };
      }

      return {
        type: 'PERMISSION_DENIED',
        message,
        userMessage: 'You do not have permission to perform this action.',
        retryable: false,
        statusCode,
        originalError: error,
        context,
        timestamp,
      };
    }

    // Not found errors
    if (statusCode === 404) {
      const isPostNotFound = context?.includes('post') || errorData?.resource === 'post';
      const isUserNotFound = context?.includes('user') || errorData?.resource === 'user';
      
      if (isPostNotFound) {
        return {
          type: 'POST_NOT_FOUND',
          message,
          userMessage: 'The post you are looking for could not be found.',
          retryable: false,
          statusCode,
          originalError: error,
          context,
          timestamp,
        };
      }
      
      if (isUserNotFound) {
        return {
          type: 'USER_NOT_FOUND',
          message,
          userMessage: 'The user profile could not be found.',
          retryable: false,
          statusCode,
          originalError: error,
          context,
          timestamp,
        };
      }

      return {
        type: 'POST_NOT_FOUND',
        message,
        userMessage: 'The requested content could not be found.',
        retryable: false,
        statusCode,
        originalError: error,
        context,
        timestamp,
      };
    }

    // Validation errors
    if (statusCode === 400) {
      const isContentTooLong = errorData?.code === 'CONTENT_TOO_LONG';
      const isInvalidMedia = errorData?.code === 'INVALID_MEDIA';
      const isSpamDetected = errorData?.code === 'SPAM_DETECTED';
      
      if (isContentTooLong) {
        return {
          type: 'CONTENT_TOO_LONG',
          message,
          userMessage: 'Your content is too long. Please shorten it and try again.',
          retryable: false,
          statusCode,
          originalError: error,
          context,
          timestamp,
        };
      }
      
      if (isInvalidMedia) {
        return {
          type: 'INVALID_MEDIA',
          message,
          userMessage: 'The media file you uploaded is not supported. Please try a different file.',
          retryable: false,
          statusCode,
          originalError: error,
          context,
          timestamp,
        };
      }
      
      if (isSpamDetected) {
        return {
          type: 'SPAM_DETECTED',
          message,
          userMessage: 'Your content was flagged as spam. Please review and try again.',
          retryable: false,
          statusCode,
          originalError: error,
          context,
          timestamp,
        };
      }

      return {
        type: 'CONTENT_VALIDATION',
        message,
        userMessage: 'Please check your input and try again.',
        retryable: false,
        statusCode,
        originalError: error,
        context,
        timestamp,
      };
    }

    // Rate limiting
    if (statusCode === 429) {
      const retryAfter = error?.headers?.['retry-after'] || errorData?.retry_after || 60;
      const isQuotaExceeded = errorData?.code === 'QUOTA_EXCEEDED';
      
      if (isQuotaExceeded) {
        return {
          type: 'QUOTA_EXCEEDED',
          message,
          userMessage: 'You have reached your daily limit. Please try again tomorrow.',
          retryable: false,
          retryAfter: retryAfter * 1000,
          statusCode,
          originalError: error,
          context,
          timestamp,
        };
      }

      return {
        type: 'RATE_LIMITED',
        message,
        userMessage: `Too many requests. Please wait ${retryAfter} seconds and try again.`,
        retryable: true,
        retryAfter: retryAfter * 1000,
        statusCode,
        originalError: error,
        context,
        timestamp,
      };
    }

    // Server errors
    if (statusCode >= 500) {
      const isTimeout = statusCode === 504 || message.includes('timeout');
      
      if (isTimeout) {
        return {
          type: 'CONNECTION_TIMEOUT',
          message,
          userMessage: 'The request timed out. Please try again.',
          retryable: true,
          statusCode,
          originalError: error,
          context,
          timestamp,
        };
      }

      return {
        type: 'SERVER_ERROR',
        message,
        userMessage: 'A server error occurred. Please try again in a moment.',
        retryable: true,
        statusCode,
        originalError: error,
        context,
        timestamp,
      };
    }

    // Unknown error
    return {
      type: 'UNKNOWN_ERROR',
      message,
      userMessage: 'An unexpected error occurred. Please try again.',
      retryable: true,
      originalError: error,
      context,
      timestamp,
    };
  }

  /**
   * Get localized error message
   */
  static getLocalizedMessage(error: CommunityError, t: TFunction, isRTL: boolean = false): string {
    const messageKey = `community.errors.${error.type.toLowerCase()}`;
    
    // Try to get translated message
    const translatedMessage = t(messageKey, { defaultValue: '' });
    if (translatedMessage) {
      return translatedMessage;
    }

    // Fallback to English messages with RTL support
    const messages: Record<CommunityErrorType, { en: string; ar: string }> = {
      NETWORK_ERROR: {
        en: 'Unable to connect to the community service. Please check your internet connection.',
        ar: 'غير قادر على الاتصال بخدمة المجتمع. يرجى التحقق من اتصالك بالإنترنت.'
      },
      AUTH_REQUIRED: {
        en: 'Please log in to access community features.',
        ar: 'يرجى تسجيل الدخول للوصول إلى ميزات المجتمع.'
      },
      PERMISSION_DENIED: {
        en: 'You do not have permission to perform this action.',
        ar: 'ليس لديك إذن لتنفيذ هذا الإجراء.'
      },
      RATE_LIMITED: {
        en: 'Too many requests. Please wait and try again.',
        ar: 'طلبات كثيرة جداً. يرجى الانتظار والمحاولة مرة أخرى.'
      },
      CONTENT_VALIDATION: {
        en: 'Please check your input and try again.',
        ar: 'يرجى التحقق من المدخلات والمحاولة مرة أخرى.'
      },
      POST_NOT_FOUND: {
        en: 'The post you are looking for could not be found.',
        ar: 'لا يمكن العثور على المنشور الذي تبحث عنه.'
      },
      USER_NOT_FOUND: {
        en: 'The user profile could not be found.',
        ar: 'لا يمكن العثور على ملف المستخدم.'
      },
      SERVER_ERROR: {
        en: 'A server error occurred. Please try again in a moment.',
        ar: 'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى بعد قليل.'
      },
      CONNECTION_TIMEOUT: {
        en: 'The request timed out. Please try again.',
        ar: 'انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.'
      },
      CONTENT_TOO_LONG: {
        en: 'Your content is too long. Please shorten it and try again.',
        ar: 'المحتوى طويل جداً. يرجى تقصيره والمحاولة مرة أخرى.'
      },
      INVALID_MEDIA: {
        en: 'The media file you uploaded is not supported. Please try a different file.',
        ar: 'ملف الوسائط الذي رفعته غير مدعوم. يرجى تجربة ملف مختلف.'
      },
      SPAM_DETECTED: {
        en: 'Your content was flagged as spam. Please review and try again.',
        ar: 'تم وضع علامة على المحتوى كرسائل مزعجة. يرجى المراجعة والمحاولة مرة أخرى.'
      },
      ACCOUNT_SUSPENDED: {
        en: 'Your account has been suspended. Please contact support for assistance.',
        ar: 'تم تعليق حسابك. يرجى الاتصال بالدعم للحصول على المساعدة.'
      },
      FEATURE_DISABLED: {
        en: 'This feature is currently disabled. Please try again later.',
        ar: 'هذه الميزة معطلة حالياً. يرجى المحاولة مرة أخرى لاحقاً.'
      },
      QUOTA_EXCEEDED: {
        en: 'You have reached your daily limit. Please try again tomorrow.',
        ar: 'لقد وصلت إلى حدك اليومي. يرجى المحاولة مرة أخرى غداً.'
      },
      UNKNOWN_ERROR: {
        en: 'An unexpected error occurred. Please try again.',
        ar: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.'
      }
    };

    const messageObj = messages[error.type];
    return isRTL ? messageObj.ar : messageObj.en;
  }

  /**
   * Execute function with retry logic
   */
  static async withRetry<T>(
    fn: () => Promise<T>,
    options: Partial<RetryOptions> = {},
    context?: string
  ): Promise<T> {
    const config = { ...this.defaultRetryOptions, ...options };
    let lastError: any;

    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        const communityError = this.parseError(error, context);

        // Don't retry if error is not retryable
        if (!communityError.retryable || attempt === config.maxRetries) {
          throw communityError;
        }

        // Calculate delay with exponential backoff
        const delay = Math.min(
          config.baseDelay * Math.pow(config.backoffMultiplier, attempt),
          config.maxDelay
        );

        // Add jitter to prevent thundering herd
        const jitteredDelay = delay + Math.random() * 1000;

        console.log(`Retrying in ${jitteredDelay}ms (attempt ${attempt + 1}/${config.maxRetries + 1})`);
        await new Promise(resolve => setTimeout(resolve, jitteredDelay));
      }
    }

    throw this.parseError(lastError, context);
  }

  /**
   * Check if error should trigger a retry
   */
  static shouldRetry(error: CommunityError): boolean {
    return error.retryable && !['AUTH_REQUIRED', 'PERMISSION_DENIED', 'CONTENT_VALIDATION'].includes(error.type);
  }

  /**
   * Get retry delay for rate limited requests
   */
  static getRetryDelay(error: CommunityError): number {
    if (error.type === 'RATE_LIMITED' && error.retryAfter) {
      return error.retryAfter;
    }
    return this.defaultRetryOptions.baseDelay;
  }
}
