import React, { useState, useEffect } from 'react';
import { <PERSON>ert<PERSON><PERSON>gle, CheckCircle, XCircle, Info, RefreshCw } from 'lucide-react';
import { communityApi } from '../../services/communityApi';
import { useCommunity } from '../../hooks/useCommunity';

interface BugReport {
  category: string;
  issue: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  status: 'pass' | 'fail' | 'warning';
  details?: string;
  fix?: string;
}

const BugAnalysisReport: React.FC = () => {
  const [reports, setReports] = useState<BugReport[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisComplete, setAnalysisComplete] = useState(false);
  
  const community = useCommunity();

  const runAnalysis = async () => {
    setIsAnalyzing(true);
    setReports([]);
    const newReports: BugReport[] = [];

    try {
      // Test 1: API Connectivity
      try {
        await communityApi.getPosts();
        newReports.push({
          category: 'API Connectivity',
          issue: 'Posts API endpoint',
          severity: 'critical',
          status: 'pass',
          details: 'Successfully connected to posts API'
        });
      } catch (error) {
        newReports.push({
          category: 'API Connectivity',
          issue: 'Posts API endpoint',
          severity: 'critical',
          status: 'fail',
          details: `Failed to connect: ${error}`,
          fix: 'Check backend server and API endpoints'
        });
      }

      // Test 2: Comments API
      try {
        const posts = await communityApi.getPosts();
        if (posts.length > 0) {
          // Try to get comments for first post
          const firstPost = posts[0];
          if (firstPost.comments && firstPost.comments.length > 0) {
            newReports.push({
              category: 'Comments System',
              issue: 'Comments data loading',
              severity: 'high',
              status: 'pass',
              details: `Found ${firstPost.comments.length} comments on first post`
            });
          } else {
            newReports.push({
              category: 'Comments System',
              issue: 'Comments data loading',
              severity: 'medium',
              status: 'warning',
              details: 'No comments found on posts - may be expected if no comments exist'
            });
          }
        }
      } catch (error) {
        newReports.push({
          category: 'Comments System',
          issue: 'Comments data loading',
          severity: 'high',
          status: 'fail',
          details: `Comments loading failed: ${error}`,
          fix: 'Check comment API endpoints and data structure'
        });
      }

      // Test 3: Hook Integration
      try {
        if (community.posts) {
          newReports.push({
            category: 'Hook Integration',
            issue: 'useCommunity hook posts',
            severity: 'critical',
            status: 'pass',
            details: `Hook loaded ${community.posts.length} posts`
          });
        } else {
          newReports.push({
            category: 'Hook Integration',
            issue: 'useCommunity hook posts',
            severity: 'critical',
            status: 'fail',
            details: 'useCommunity hook not returning posts data',
            fix: 'Check hook implementation and data flow'
          });
        }

        // Check comment handlers
        const commentHandlers = [
          'handleLikeComment',
          'handleEditComment', 
          'handleDeleteComment',
          'handleReportComment'
        ];
        
        commentHandlers.forEach(handler => {
          if (typeof community[handler as keyof typeof community] === 'function') {
            newReports.push({
              category: 'Comment Handlers',
              issue: `${handler} function`,
              severity: 'high',
              status: 'pass',
              details: `${handler} is properly defined`
            });
          } else {
            newReports.push({
              category: 'Comment Handlers',
              issue: `${handler} function`,
              severity: 'high',
              status: 'fail',
              details: `${handler} is not defined or not a function`,
              fix: 'Check useCommunityPosts hook implementation'
            });
          }
        });

      } catch (error) {
        newReports.push({
          category: 'Hook Integration',
          issue: 'useCommunity hook execution',
          severity: 'critical',
          status: 'fail',
          details: `Hook execution failed: ${error}`,
          fix: 'Check hook dependencies and implementation'
        });
      }

      // Test 4: Component Functionality
      try {
        // Test if posts have comments data structure
        const posts = await communityApi.getPosts();
        if (posts.length > 0) {
          const firstPost = posts[0];
          if (firstPost.comments !== undefined) {
            newReports.push({
              category: 'Data Structure',
              issue: 'Comments data structure',
              severity: 'high',
              status: 'pass',
              details: `Posts have comments property. First post has ${firstPost.comments?.length || 0} comments`
            });
          } else {
            newReports.push({
              category: 'Data Structure',
              issue: 'Comments data structure',
              severity: 'high',
              status: 'fail',
              details: 'Posts missing comments property',
              fix: 'Check API serializer to include comments in post data'
            });
          }
        }
      } catch (error) {
        newReports.push({
          category: 'Data Structure',
          issue: 'Posts data structure test',
          severity: 'high',
          status: 'fail',
          details: `Failed to test data structure: ${error}`,
          fix: 'Check API connectivity and data serialization'
        });
      }

      // Test 5: Backend Connectivity
      try {
        const response = await fetch('http://localhost:8000/api/community/posts/');
        if (response.ok) {
          newReports.push({
            category: 'Backend Connectivity',
            issue: 'Django backend connection',
            severity: 'critical',
            status: 'pass',
            details: `Backend responding with status ${response.status}`
          });
        } else {
          newReports.push({
            category: 'Backend Connectivity',
            issue: 'Django backend connection',
            severity: 'critical',
            status: 'fail',
            details: `Backend returned status ${response.status}`,
            fix: 'Start Django development server'
          });
        }
      } catch (error) {
        newReports.push({
          category: 'Backend Connectivity',
          issue: 'Django backend connection',
          severity: 'critical',
          status: 'fail',
          details: `Cannot connect to backend: ${error}`,
          fix: 'Start Django development server on port 8000'
        });
      }

      // Test 6: Specific UI/UX Issues Check
      const uiIssues = [
        {
          feature: 'Comment editing UI',
          test: () => document.querySelector('[data-testid="edit-comment"]'),
          severity: 'high' as const
        },
        {
          feature: 'Rich text editor in comments',
          test: () => document.querySelector('.rich-text-editor'),
          severity: 'medium' as const
        },
        {
          feature: 'Comment reply threading',
          test: () => document.querySelector('.comment-thread'),
          severity: 'high' as const
        },
        {
          feature: 'Post creation modal',
          test: () => document.querySelector('[data-testid="create-post-modal"]'),
          severity: 'medium' as const
        }
      ];

      uiIssues.forEach(({ feature, test, severity }) => {
        try {
          const element = test();
          newReports.push({
            category: 'UI Elements',
            issue: feature,
            severity,
            status: element ? 'pass' : 'warning',
            details: element ? `${feature} element found in DOM` : `${feature} element not found in DOM`,
            fix: element ? undefined : `Check if ${feature} is properly rendered`
          });
        } catch (error) {
          newReports.push({
            category: 'UI Elements',
            issue: feature,
            severity,
            status: 'fail',
            details: `Error testing ${feature}: ${error}`,
            fix: `Debug ${feature} implementation`
          });
        }
      });

    } catch (error) {
      newReports.push({
        category: 'Analysis Error',
        issue: 'Bug analysis execution',
        severity: 'critical',
        status: 'fail',
        details: `Analysis failed: ${error}`,
        fix: 'Check analysis tool implementation'
      });
    }

    setReports(newReports);
    setIsAnalyzing(false);
    setAnalysisComplete(true);
  };

  useEffect(() => {
    runAnalysis();
  }, []);

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <XCircle className="w-5 h-5 text-red-500" />;
      case 'high': return <AlertTriangle className="w-5 h-5 text-orange-500" />;
      case 'medium': return <Info className="w-5 h-5 text-yellow-500" />;
      case 'low': return <CheckCircle className="w-5 h-5 text-blue-500" />;
      default: return <Info className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'fail': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      default: return <Info className="w-4 h-4 text-gray-500" />;
    }
  };

  const groupedReports = reports.reduce((acc, report) => {
    if (!acc[report.category]) {
      acc[report.category] = [];
    }
    acc[report.category].push(report);
    return acc;
  }, {} as Record<string, BugReport[]>);

  const criticalIssues = reports.filter(r => r.severity === 'critical' && r.status === 'fail').length;
  const highIssues = reports.filter(r => r.severity === 'high' && r.status === 'fail').length;
  const warnings = reports.filter(r => r.status === 'warning').length;
  const passed = reports.filter(r => r.status === 'pass').length;

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 max-w-4xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-white">Community Page Bug Analysis</h2>
        <button
          onClick={runAnalysis}
          disabled={isAnalyzing}
          className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 ${isAnalyzing ? 'animate-spin' : ''}`} />
          {isAnalyzing ? 'Analyzing...' : 'Re-analyze'}
        </button>
      </div>

      {/* Summary */}
      <div className="grid grid-cols-4 gap-4 mb-6">
        <div className="bg-red-500/20 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-red-400">{criticalIssues}</div>
          <div className="text-sm text-red-300">Critical Issues</div>
        </div>
        <div className="bg-orange-500/20 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-orange-400">{highIssues}</div>
          <div className="text-sm text-orange-300">High Priority</div>
        </div>
        <div className="bg-yellow-500/20 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-yellow-400">{warnings}</div>
          <div className="text-sm text-yellow-300">Warnings</div>
        </div>
        <div className="bg-green-500/20 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-green-400">{passed}</div>
          <div className="text-sm text-green-300">Passed</div>
        </div>
      </div>

      {/* Detailed Reports */}
      <div className="space-y-6">
        {Object.entries(groupedReports).map(([category, categoryReports]) => (
          <div key={category} className="bg-white/5 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-white mb-3">{category}</h3>
            <div className="space-y-3">
              {categoryReports.map((report, index) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-white/5 rounded-lg">
                  <div className="flex items-center gap-2">
                    {getSeverityIcon(report.severity)}
                    {getStatusIcon(report.status)}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-white">{report.issue}</div>
                    <div className="text-sm text-gray-300 mt-1">{report.details}</div>
                    {report.fix && (
                      <div className="text-sm text-blue-300 mt-2 italic">Fix: {report.fix}</div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {!analysisComplete && !isAnalyzing && (
        <div className="text-center text-gray-400 mt-6">
          Click "Re-analyze" to run bug detection
        </div>
      )}
    </div>
  );
};

export default BugAnalysisReport;
