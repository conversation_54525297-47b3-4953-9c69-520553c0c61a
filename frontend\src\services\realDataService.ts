/**
 * 🎯 REAL DATA SERVICE
 * Centralized service for replacing mock data with real API calls
 */

import { apiRequest } from './api';
import { logMockDataUsage } from '../utils/mockDataAudit';

// ========================================
// USER RECOMMENDATIONS SERVICE
// ========================================

export interface UserRecommendation {
  id: string;
  username: string;
  first_name: string;
  last_name: string;
  full_name: string;
  avatar: string;
  is_verified: boolean;
  bio: string;
  location: string;
  user_role: string;
  date_joined: string;
  followers_count: number;
  following_count: number;
  posts_count: number;
  is_following: boolean;
  is_followed_by: boolean;
  recommendation_reason: {
    type: string;
    description: string;
    strength: number;
  };
  mutual_connections: number;
  similarity_score: number;
  activity_score: number;
}

export const userRecommendationsService = {
  async getRecommendations(limit: number = 10): Promise<UserRecommendation[]> {
    try {
      const response = await apiRequest<{ results: UserRecommendation[] }>(
        `/users/recommendations/?limit=${limit}`
      );
      return response.results || [];
    } catch (error) {
      console.error('Failed to fetch user recommendations:', error);
      logMockDataUsage('UserDiscovery', 'User Recommendations');
      throw new Error('Unable to load user recommendations. Please try again later.');
    }
  },

  async getMutualConnections(userId: string): Promise<UserRecommendation[]> {
    try {
      const response = await apiRequest<{ results: UserRecommendation[] }>(
        `/users/${userId}/mutual-connections/`
      );
      return response.results || [];
    } catch (error) {
      console.error('Failed to fetch mutual connections:', error);
      throw new Error('Unable to load mutual connections.');
    }
  },

  async followUser(userId: string): Promise<void> {
    await apiRequest(`/users/${userId}/follow/`, 'POST');
  },

  async unfollowUser(userId: string): Promise<void> {
    await apiRequest(`/users/${userId}/unfollow/`, 'POST');
  }
};

// ========================================
// BUSINESS PLANS SERVICE
// ========================================

export interface BusinessPlanCollaborator {
  id: string;
  name: string;
  role: string;
  avatar?: string;
  permissions: string[];
}

export interface BusinessPlan {
  id: string;
  title: string;
  description: string;
  industry: string;
  stage: string;
  lastModified: string;
  completionPercentage: number;
  status: 'draft' | 'review' | 'approved' | 'published';
  collaborators: BusinessPlanCollaborator[];
  sections: Record<string, any>;
  owner: {
    id: string;
    name: string;
    avatar?: string;
  };
  created_at: string;
  updated_at: string;
}

export const businessPlansService = {
  async getBusinessPlan(planId: string): Promise<BusinessPlan> {
    try {
      const response = await apiRequest<BusinessPlan>(`/incubator/business-plans/${planId}/`);
      return response;
    } catch (error) {
      console.error('Failed to fetch business plan:', error);
      logMockDataUsage('BusinessPlanPage', 'Business Plans');
      throw new Error('Unable to load business plan. Please check the plan ID and try again.');
    }
  },

  async getBusinessPlans(filters?: Record<string, any>): Promise<BusinessPlan[]> {
    try {
      const queryParams = filters ? new URLSearchParams(filters).toString() : '';
      const response = await apiRequest<{ results: BusinessPlan[] }>(
        `/incubator/business-plans/${queryParams ? `?${queryParams}` : ''}`
      );
      return response.results || [];
    } catch (error) {
      console.error('Failed to fetch business plans:', error);
      throw new Error('Unable to load business plans.');
    }
  },

  async createBusinessPlan(data: Partial<BusinessPlan>): Promise<BusinessPlan> {
    try {
      return await apiRequest<BusinessPlan>('/incubator/business-plans/', 'POST', data);
    } catch (error) {
      console.error('Failed to create business plan:', error);
      throw new Error('Unable to create business plan. Please try again.');
    }
  },

  async updateBusinessPlan(planId: string, data: Partial<BusinessPlan>): Promise<BusinessPlan> {
    try {
      return await apiRequest<BusinessPlan>(`/incubator/business-plans/${planId}/`, 'PATCH', data);
    } catch (error) {
      console.error('Failed to update business plan:', error);
      throw new Error('Unable to update business plan. Please try again.');
    }
  },

  async deleteBusinessPlan(planId: string): Promise<void> {
    try {
      await apiRequest(`/incubator/business-plans/${planId}/`, 'DELETE');
    } catch (error) {
      console.error('Failed to delete business plan:', error);
      throw new Error('Unable to delete business plan. Please try again.');
    }
  },

  async addCollaborator(planId: string, collaboratorData: Partial<BusinessPlanCollaborator>): Promise<BusinessPlanCollaborator> {
    try {
      return await apiRequest<BusinessPlanCollaborator>(
        `/incubator/business-plans/${planId}/collaborators/`, 
        'POST', 
        collaboratorData
      );
    } catch (error) {
      console.error('Failed to add collaborator:', error);
      throw new Error('Unable to add collaborator. Please try again.');
    }
  },

  async removeCollaborator(planId: string, collaboratorId: string): Promise<void> {
    try {
      await apiRequest(`/incubator/business-plans/${planId}/collaborators/${collaboratorId}/`, 'DELETE');
    } catch (error) {
      console.error('Failed to remove collaborator:', error);
      throw new Error('Unable to remove collaborator. Please try again.');
    }
  }
};

// ========================================
// SECURITY EVENTS SERVICE
// ========================================

export interface SecurityEvent {
  id: string;
  event_type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  user_id?: string;
  ip_address: string;
  user_agent: string;
  timestamp: string;
  status: 'open' | 'investigating' | 'resolved' | 'false_positive';
  details: Record<string, any>;
}

export const securityService = {
  async getSecurityEvents(filters?: Record<string, any>): Promise<SecurityEvent[]> {
    try {
      const queryParams = filters ? new URLSearchParams(filters).toString() : '';
      const response = await apiRequest<{ results: SecurityEvent[] }>(
        `/superadmin/security/events/${queryParams ? `?${queryParams}` : ''}`
      );
      return response.results || [];
    } catch (error) {
      console.error('Failed to fetch security events:', error);
      logMockDataUsage('SuperAdminApi', 'Security Events');
      throw new Error('Unable to load security events. Please try again later.');
    }
  },

  async getSecurityConfiguration(): Promise<Record<string, any>> {
    try {
      return await apiRequest<Record<string, any>>('/superadmin/security/config/');
    } catch (error) {
      console.error('Failed to fetch security configuration:', error);
      logMockDataUsage('SuperAdminApi', 'Security Configuration');
      throw new Error('Unable to load security configuration.');
    }
  },

  async updateSecurityConfiguration(config: Record<string, any>): Promise<Record<string, any>> {
    try {
      return await apiRequest<Record<string, any>>('/superadmin/security/config/', 'PATCH', config);
    } catch (error) {
      console.error('Failed to update security configuration:', error);
      throw new Error('Unable to update security configuration.');
    }
  },

  async resolveSecurityEvent(eventId: string, resolution: string): Promise<SecurityEvent> {
    try {
      return await apiRequest<SecurityEvent>(
        `/superadmin/security/events/${eventId}/resolve/`, 
        'POST', 
        { resolution }
      );
    } catch (error) {
      console.error('Failed to resolve security event:', error);
      throw new Error('Unable to resolve security event.');
    }
  }
};

// ========================================
// MENTORSHIP REQUESTS SERVICE
// ========================================

export interface MentorshipRequest {
  id: string;
  applicantName: string;
  applicantEmail: string;
  applicantTitle: string;
  company: string;
  experience: string;
  goals: string;
  preferredSchedule: string;
  sessionType: 'video' | 'audio' | 'chat';
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  submittedAt: string;
  urgency: 'low' | 'medium' | 'high';
  background: string;
  expectations: string;
}

export const mentorshipService = {
  async getMentorshipRequests(filters?: Record<string, any>): Promise<MentorshipRequest[]> {
    try {
      const queryParams = filters ? new URLSearchParams(filters).toString() : '';
      const response = await apiRequest<{ results: MentorshipRequest[] }>(
        `/incubator/mentorship-applications/${queryParams ? `?${queryParams}` : ''}`
      );
      return response.results || [];
    } catch (error) {
      console.error('Failed to fetch mentorship requests:', error);
      logMockDataUsage('MentorRequestsPage', 'Mentorship Requests');
      throw new Error('Unable to load mentorship requests. Please try again later.');
    }
  },

  async approveMentorshipRequest(requestId: string): Promise<MentorshipRequest> {
    try {
      return await apiRequest<MentorshipRequest>(
        `/incubator/mentorship-applications/${requestId}/approve/`, 
        'POST'
      );
    } catch (error) {
      console.error('Failed to approve mentorship request:', error);
      throw new Error('Unable to approve mentorship request.');
    }
  },

  async rejectMentorshipRequest(requestId: string, reason: string): Promise<MentorshipRequest> {
    try {
      return await apiRequest<MentorshipRequest>(
        `/incubator/mentorship-applications/${requestId}/reject/`, 
        'POST',
        { reason }
      );
    } catch (error) {
      console.error('Failed to reject mentorship request:', error);
      throw new Error('Unable to reject mentorship request.');
    }
  }
};

// Export all services
export default {
  userRecommendationsService,
  businessPlansService,
  securityService,
  mentorshipService
};
