"""
🎯 BUSINESS PLANS API VIEWS
Real implementation for business plan management
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.db.models import Q, Prefetch
from django.utils import timezone
from django.core.exceptions import ValidationError
import logging
import json

logger = logging.getLogger(__name__)

# Assuming you have these models - adjust imports based on your actual models
try:
    from incubator.models import BusinessPlan, BusinessPlanCollaborator, BusinessPlanTemplate
except ImportError:
    # Fallback if models don't exist yet
    BusinessPlan = None
    BusinessPlanCollaborator = None
    BusinessPlanTemplate = None

class BusinessPlanViewSet(viewsets.ModelViewSet):
    """
    Business Plan management endpoints
    Replaces mock data in BusinessPlanPage component
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Get business plans accessible to the current user"""
        user = self.request.user
        
        if not BusinessPlan:
            # Return empty queryset if model doesn't exist
            from django.contrib.auth.models import User
            return User.objects.none()
        
        # Base queryset with optimizations
        queryset = BusinessPlan.objects.select_related(
            'owner', 'template'
        ).prefetch_related(
            Prefetch(
                'collaborators',
                queryset=BusinessPlanCollaborator.objects.select_related('user')
            )
        )
        
        # Filter based on user role and permissions
        user_profile = getattr(user, 'profile', None)
        user_role = getattr(user_profile, 'role', 'user') if user_profile else 'user'
        
        if user_role in ['super_admin', 'admin']:
            # Admins can see all business plans
            return queryset.all()
        elif user_role == 'moderator':
            # Moderators can see published plans and their own
            return queryset.filter(
                Q(status__in=['published', 'review']) | Q(owner=user)
            )
        elif user_role in ['mentor', 'investor']:
            # Mentors and investors can see published plans and ones they collaborate on
            return queryset.filter(
                Q(status='published') | 
                Q(owner=user) | 
                Q(collaborators__user=user)
            ).distinct()
        else:
            # Entrepreneurs and regular users see only their own plans
            return queryset.filter(
                Q(owner=user) | Q(collaborators__user=user)
            ).distinct()

    def list(self, request):
        """
        GET /api/incubator/business-plans/
        List business plans with filtering and pagination
        """
        try:
            queryset = self.get_queryset()
            
            # Apply filters
            status_filter = request.query_params.get('status')
            if status_filter:
                queryset = queryset.filter(status=status_filter)
            
            industry_filter = request.query_params.get('industry')
            if industry_filter:
                queryset = queryset.filter(industry__icontains=industry_filter)
            
            search_query = request.query_params.get('search')
            if search_query:
                queryset = queryset.filter(
                    Q(title__icontains=search_query) |
                    Q(description__icontains=search_query)
                )
            
            # Pagination
            page_size = min(int(request.query_params.get('page_size', 20)), 100)
            page = int(request.query_params.get('page', 1))
            start = (page - 1) * page_size
            end = start + page_size
            
            total_count = queryset.count()
            business_plans = queryset[start:end]
            
            # Format response
            results = []
            for plan in business_plans:
                results.append(self._format_business_plan(plan, request.user))
            
            return Response({
                'results': results,
                'count': len(results),
                'total': total_count,
                'page': page,
                'page_size': page_size,
                'has_next': end < total_count,
                'has_previous': page > 1
            })
            
        except Exception as e:
            logger.error(f"Business plans list error for user {request.user.id}: {str(e)}")
            return Response(
                {
                    'error': 'Failed to fetch business plans',
                    'message': 'Unable to load business plans at this time'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def retrieve(self, request, pk=None):
        """
        GET /api/incubator/business-plans/{id}/
        Get detailed business plan information
        """
        try:
            business_plan = get_object_or_404(self.get_queryset(), pk=pk)
            
            # Check if user has permission to view this plan
            if not self._can_view_plan(request.user, business_plan):
                return Response(
                    {'error': 'Permission denied'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # Format detailed response
            plan_data = self._format_business_plan(business_plan, request.user, detailed=True)
            
            return Response(plan_data)
            
        except Exception as e:
            logger.error(f"Business plan retrieve error: {str(e)}")
            return Response(
                {'error': 'Failed to fetch business plan'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def create(self, request):
        """
        POST /api/incubator/business-plans/
        Create a new business plan
        """
        try:
            data = request.data
            user = request.user
            
            # Validate required fields
            required_fields = ['title', 'description']
            for field in required_fields:
                if not data.get(field):
                    return Response(
                        {'error': f'{field} is required'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            # Check if user can create business plans
            if not self._can_create_plan(user):
                return Response(
                    {'error': 'Permission denied'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # Create business plan
            if BusinessPlan:
                business_plan = BusinessPlan.objects.create(
                    title=data['title'],
                    description=data['description'],
                    industry=data.get('industry', ''),
                    stage=data.get('stage', 'idea'),
                    owner=user,
                    status='draft',
                    sections=data.get('sections', {}),
                    template_id=data.get('template_id')
                )
                
                # Add initial collaborator (owner)
                if BusinessPlanCollaborator:
                    BusinessPlanCollaborator.objects.create(
                        business_plan=business_plan,
                        user=user,
                        role='owner',
                        permissions=['read', 'write', 'delete', 'manage_collaborators']
                    )
                
                logger.info(f"Business plan created: {business_plan.id} by user {user.id}")
                
                return Response(
                    self._format_business_plan(business_plan, user, detailed=True),
                    status=status.HTTP_201_CREATED
                )
            else:
                return Response(
                    {'error': 'Business plan functionality not available'},
                    status=status.HTTP_501_NOT_IMPLEMENTED
                )
            
        except ValidationError as e:
            return Response(
                {'error': 'Validation error', 'details': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Business plan creation error: {str(e)}")
            return Response(
                {'error': 'Failed to create business plan'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def update(self, request, pk=None):
        """
        PUT/PATCH /api/incubator/business-plans/{id}/
        Update business plan
        """
        try:
            business_plan = get_object_or_404(self.get_queryset(), pk=pk)
            
            # Check permissions
            if not self._can_edit_plan(request.user, business_plan):
                return Response(
                    {'error': 'Permission denied'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            data = request.data
            
            # Update fields
            updatable_fields = ['title', 'description', 'industry', 'stage', 'sections', 'status']
            for field in updatable_fields:
                if field in data:
                    setattr(business_plan, field, data[field])
            
            business_plan.updated_at = timezone.now()
            business_plan.save()
            
            logger.info(f"Business plan updated: {business_plan.id} by user {request.user.id}")
            
            return Response(
                self._format_business_plan(business_plan, request.user, detailed=True)
            )
            
        except Exception as e:
            logger.error(f"Business plan update error: {str(e)}")
            return Response(
                {'error': 'Failed to update business plan'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def destroy(self, request, pk=None):
        """
        DELETE /api/incubator/business-plans/{id}/
        Delete business plan
        """
        try:
            business_plan = get_object_or_404(self.get_queryset(), pk=pk)
            
            # Check permissions (only owner or admin can delete)
            if not (business_plan.owner == request.user or self._is_admin(request.user)):
                return Response(
                    {'error': 'Permission denied'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            plan_id = business_plan.id
            business_plan.delete()
            
            logger.info(f"Business plan deleted: {plan_id} by user {request.user.id}")
            
            return Response(status=status.HTTP_204_NO_CONTENT)
            
        except Exception as e:
            logger.error(f"Business plan deletion error: {str(e)}")
            return Response(
                {'error': 'Failed to delete business plan'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def add_collaborator(self, request, pk=None):
        """
        POST /api/incubator/business-plans/{id}/add_collaborator/
        Add collaborator to business plan
        """
        try:
            business_plan = get_object_or_404(self.get_queryset(), pk=pk)
            
            # Check permissions
            if not self._can_manage_collaborators(request.user, business_plan):
                return Response(
                    {'error': 'Permission denied'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            data = request.data
            user_id = data.get('user_id')
            role = data.get('role', 'collaborator')
            permissions = data.get('permissions', ['read'])
            
            if not user_id:
                return Response(
                    {'error': 'user_id is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Get user to add
            from django.contrib.auth.models import User
            try:
                collaborator_user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                return Response(
                    {'error': 'User not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Check if already a collaborator
            if BusinessPlanCollaborator and BusinessPlanCollaborator.objects.filter(
                business_plan=business_plan, user=collaborator_user
            ).exists():
                return Response(
                    {'error': 'User is already a collaborator'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Add collaborator
            if BusinessPlanCollaborator:
                collaborator = BusinessPlanCollaborator.objects.create(
                    business_plan=business_plan,
                    user=collaborator_user,
                    role=role,
                    permissions=permissions
                )
                
                logger.info(f"Collaborator added to business plan {business_plan.id}: user {collaborator_user.id}")
                
                return Response({
                    'id': str(collaborator.id),
                    'user': {
                        'id': str(collaborator_user.id),
                        'username': collaborator_user.username,
                        'full_name': collaborator_user.get_full_name()
                    },
                    'role': role,
                    'permissions': permissions,
                    'added_at': collaborator.created_at.isoformat()
                }, status=status.HTTP_201_CREATED)
            else:
                return Response(
                    {'error': 'Collaborator functionality not available'},
                    status=status.HTTP_501_NOT_IMPLEMENTED
                )
            
        except Exception as e:
            logger.error(f"Add collaborator error: {str(e)}")
            return Response(
                {'error': 'Failed to add collaborator'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _format_business_plan(self, plan, user, detailed=False):
        """Format business plan for API response"""
        if not plan:
            return {}
        
        # Basic plan data
        plan_data = {
            'id': str(plan.id),
            'title': plan.title,
            'description': plan.description,
            'industry': getattr(plan, 'industry', ''),
            'stage': getattr(plan, 'stage', 'idea'),
            'status': getattr(plan, 'status', 'draft'),
            'created_at': plan.created_at.isoformat() if hasattr(plan, 'created_at') else None,
            'updated_at': plan.updated_at.isoformat() if hasattr(plan, 'updated_at') else None,
            'completion_percentage': self._calculate_completion_percentage(plan),
            'owner': {
                'id': str(plan.owner.id),
                'username': plan.owner.username,
                'full_name': plan.owner.get_full_name()
            }
        }
        
        # Add collaborators
        if hasattr(plan, 'collaborators'):
            collaborators = []
            for collab in plan.collaborators.all():
                collaborators.append({
                    'id': str(collab.id),
                    'user': {
                        'id': str(collab.user.id),
                        'username': collab.user.username,
                        'full_name': collab.user.get_full_name()
                    },
                    'role': getattr(collab, 'role', 'collaborator'),
                    'permissions': getattr(collab, 'permissions', ['read'])
                })
            plan_data['collaborators'] = collaborators
        else:
            plan_data['collaborators'] = []
        
        # Add detailed information if requested
        if detailed:
            plan_data['sections'] = getattr(plan, 'sections', {})
            plan_data['template'] = None
            if hasattr(plan, 'template') and plan.template:
                plan_data['template'] = {
                    'id': str(plan.template.id),
                    'name': plan.template.name
                }
        
        return plan_data

    def _calculate_completion_percentage(self, plan):
        """Calculate business plan completion percentage"""
        if not hasattr(plan, 'sections') or not plan.sections:
            return 0
        
        sections = plan.sections if isinstance(plan.sections, dict) else {}
        total_sections = 10  # Assume 10 standard sections
        completed_sections = sum(1 for section in sections.values() if section and str(section).strip())
        
        return min(100, int((completed_sections / total_sections) * 100))

    def _can_view_plan(self, user, plan):
        """Check if user can view the business plan"""
        if plan.owner == user:
            return True
        
        if self._is_admin(user):
            return True
        
        if hasattr(plan, 'collaborators') and plan.collaborators.filter(user=user).exists():
            return True
        
        if plan.status == 'published':
            return True
        
        return False

    def _can_edit_plan(self, user, plan):
        """Check if user can edit the business plan"""
        if plan.owner == user:
            return True
        
        if self._is_admin(user):
            return True
        
        if hasattr(plan, 'collaborators'):
            collab = plan.collaborators.filter(user=user).first()
            if collab and 'write' in getattr(collab, 'permissions', []):
                return True
        
        return False

    def _can_create_plan(self, user):
        """Check if user can create business plans"""
        user_profile = getattr(user, 'profile', None)
        user_role = getattr(user_profile, 'role', 'user') if user_profile else 'user'
        
        return user_role in ['entrepreneur', 'mentor', 'investor', 'admin', 'super_admin']

    def _can_manage_collaborators(self, user, plan):
        """Check if user can manage collaborators"""
        if plan.owner == user:
            return True
        
        if self._is_admin(user):
            return True
        
        if hasattr(plan, 'collaborators'):
            collab = plan.collaborators.filter(user=user).first()
            if collab and 'manage_collaborators' in getattr(collab, 'permissions', []):
                return True
        
        return False

    def _is_admin(self, user):
        """Check if user is admin"""
        user_profile = getattr(user, 'profile', None)
        user_role = getattr(user_profile, 'role', 'user') if user_profile else 'user'
        return user_role in ['admin', 'super_admin']
