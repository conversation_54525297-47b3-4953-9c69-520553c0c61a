"""
🎯 AI MODELS MIGRATION COMMAND
Safely migrates existing AI model data to consolidated structure

This command:
1. Validates existing data integrity
2. Creates backup of existing data
3. Migrates data to consolidated models
4. Verifies migration success
5. Provides rollback capability

Usage:
    python manage.py migrate_ai_models --dry-run    # Preview migration
    python manage.py migrate_ai_models --backup     # Create backup only
    python manage.py migrate_ai_models --migrate    # Perform migration
    python manage.py migrate_ai_models --verify     # Verify migration
    python manage.py migrate_ai_models --rollback   # Rollback migration
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction, connection
from django.apps import apps
import json
import os
from datetime import datetime


class Command(BaseCommand):
    help = '🎯 Migrate AI models to consolidated structure'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Preview migration without making changes',
        )
        parser.add_argument(
            '--backup',
            action='store_true',
            help='Create backup of existing data',
        )
        parser.add_argument(
            '--migrate',
            action='store_true',
            help='Perform the migration',
        )
        parser.add_argument(
            '--verify',
            action='store_true',
            help='Verify migration success',
        )
        parser.add_argument(
            '--rollback',
            action='store_true',
            help='Rollback migration',
        )

    def handle(self, *args, **options):
        """Main command handler"""
        
        if options['dry_run']:
            self.dry_run_migration()
        elif options['backup']:
            self.create_backup()
        elif options['migrate']:
            self.perform_migration()
        elif options['verify']:
            self.verify_migration()
        elif options['rollback']:
            self.rollback_migration()
        else:
            self.stdout.write(
                self.style.ERROR(
                    'Please specify an action: --dry-run, --backup, --migrate, --verify, or --rollback'
                )
            )

    def dry_run_migration(self):
        """Preview migration without making changes"""
        self.stdout.write(self.style.SUCCESS('🔍 DRY RUN: AI Models Migration Preview'))
        
        # Check existing data
        self.stdout.write('\n📊 Analyzing existing data...')
        
        try:
            # Check core models
            core_models = apps.get_app_config('core').get_models()
            ai_core_models = apps.get_app_config('ai_core').get_models()
            ai_recommendations_models = apps.get_app_config('ai_recommendations').get_models()
            ai_models_models = apps.get_app_config('ai_models').get_models()
            
            total_records = 0
            
            for model in core_models:
                if 'AI' in model.__name__:
                    count = model.objects.count()
                    total_records += count
                    self.stdout.write(f'  📋 {model.__name__}: {count} records')
            
            for model in ai_core_models:
                count = model.objects.count()
                total_records += count
                self.stdout.write(f'  📋 {model.__name__}: {count} records')
            
            for model in ai_recommendations_models:
                count = model.objects.count()
                total_records += count
                self.stdout.write(f'  📋 {model.__name__}: {count} records')
            
            for model in ai_models_models:
                count = model.objects.count()
                total_records += count
                self.stdout.write(f'  📋 {model.__name__}: {count} records')
            
            self.stdout.write(f'\n📈 Total records to migrate: {total_records}')
            
            # Check for potential issues
            self.stdout.write('\n🔍 Checking for potential issues...')
            
            # Check for foreign key constraints
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT table_name, constraint_name 
                    FROM information_schema.table_constraints 
                    WHERE constraint_type = 'FOREIGN KEY' 
                    AND table_name LIKE '%ai%'
                """)
                constraints = cursor.fetchall()
                
                if constraints:
                    self.stdout.write('  ⚠️  Foreign key constraints found:')
                    for table, constraint in constraints:
                        self.stdout.write(f'    - {table}.{constraint}')
                else:
                    self.stdout.write('  ✅ No foreign key constraint issues detected')
            
            self.stdout.write('\n✅ Dry run completed successfully!')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Dry run failed: {str(e)}')
            )

    def create_backup(self):
        """Create backup of existing data"""
        self.stdout.write(self.style.SUCCESS('💾 Creating backup of existing AI data...'))
        
        backup_dir = f'ai_migration_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
        os.makedirs(backup_dir, exist_ok=True)
        
        try:
            # Backup each model's data
            models_to_backup = [
                ('core', ['AIConfiguration', 'AIUsageLog', 'AIServiceStatus', 'AIRateLimit']),
                ('ai_core', ['AISession', 'AIInteraction']),
                ('ai_recommendations', ['AIRecommendation', 'AIRecommendationFeedback']),
                ('ai_models', ['AIModelMetadata', 'PredictionLog']),
            ]
            
            for app_name, model_names in models_to_backup:
                app_config = apps.get_app_config(app_name)
                
                for model_name in model_names:
                    try:
                        model = app_config.get_model(model_name)
                        data = list(model.objects.values())
                        
                        backup_file = os.path.join(backup_dir, f'{app_name}_{model_name}.json')
                        with open(backup_file, 'w') as f:
                            json.dump(data, f, indent=2, default=str)
                        
                        self.stdout.write(f'  ✅ Backed up {model_name}: {len(data)} records')
                        
                    except Exception as e:
                        self.stdout.write(f'  ⚠️  Could not backup {model_name}: {str(e)}')
            
            self.stdout.write(f'\n💾 Backup created in: {backup_dir}')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Backup failed: {str(e)}')
            )

    def perform_migration(self):
        """Perform the actual migration"""
        self.stdout.write(self.style.SUCCESS('🚀 Performing AI models migration...'))
        
        try:
            with transaction.atomic():
                # Since we're using db_table to preserve original table names,
                # the migration should be seamless
                self.stdout.write('  📋 Models are configured to use existing table names')
                self.stdout.write('  ✅ Migration uses existing data in place')
                self.stdout.write('  🔄 No data movement required')
                
                # Verify the consolidated models can access existing data
                from core.ai.models import (
                    AIConfiguration, AIUsageLog, AIServiceStatus, AIRateLimit,
                    AISession, AIInteraction, AIRecommendation, AIRecommendationFeedback,
                    AIModelMetadata, PredictionLog
                )
                
                # Test access to each model
                models_to_test = [
                    ('AIConfiguration', AIConfiguration),
                    ('AIUsageLog', AIUsageLog),
                    ('AIServiceStatus', AIServiceStatus),
                    ('AIRateLimit', AIRateLimit),
                    ('AISession', AISession),
                    ('AIInteraction', AIInteraction),
                    ('AIRecommendation', AIRecommendation),
                    ('AIRecommendationFeedback', AIRecommendationFeedback),
                    ('AIModelMetadata', AIModelMetadata),
                    ('PredictionLog', PredictionLog),
                ]
                
                for model_name, model_class in models_to_test:
                    try:
                        count = model_class.objects.count()
                        self.stdout.write(f'  ✅ {model_name}: {count} records accessible')
                    except Exception as e:
                        self.stdout.write(f'  ❌ {model_name}: Error - {str(e)}')
                        raise
                
                self.stdout.write('\n🎯 Migration completed successfully!')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Migration failed: {str(e)}')
            )
            raise

    def verify_migration(self):
        """Verify migration success"""
        self.stdout.write(self.style.SUCCESS('🔍 Verifying AI models migration...'))
        
        try:
            from core.ai.models import (
                AIConfiguration, AIUsageLog, AIServiceStatus, AIRateLimit,
                AISession, AIInteraction, AIRecommendation, AIRecommendationFeedback,
                AIModelMetadata, PredictionLog
            )
            
            # Verify each model
            models_to_verify = [
                ('AIConfiguration', AIConfiguration),
                ('AIUsageLog', AIUsageLog),
                ('AIServiceStatus', AIServiceStatus),
                ('AIRateLimit', AIRateLimit),
                ('AISession', AISession),
                ('AIInteraction', AIInteraction),
                ('AIRecommendation', AIRecommendation),
                ('AIRecommendationFeedback', AIRecommendationFeedback),
                ('AIModelMetadata', AIModelMetadata),
                ('PredictionLog', PredictionLog),
            ]
            
            total_records = 0
            
            for model_name, model_class in models_to_verify:
                try:
                    count = model_class.objects.count()
                    total_records += count
                    
                    # Test basic operations
                    if count > 0:
                        first_record = model_class.objects.first()
                        self.stdout.write(f'  ✅ {model_name}: {count} records, first ID: {first_record.id}')
                    else:
                        self.stdout.write(f'  ✅ {model_name}: {count} records (empty table)')
                        
                except Exception as e:
                    self.stdout.write(f'  ❌ {model_name}: Verification failed - {str(e)}')
                    raise
            
            self.stdout.write(f'\n📊 Total verified records: {total_records}')
            self.stdout.write('✅ Migration verification completed successfully!')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Verification failed: {str(e)}')
            )

    def rollback_migration(self):
        """Rollback migration if needed"""
        self.stdout.write(self.style.WARNING('⚠️  Rollback not needed for this migration'))
        self.stdout.write('   Since we use existing table names, no data was moved.')
        self.stdout.write('   Simply revert to the original model imports to rollback.')
        self.stdout.write('   Backup data is available if needed for restoration.')
