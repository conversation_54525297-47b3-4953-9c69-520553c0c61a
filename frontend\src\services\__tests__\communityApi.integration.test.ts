/**
 * Integration Tests for Community API
 * Tests real API interactions with mock server responses
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { rest } from 'msw';
import { setupServer } from 'msw/node';
import * as communityApi from '../communityApi';

// Mock server setup
const server = setupServer(
  // Posts endpoints
  rest.get('http://localhost:8000/api/community/posts/', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json([
        {
          id: '1',
          title: 'Test Post 1',
          content: 'Test content 1',
          author: { id: 1, name: 'Author 1' },
          created_at: '2024-01-01T00:00:00Z',
          likes_count: 5,
          comments_count: 2,
          is_liked: false,
          is_saved: false,
          hashtags: ['test', 'api'],
        },
        {
          id: '2',
          title: 'Test Post 2',
          content: 'Test content 2',
          author: { id: 2, name: 'Author 2' },
          created_at: '2024-01-02T00:00:00Z',
          likes_count: 3,
          comments_count: 1,
          is_liked: true,
          is_saved: false,
          hashtags: ['integration'],
        },
      ])
    );
  }),

  rest.post('http://localhost:8000/api/community/posts/', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        id: '3',
        title: 'New Post',
        content: 'New content',
        author: { id: 1, name: 'Test User' },
        created_at: '2024-01-03T00:00:00Z',
        likes_count: 0,
        comments_count: 0,
        is_liked: false,
        is_saved: false,
        hashtags: ['new'],
      })
    );
  }),

  rest.get('http://localhost:8000/api/community/posts/search/', (req, res, ctx) => {
    const query = req.url.searchParams.get('q');
    if (query === 'test') {
      return res(
        ctx.status(200),
        ctx.json([
          {
            id: '1',
            title: 'Test Post 1',
            content: 'Test content 1',
            author: { id: 1, name: 'Author 1' },
            created_at: '2024-01-01T00:00:00Z',
            likes_count: 5,
            comments_count: 2,
            is_liked: false,
            is_saved: false,
            hashtags: ['test'],
          },
        ])
      );
    }
    return res(ctx.status(200), ctx.json([]));
  }),

  // Stats endpoint
  rest.get('http://localhost:8000/api/community/stats/current/', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        total_posts: 10,
        total_users: 5,
        posts_today: 2,
        active_users: 3,
        trending_hashtags: [
          { tag: 'test', count: 5 },
          { tag: 'api', count: 3 },
        ],
      })
    );
  }),

  // Post interactions
  rest.post('http://localhost:8000/api/community/posts/:id/like/', (req, res, ctx) => {
    return res(ctx.status(200), ctx.json({ success: true }));
  }),

  rest.post('http://localhost:8000/api/community/posts/:id/comment/', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        id: 'comment-1',
        content: 'Test comment',
        author: { id: 1, name: 'Test User' },
        created_at: '2024-01-03T00:00:00Z',
      })
    );
  }),

  rest.post('http://localhost:8000/api/community/posts/:id/share/', (req, res, ctx) => {
    return res(ctx.status(200), ctx.json({ success: true }));
  }),

  rest.post('http://localhost:8000/api/community/posts/:id/save/', (req, res, ctx) => {
    return res(ctx.status(200), ctx.json({ success: true }));
  }),

  // Health check
  rest.get('http://localhost:8000/api/community/ping/', (req, res, ctx) => {
    return res(ctx.status(200), ctx.json({ status: 'ok', timestamp: Date.now() }));
  }),

  // Hashtags
  rest.get('http://localhost:8000/api/community/hashtags/trending/', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json([
        { tag: 'test', count: 5 },
        { tag: 'api', count: 3 },
        { tag: 'integration', count: 2 },
      ])
    );
  }),

  // User recommendations
  rest.get('http://localhost:8000/api/community/users/recommendations/', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json([
        {
          id: 3,
          name: 'Recommended User 1',
          avatar: null,
          followers_count: 10,
          posts_count: 5,
        },
        {
          id: 4,
          name: 'Recommended User 2',
          avatar: null,
          followers_count: 8,
          posts_count: 3,
        },
      ])
    );
  }),

  // Error scenarios
  rest.get('http://localhost:8000/api/community/error-test/', (req, res, ctx) => {
    return res(ctx.status(500), ctx.json({ error: 'Internal server error' }));
  }),

  rest.get('http://localhost:8000/api/community/network-error/', (req, res, ctx) => {
    return res.networkError('Network connection failed');
  }),
);

describe('Community API Integration Tests', () => {
  beforeEach(() => {
    server.listen({ onUnhandledRequest: 'error' });
  });

  afterEach(() => {
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
  });

  describe('Posts API', () => {
    it('should fetch posts successfully', async () => {
      const posts = await communityApi.getPosts();

      expect(posts).toHaveLength(2);
      expect(posts[0]).toMatchObject({
        id: '1',
        title: 'Test Post 1',
        content: 'Test content 1',
        author: { id: 1, name: 'Author 1' },
        likes_count: 5,
        comments_count: 2,
        is_liked: false,
        is_saved: false,
      });
    });

    it('should create a new post successfully', async () => {
      const postData = {
        title: 'New Post',
        content: 'New content',
        hashtags: ['new'],
      };

      const newPost = await communityApi.createPost(postData);

      expect(newPost).toMatchObject({
        id: '3',
        title: 'New Post',
        content: 'New content',
        author: { id: 1, name: 'Test User' },
        likes_count: 0,
        comments_count: 0,
        is_liked: false,
        is_saved: false,
      });
    });

    it('should search posts successfully', async () => {
      const results = await communityApi.searchPosts('test');

      expect(results).toHaveLength(1);
      expect(results[0]).toMatchObject({
        id: '1',
        title: 'Test Post 1',
        hashtags: ['test'],
      });
    });

    it('should return empty results for non-matching search', async () => {
      const results = await communityApi.searchPosts('nonexistent');

      expect(results).toHaveLength(0);
    });
  });

  describe('Post Interactions', () => {
    it('should like a post successfully', async () => {
      const result = await communityApi.likePost('1');

      expect(result).toEqual({ success: true });
    });

    it('should comment on a post successfully', async () => {
      const comment = await communityApi.commentPost('1', 'Test comment');

      expect(comment).toMatchObject({
        id: 'comment-1',
        content: 'Test comment',
        author: { id: 1, name: 'Test User' },
      });
    });

    it('should share a post successfully', async () => {
      const result = await communityApi.sharePost('1');

      expect(result).toEqual({ success: true });
    });

    it('should save a post successfully', async () => {
      const result = await communityApi.savePost('1');

      expect(result).toEqual({ success: true });
    });
  });

  describe('Stats API', () => {
    it('should fetch community stats successfully', async () => {
      const stats = await communityApi.getStats();

      expect(stats).toMatchObject({
        total_posts: 10,
        total_users: 5,
        posts_today: 2,
        active_users: 3,
        trending_hashtags: [
          { tag: 'test', count: 5 },
          { tag: 'api', count: 3 },
        ],
      });
    });
  });

  describe('Health Check', () => {
    it('should ping successfully', async () => {
      const result = await communityApi.ping();

      expect(result).toMatchObject({
        status: 'ok',
        timestamp: expect.any(Number),
      });
    });
  });

  describe('Hashtags API', () => {
    it('should fetch trending hashtags successfully', async () => {
      const hashtags = await communityApi.getTrendingHashtags();

      expect(hashtags).toHaveLength(3);
      expect(hashtags[0]).toMatchObject({
        tag: 'test',
        count: 5,
      });
    });
  });

  describe('User Recommendations', () => {
    it('should fetch user recommendations successfully', async () => {
      const users = await communityApi.getUserRecommendations({
        activity_level: 'high',
        verification_status: 'all',
      });

      expect(users).toHaveLength(2);
      expect(users[0]).toMatchObject({
        id: 3,
        name: 'Recommended User 1',
        followers_count: 10,
        posts_count: 5,
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle server errors gracefully', async () => {
      server.use(
        rest.get('http://localhost:8000/api/community/posts/', (req, res, ctx) => {
          return res(ctx.status(500), ctx.json({ error: 'Internal server error' }));
        })
      );

      await expect(communityApi.getPosts()).rejects.toThrow();
    });

    it('should handle network errors gracefully', async () => {
      server.use(
        rest.get('http://localhost:8000/api/community/posts/', (req, res, ctx) => {
          return res.networkError('Network connection failed');
        })
      );

      await expect(communityApi.getPosts()).rejects.toThrow();
    });

    it('should handle authentication errors', async () => {
      server.use(
        rest.post('http://localhost:8000/api/community/posts/:id/like/', (req, res, ctx) => {
          return res(ctx.status(401), ctx.json({ error: 'Authentication required' }));
        })
      );

      await expect(communityApi.likePost('1')).rejects.toThrow();
    });

    it('should handle rate limiting', async () => {
      server.use(
        rest.get('http://localhost:8000/api/community/posts/', (req, res, ctx) => {
          return res(ctx.status(429), ctx.json({ error: 'Rate limit exceeded' }));
        })
      );

      await expect(communityApi.getPosts()).rejects.toThrow();
    });
  });

  describe('Request Deduplication', () => {
    it('should handle concurrent requests properly', async () => {
      const promises = [
        communityApi.getPosts(),
        communityApi.getPosts(),
        communityApi.getPosts(),
      ];

      const results = await Promise.all(promises);

      // All requests should return the same data
      expect(results[0]).toEqual(results[1]);
      expect(results[1]).toEqual(results[2]);
    });
  });

  describe('Performance', () => {
    it('should complete requests within reasonable time', async () => {
      const startTime = Date.now();
      
      await communityApi.getPosts();
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within 1 second
      expect(duration).toBeLessThan(1000);
    });

    it('should handle large datasets efficiently', async () => {
      // Mock large dataset
      server.use(
        rest.get('http://localhost:8000/api/community/posts/', (req, res, ctx) => {
          const largePosts = Array.from({ length: 100 }, (_, i) => ({
            id: `${i + 1}`,
            title: `Post ${i + 1}`,
            content: `Content for post ${i + 1}`,
            author: { id: 1, name: 'Author' },
            created_at: new Date().toISOString(),
            likes_count: Math.floor(Math.random() * 100),
            comments_count: Math.floor(Math.random() * 20),
            is_liked: false,
            is_saved: false,
            hashtags: [`tag${i % 10}`],
          }));

          return res(ctx.status(200), ctx.json(largePosts));
        })
      );

      const startTime = Date.now();
      const posts = await communityApi.getPosts();
      const endTime = Date.now();

      expect(posts).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(2000); // Should handle 100 posts within 2 seconds
    });
  });
});
