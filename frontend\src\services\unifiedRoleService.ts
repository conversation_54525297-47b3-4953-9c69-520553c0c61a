/**
 * 🎯 UNIFIED ROLE SERVICE
 * Single source of truth for all role-related operations in the frontend
 * 
 * This service eliminates conflicts between:
 * - Multiple role hooks (useRoles vs useUnifiedRoles)
 * - Inconsistent role checking patterns
 * - Conflicting navigation systems
 * - Dashboard role inconsistencies
 */

import { 
  ROLE_HIERARCHY, 
  PERMISSION_HIERARCHY, 
  ROLE_CAPABILITIES, 
  getRoleCapabilities,
  canUserAccess,
  getUserSidebarType,
  getMaxAIRequests,
  getRoleNavigation,
  type RoleCapabilities,
  type NavigationItem
} from '../config/roleConfig';

export interface UnifiedRoleInfo {
  primaryRole: string;
  isAuthenticated: boolean;
  capabilities: RoleCapabilities;
  sidebarType: 'full' | 'minimal' | 'none';
  maxAIRequests: number;
  navigation: NavigationItem[];
  accessibleRoutes: string[];
}

export class UnifiedRoleService {
  private static instance: UnifiedRoleService;
  
  public static getInstance(): UnifiedRoleService {
    if (!UnifiedRoleService.instance) {
      UnifiedRoleService.instance = new UnifiedRoleService();
    }
    return UnifiedRoleService.instance;
  }

  /**
   * Get comprehensive role information for a user
   */
  public getRoleInfo(user: any, isAuthenticated: boolean): UnifiedRoleInfo {
    const primaryRole = this.extractPrimaryRole(user, isAuthenticated);
    const capabilities = getRoleCapabilities(primaryRole);
    
    return {
      primaryRole,
      isAuthenticated,
      capabilities,
      sidebarType: getUserSidebarType(primaryRole),
      maxAIRequests: getMaxAIRequests(primaryRole),
      navigation: getRoleNavigation(primaryRole),
      accessibleRoutes: this.getAccessibleRoutes(primaryRole)
    };
  }

  /**
   * Extract primary role from user object with consistent logic
   * ✅ FIXED: Properly reads backend UserSerializer structure
   */
  private extractPrimaryRole(user: any, isAuthenticated: boolean): string {
    if (!isAuthenticated || !user) {
      console.log('🔍 Role extraction: User not authenticated or missing');
      return 'user'; // Default for unauthenticated users
    }

    try {
      console.log('🔍 Role extraction - User object:', {
        user_role: user.user_role,
        is_superuser: user.is_superuser,
        is_staff: user.is_staff,
        profile: user.profile ? {
          active_roles: user.profile.active_roles,
          highest_permission_level: user.profile.highest_permission_level
        } : null
      });

      // ✅ FIXED: Check user_role field first (from backend UserSerializer.get_user_role)
      if (user.user_role && typeof user.user_role === 'string') {
        console.log('✅ Role extraction: Found user_role =', user.user_role);
        return user.user_role;
      }

      // Check Django built-in roles (these are reliable)
      if (user.is_superuser) {
        console.log('✅ Role extraction: User is superuser');
        return 'super_admin';
      }
      if (user.is_staff) {
        console.log('✅ Role extraction: User is staff');
        return 'admin';
      }

      // ✅ FIXED: Check profile.active_roles array (from UserProfileSerializer.get_active_roles)
      if (user.profile && user.profile.active_roles && Array.isArray(user.profile.active_roles)) {
        const activeRoles = user.profile.active_roles;
        if (activeRoles.length > 0) {
          // Get the highest priority role from active roles
          const highestRole = this.getHighestRoleFromArray(activeRoles);
          console.log('✅ Role extraction: Found active roles, highest =', highestRole);
          return highestRole;
        }
      }

      // Legacy support for old user object structures
      if (user.role) return user.role;
      if (user.primary_role) return user.primary_role;
      if (user.roles && user.roles.length > 0) {
        return this.getHighestRole(user.roles);
      }

      console.log('⚠️ Role extraction: No role found, defaulting to user');
      return 'user'; // Default fallback
    } catch (error) {
      console.error('❌ Role extraction error:', error);
      return 'user'; // Safe fallback on error
    }
  }

  /**
   * Get the highest role from a list of roles
   */
  private getHighestRole(roles: string[]): string {
    let highestRole = 'user';
    let highestLevel = 0;

    for (const role of roles) {
      const level = ROLE_HIERARCHY[role] || 0;
      if (level > highestLevel) {
        highestLevel = level;
        highestRole = role;
      }
    }

    return highestRole;
  }

  /**
   * ✅ NEW: Get highest role from array of role objects (from backend active_roles)
   */
  private getHighestRoleFromArray(roleObjects: any[]): string {
    const hierarchy = ROLE_HIERARCHY;
    let highestRole = 'user';
    let highestLevel = 0;

    for (const roleObj of roleObjects) {
      // Handle both string roles and role objects
      const roleName = typeof roleObj === 'string' ? roleObj : roleObj.name;
      if (roleName) {
        const level = hierarchy[roleName] || 0;
        if (level > highestLevel) {
          highestLevel = level;
          highestRole = roleName;
        }
      }
    }

    return highestRole;
  }

  /**
   * Check if user has a specific capability
   */
  public hasCapability(role: string, capability: keyof RoleCapabilities): boolean {
    return canUserAccess(role, capability);
  }

  /**
   * Check if user can access a specific route
   */
  public canAccessRoute(role: string, route: string): boolean {
    const accessibleRoutes = this.getAccessibleRoutes(role);
    return accessibleRoutes.includes(route) || 
           accessibleRoutes.some(allowedRoute => route.startsWith(allowedRoute));
  }

  /**
   * Get accessible routes for a role
   */
  private getAccessibleRoutes(role: string): string[] {
    const capabilities = getRoleCapabilities(role);
    const routes: string[] = [];

    // Public routes (always accessible)
    routes.push('/', '/community', '/about', '/contact');

    // Authenticated user routes
    if (role !== 'user' || capabilities.canAccessAI) {
      routes.push('/profile', '/settings');
    }

    // User-specific routes (NO dashboard)
    if (role === 'user') {
      routes.push('/user/home', '/user/profile', '/user/ai-chat');
      return routes; // Users don't get any other routes
    }

    // Business role routes (dashboard access)
    if (capabilities.canAccessDashboard) {
      routes.push(`/${role}/dashboard`);
    }

    if (capabilities.canCreateBusinessPlans) {
      routes.push(`/${role}/business-plans`, `/${role}/business-ideas`);
    }

    if (capabilities.canMentor) {
      routes.push(`/${role}/mentorship`, `/${role}/mentees`);
    }

    if (capabilities.canInvest) {
      routes.push(`/${role}/investments`, `/${role}/portfolio`);
    }

    if (capabilities.canModerateContent) {
      routes.push(`/${role}/moderation`, `/${role}/reports`);
    }

    if (capabilities.canManageUsers) {
      routes.push(`/${role}/users`, `/${role}/user-management`);
    }

    if (capabilities.canAccessSystemSettings) {
      routes.push(`/${role}/system`, `/${role}/settings`);
    }

    if (capabilities.canAccessAnalytics) {
      routes.push(`/${role}/analytics`);
    }

    return routes;
  }

  /**
   * Get dashboard route for a role
   */
  public getDashboardRoute(role: string): string {
    const capabilities = getRoleCapabilities(role);
    
    if (!capabilities.canAccessDashboard) {
      // Users don't have dashboard access
      return role === 'user' ? '/user/home' : '/';
    }
    
    return `/${role}/dashboard`;
  }

  /**
   * Get home route for a role
   */
  public getHomeRoute(role: string): string {
    if (role === 'user') {
      return '/user/home';
    }
    
    const capabilities = getRoleCapabilities(role);
    return capabilities.canAccessDashboard ? `/${role}/dashboard` : '/';
  }

  /**
   * Validate role consistency across the application
   */
  public validateRoleConsistency(): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    // Check if all roles in ROLE_CAPABILITIES have corresponding navigation
    for (const role of Object.keys(ROLE_CAPABILITIES)) {
      try {
        getRoleNavigation(role);
      } catch (error) {
        issues.push(`Missing navigation for role: ${role}`);
      }
    }

    // Check if dashboard roles are consistent
    const dashboardRoles = Object.keys(ROLE_CAPABILITIES).filter(
      role => ROLE_CAPABILITIES[role].canAccessDashboard
    );

    if (dashboardRoles.includes('user')) {
      issues.push('User role should not have dashboard access');
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }

  /**
   * ✅ NEW: Test authentication flow integration
   * This method helps debug role extraction issues
   */
  public testAuthFlowIntegration(user: any, isAuthenticated: boolean): {
    success: boolean;
    extractedRole: string;
    expectedRole?: string;
    issues: string[];
    userStructure: any;
  } {
    const issues: string[] = [];

    console.log('🧪 Testing auth flow integration...');

    // Test role extraction
    const extractedRole = this.extractPrimaryRole(user, isAuthenticated);

    // Analyze user structure
    const userStructure = {
      hasUserRole: !!user?.user_role,
      userRoleValue: user?.user_role,
      hasProfile: !!user?.profile,
      hasActiveRoles: !!(user?.profile?.active_roles?.length),
      activeRolesCount: user?.profile?.active_roles?.length || 0,
      isDjangoSuperuser: !!user?.is_superuser,
      isDjangoStaff: !!user?.is_staff,
      isAuthenticated
    };

    // Validate extraction logic
    if (!isAuthenticated && extractedRole !== 'user') {
      issues.push('Unauthenticated user should have "user" role');
    }

    if (isAuthenticated && user?.is_superuser && extractedRole !== 'super_admin') {
      issues.push('Superuser should have "super_admin" role');
    }

    if (isAuthenticated && user?.is_staff && !user?.is_superuser && extractedRole !== 'admin') {
      issues.push('Staff user should have "admin" role');
    }

    console.log('🧪 Auth flow test results:', {
      extractedRole,
      userStructure,
      issues
    });

    return {
      success: issues.length === 0,
      extractedRole,
      issues,
      userStructure
    };
  }
}

// Export singleton instance
export const unifiedRoleService = UnifiedRoleService.getInstance();
