# Generated by Django for performance optimization

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('incubator', '0001_initial'),
    ]

    operations = [
        # Add critical missing indexes for BusinessIdea
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_incubator_businessidea_owner_id ON incubator_businessidea(owner_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_incubator_businessidea_owner_id;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_incubator_businessidea_created_at ON incubator_businessidea(created_at DESC);",
            reverse_sql="DROP INDEX IF EXISTS idx_incubator_businessidea_created_at;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_incubator_businessidea_moderation_status ON incubator_businessidea(moderation_status);",
            reverse_sql="DROP INDEX IF EXISTS idx_incubator_businessidea_moderation_status;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_incubator_businessidea_current_stage ON incubator_businessidea(current_stage);",
            reverse_sql="DROP INDEX IF EXISTS idx_incubator_businessidea_current_stage;"
        ),
        
        # Add indexes for ProgressUpdate
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_incubator_progressupdate_business_idea_id ON incubator_progressupdate(business_idea_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_incubator_progressupdate_business_idea_id;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_incubator_progressupdate_created_by_id ON incubator_progressupdate(created_by_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_incubator_progressupdate_created_by_id;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_incubator_progressupdate_created_at ON incubator_progressupdate(created_at DESC);",
            reverse_sql="DROP INDEX IF EXISTS idx_incubator_progressupdate_created_at;"
        ),
        
        # Add indexes for BusinessPlan
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_incubator_businessplan_business_idea_id ON incubator_businessplan(business_idea_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_incubator_businessplan_business_idea_id;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_incubator_businessplan_owner_id ON incubator_businessplan(owner_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_incubator_businessplan_owner_id;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_incubator_businessplan_status ON incubator_businessplan(status);",
            reverse_sql="DROP INDEX IF EXISTS idx_incubator_businessplan_status;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_incubator_businessplan_created_at ON incubator_businessplan(created_at DESC);",
            reverse_sql="DROP INDEX IF EXISTS idx_incubator_businessplan_created_at;"
        ),
    ]
