import React, { Suspense, lazy, useState, useEffect } from 'react';
import { Plus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../hooks/useLanguage';
import { useAuth } from '../hooks/useAuth';
import { useCommunity } from '../hooks/useCommunity';
import { usePostInteractions } from '../hooks/usePostInteractions';
import { useToast } from '../hooks/useToast';
import { useRenderTracking } from '../hooks/usePerformanceOptimization';
import { useAccessibility, usePageAnnouncement, useKeyboardShortcuts } from '../hooks/useAccessibility';
import { useIsMobile } from '../hooks/useIsMobile';

// Core components (loaded immediately)
import ToastContainer from '../components/ToastContainer';
import ErrorBoundary from '../components/ErrorBoundary';
import CommunityErrorBoundary from '../components/community/CommunityErrorBoundary';
import CommunityHeader from '../components/community/CommunityHeader';

// Lazy-loaded components for better performance
const PostsFeed = lazy(() => import('../components/community/PostsFeed'));
const CommunitySidebar = lazy(() => import('../components/community/CommunitySidebar'));
import {
  PostSkeleton,
  SidebarSkeleton,
  EmptyState
} from '../components/community/CommunityLoadingStates';
// import GuestOnboarding, { FeatureHighlight } from '../components/community/GuestOnboarding';

// Lazy-loaded components (loaded on demand)
const UserProfileModal = lazy(() => import('../components/UserProfileModal'));
const ContentModerationModal = lazy(() => import('../components/ContentModerationModal'));
const CreatePostModal = lazy(() => import('../components/CreatePostModal'));
const AdvancedSearch = lazy(() => import('../components/AdvancedSearch'));
const UserDiscovery = lazy(() =>
  import('../components/UserDiscovery').catch(() => ({
    default: () => (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-gray-900 rounded-2xl border border-white/10 p-6 text-center">
          <h2 className="text-xl font-bold text-white mb-2">User Discovery</h2>
          <p className="text-gray-400">This feature is temporarily unavailable.</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Reload Page
          </button>
        </div>
      </div>
    )
  }))
);
const PostAnalytics = lazy(() => import('../components/PostAnalytics'));

// Loading fallback component
const ModalLoadingFallback: React.FC = () => (
  <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
    <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl p-6">
      <div className="animate-spin w-8 h-8 border-4 border-purple-400 border-t-transparent rounded-full mx-auto mb-4"></div>
      <p className="text-white text-center">Loading...</p>
    </div>
  </div>
);

const CommunityPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { isAuthenticated } = useAuth();
  const { toasts, removeToast } = useToast();
  const { announce } = useAccessibility();
  const isMobile = useIsMobile();

  // Performance tracking
  useRenderTracking('CommunityPage');

  // Accessibility features
  usePageAnnouncement(
    t('community.page.title', 'Community'),
    t('community.page.description', 'Connect with Syrian entrepreneurs and innovators in the Yasmeen AI community')
  );

  // Keyboard shortcuts
  useKeyboardShortcuts({
    'ctrl+/': () => {
      const searchInput = document.getElementById('search');
      searchInput?.focus();
      announce(t('community.accessibility.searchFocused', 'Search input focused'));
    },
    'ctrl+n': () => {
      handleCreatePostClick();
      announce(t('community.accessibility.createPostOpened', 'Create post modal opened'));
    },
    'escape': () => {
      // Close any open modals
      if (showCreateModal) closeCreateModal();
      if (showAdvancedSearch) closeAdvancedSearch();
      if (showUserDiscovery) closeUserDiscovery();
      if (showPostAnalytics) closePostAnalytics();
    }
  });

  // Local state for onboarding - use useEffect to avoid hydration issues
  const [showOnboarding, setShowOnboarding] = useState(false);

  // Mobile-specific state
  const [showMobileSidebar, setShowMobileSidebar] = useState(false);

  // Initialize onboarding state after component mounts to avoid SSR issues
  useEffect(() => {
    if (!isAuthenticated && !localStorage.getItem('community-onboarding-seen')) {
      setShowOnboarding(true);
    }
  }, [isAuthenticated]);

  // Unified community hook for all business logic
  const {
    // Posts data
    posts,
    searchResults,
    activeView,
    searchQuery,
    isLoading,
    isSearching,
    computedStats,

    // Stats data
    stats,
    connectionStatus,

    // Actions
    handleViewChange,
    handleSearchChange,
    handleClearSearch,

    // Post operations
    handleLikePost,
    handleCreatePost,
    handleCommentPost,
    handleSharePost,
    handleSavePost,
    handleEditPost,
    handleDeletePost,
    handleReportPost,

    // Comment operations
    handleLikeComment,
    handleEditComment,
    handleDeleteComment,
    handleReportComment,

    // Modal states and actions
    showCreateModal,
    showAdvancedSearch,
    showUserDiscovery,
    showPostAnalytics,
    closeCreateModal,
    closeAdvancedSearch,
    closeUserDiscovery,
    closePostAnalytics,
    handleCreatePostClick,
    handleAdvancedSearchResults,
  } = useCommunity(
    (results) => {
      // Handle search results from advanced search
      // This would be implemented based on the search results structure
    }
  );

  const {
    showUserModal,
    selectedUser,
    showModerationModal,
    selectedContent,
    closeUserModal,
    closeModerationModal,
    handleModerationAction,
  } = usePostInteractions();

  // Announce content changes for screen readers
  useEffect(() => {
    if (posts.length > 0) {
      announce(
        t('community.accessibility.postsLoaded', 'Posts loaded: {{count}} posts available', { count: posts.length }),
        'polite'
      );
    }
  }, [posts.length, announce, t]);

  useEffect(() => {
    if (searchQuery && searchResults.length >= 0) {
      announce(
        t('community.accessibility.searchResults', 'Search results: {{count}} posts found', { count: searchResults.length }),
        'polite'
      );
    }
  }, [searchResults.length, searchQuery, announce, t]);

  // For unauthenticated users, show a banner encouraging login but still show public content
  const showGuestBanner = !isAuthenticated;

  // Handle onboarding close
  const handleOnboardingClose = () => {
    setShowOnboarding(false);
    localStorage.setItem('community-onboarding-seen', 'true');
  };

  return (
    <ErrorBoundary isRTL={isRTL}>
      <div
        className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 text-white"
        dir={isRTL ? 'rtl' : 'ltr'}
      >
        {/* Skip Links for Accessibility */}
        <div className="sr-only focus-within:not-sr-only">
          <a
            href="#main-content"
            className="absolute top-4 left-4 bg-purple-600 text-white px-4 py-2 rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-purple-400"
          >
            {t('community.navigation.skipToMain', 'Skip to main content')}
          </a>
          <a
            href="#search"
            className="absolute top-4 left-32 bg-purple-600 text-white px-4 py-2 rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-purple-400"
          >
            {t('community.navigation.skipToSearch', 'Skip to search')}
          </a>
          <div
            className="absolute top-4 left-64 bg-purple-600 text-white px-4 py-2 rounded-lg z-50"
            role="region"
            aria-label={t('community.accessibility.keyboardShortcuts', 'Keyboard shortcuts')}
          >
            <div className="text-xs">
              <div>Ctrl+/ : {t('community.accessibility.focusSearch', 'Focus search')}</div>
              <div>Ctrl+N : {t('community.accessibility.createPost', 'Create post')}</div>
              <div>Esc : {t('community.accessibility.closeModal', 'Close modal')}</div>
            </div>
          </div>
        </div>

        {/* Community Header */}
        <CommunityHeader
          searchQuery={searchQuery}
          onSearchChange={handleSearchChange}
          onClearSearch={handleClearSearch}
          onCreatePost={handleCreatePostClick}
          connectionStatus={connectionStatus}
          isSearching={isSearching}
          showGuestBanner={showGuestBanner}
        />



        {/* Main Content */}
        <main
          id="main-content"
          tabIndex={-1}
          role="main"
          aria-label={t('community.navigation.mainContent', 'Community main content')}
          data-testid="main-content"
        >
          <CommunityErrorBoundary t={t} isRTL={isRTL}>
            <div className={`${isMobile ? 'community-main-container' : 'community-desktop-grid'}`}>
            {isLoading && posts.length === 0 ? (
              // Initial loading state
              <div className={`${isMobile ? 'community-grid-layout' : 'contents'}`}>
                {!isMobile && (
                  <div className="community-sidebar-desktop">
                    <SidebarSkeleton />
                  </div>
                )}
                <div className={`${isMobile ? 'community-posts-mobile' : 'community-posts-desktop'}`}>
                  <PostSkeleton count={isMobile ? 2 : 3} />
                </div>
              </div>
            ) : (
              <div className={`${isMobile ? 'community-grid-layout' : 'contents'}`}>
                {/* Mobile Sidebar Toggle */}
                {isMobile && (
                  <div className="mb-4">
                    <button
                      onClick={() => setShowMobileSidebar(!showMobileSidebar)}
                      className="w-full bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg p-3 flex items-center justify-between text-white transition-colors"
                      aria-expanded={showMobileSidebar}
                      aria-controls="mobile-sidebar"
                    >
                      <span className="flex items-center gap-2">
                        <span>📊</span>
                        <span>{t('community.navigation.showStats', 'Community Stats')}</span>
                      </span>
                      <span className={`transform transition-transform ${showMobileSidebar ? 'rotate-180' : ''}`}>
                        ▼
                      </span>
                    </button>
                  </div>
                )}

                {/* Sidebar */}
                {(!isMobile || showMobileSidebar) && (
                  <aside
                    id="mobile-sidebar"
                    role="complementary"
                    aria-label={t('community.navigation.sidebar', 'Community sidebar')}
                    className={`${isMobile ? 'community-sidebar-mobile' : 'community-sidebar-desktop'}`}
                  >
                    <Suspense fallback={<SidebarSkeleton />}>
                      <CommunitySidebar
                        stats={stats}
                        onCreatePost={handleCreatePostClick}
                        isAuthenticated={isAuthenticated}
                      />
                    </Suspense>
                  </aside>
                )}

                {/* Posts Feed */}
                <section
                  className={`${isMobile ? 'community-posts-mobile' : 'community-posts-desktop'}`}
                  role="feed"
                  aria-label={t('community.navigation.postsFeed', 'Community posts feed')}
                  aria-live="polite"
                  aria-busy={isLoading}
                >
                  {/* Feature Highlight for Guests - Disabled for now */}
                  {/* <FeatureHighlight isAuthenticated={isAuthenticated} /> */}

                  {posts.length === 0 && !isLoading ? (
                    <EmptyState
                      icon="📝"
                      title={t('community.empty.noPosts', 'No posts yet')}
                      description={t('community.empty.noPostsDescription', 'Be the first to share something with the community!')}
                      action={{
                        label: t('community.actions.createPost', 'Create Post'),
                        onClick: handleCreatePostClick
                      }}
                    />
                  ) : (
                    <Suspense fallback={<PostSkeleton count={3} />}>
                      <PostsFeed
                        posts={posts}
                        searchResults={searchResults}
                        searchQuery={searchQuery}
                        isSearching={isSearching}
                        activeView={activeView}
                        isLoading={isLoading}
                        onViewChange={handleViewChange}
                        onLikePost={handleLikePost}
                        onCommentPost={handleCommentPost}
                        onSharePost={handleSharePost}
                        onSavePost={handleSavePost}
                        onEditPost={handleEditPost}
                        onDeletePost={handleDeletePost}
                        onReportPost={handleReportPost}
                        onLikeComment={handleLikeComment}
                        onEditComment={handleEditComment}
                        onDeleteComment={handleDeleteComment}
                        onReportComment={handleReportComment}
                        onCreatePost={handleCreatePostClick}
                        isAuthenticated={isAuthenticated}
                        computedStats={computedStats}
                      />
                    </Suspense>
                  )}
                </section>
              </div>
            )}
          </div>
        </CommunityErrorBoundary>
        </main>

        {/* Modals and Overlays with Lazy Loading */}
        {showCreateModal && (
          <Suspense fallback={<ModalLoadingFallback />}>
            <CreatePostModal
              isOpen={showCreateModal}
              onClose={closeCreateModal}
              onSubmit={handleCreatePost}
            />
          </Suspense>
        )}

        {showUserModal && selectedUser && (
          <Suspense fallback={<ModalLoadingFallback />}>
            <UserProfileModal
              user={selectedUser}
              isOpen={showUserModal}
              onClose={closeUserModal}
            />
          </Suspense>
        )}

        {showModerationModal && selectedContent && (
          <Suspense fallback={<ModalLoadingFallback />}>
            <ContentModerationModal
              isOpen={showModerationModal}
              onClose={closeModerationModal}
              content={selectedContent}
              onAction={handleModerationAction}
            />
          </Suspense>
        )}

        {showAdvancedSearch && (
          <Suspense fallback={<ModalLoadingFallback />}>
            <AdvancedSearch
              isRTL={isRTL}
              onSearchResults={handleAdvancedSearchResults}
              onClose={closeAdvancedSearch}
            />
          </Suspense>
        )}

        {showPostAnalytics && (
          <Suspense fallback={<ModalLoadingFallback />}>
            <PostAnalytics
              postId={showPostAnalytics}
              isOpen={!!showPostAnalytics}
              onClose={closePostAnalytics}
              isRTL={isRTL}
            />
          </Suspense>
        )}

        {showUserDiscovery && (
          <Suspense fallback={<ModalLoadingFallback />}>
            <UserDiscovery
              isOpen={showUserDiscovery}
              onClose={closeUserDiscovery}
              isRTL={isRTL}
            />
          </Suspense>
        )}

        {/* Mobile Floating Action Button */}
        {isMobile && (
          <button
            onClick={handleCreatePostClick}
            className="fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 rounded-full shadow-lg flex items-center justify-center z-50 transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-purple-400"
            aria-label={t('community.actions.createPost', 'Create a new post')}
          >
            <Plus className="w-6 h-6 text-white" />
          </button>
        )}

        {/* Toast Notifications */}
        <ToastContainer
          toasts={toasts}
          onRemove={removeToast}
          isRTL={isRTL}
        />

        {/* Guest Onboarding Modal - Disabled for now */}
        {/* <GuestOnboarding
          showWelcomeModal={showOnboarding}
          onClose={handleOnboardingClose}
        /> */}
      </div>
    </ErrorBoundary>
  );
};

export default React.memo(CommunityPage);
