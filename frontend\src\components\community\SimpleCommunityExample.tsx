import React, { useState, useEffect } from 'react';
import { SimpleCommentSection } from './SimpleCommentSection';
import { communityApi, CommunityComment } from '../../services/communityApi';
import { useAuth } from '../../hooks/useAuth';

interface SimpleCommunityExampleProps {
  postId: string;
}

/**
 * Example of how to use the simplified comment system
 * This shows the clean, simple approach without complex features
 */
export const SimpleCommunityExample: React.FC<SimpleCommunityExampleProps> = ({ postId }) => {
  const { isAuthenticated } = useAuth();
  const [comments, setComments] = useState<CommunityComment[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load comments when component mounts
  useEffect(() => {
    loadComments();
  }, [postId]);

  const loadComments = async () => {
    try {
      setIsLoading(true);
      const fetchedComments = await communityApi.getComments(postId);
      setComments(fetchedComments);
    } catch (error) {
      console.error('Failed to load comments:', error);
      setComments([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddComment = async (content: string) => {
    try {
      // Create the comment
      const newComment = await communityApi.createComment({
        post: postId,
        content: content
      });

      // Add to local state immediately for better UX
      setComments(prev => [...prev, newComment]);

      // Optionally reload all comments to ensure consistency
      // await loadComments();
    } catch (error) {
      console.error('Failed to add comment:', error);
      throw error; // Let the component handle the error display
    }
  };

  return (
    <div className="bg-gray-900 rounded-lg p-6">
      <h3 className="text-white text-lg font-semibold mb-4">
        Simple Comments Example
      </h3>
      
      <SimpleCommentSection
        postId={postId}
        comments={comments}
        onAddComment={handleAddComment}
        isLoading={isLoading}
      />
      
      {/* Debug info for development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-6 p-4 bg-gray-800 rounded text-xs text-gray-400">
          <strong>Debug Info:</strong>
          <br />
          Post ID: {postId}
          <br />
          Comments: {comments.length}
          <br />
          Authenticated: {isAuthenticated ? 'Yes' : 'No'}
          <br />
          Loading: {isLoading ? 'Yes' : 'No'}
        </div>
      )}
    </div>
  );
};

export default SimpleCommunityExample;
