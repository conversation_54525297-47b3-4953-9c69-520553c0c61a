/**
 * Progress Indicator Component
 * Provides visual feedback for loading states and progress tracking
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { CheckCircle, AlertCircle, Loader2, Clock, Wifi, WifiOff } from 'lucide-react';

interface ProgressStepProps {
  steps: Array<{
    id: string;
    label: string;
    status: 'pending' | 'loading' | 'completed' | 'error';
    description?: string;
  }>;
  currentStep?: string;
  className?: string;
}

export const ProgressSteps: React.FC<ProgressStepProps> = ({
  steps,
  currentStep,
  className = ''
}) => {
  const { t } = useTranslation();

  const getStepIcon = (status: string) => {
    switch (status) {
      case 'loading':
        return <Loader2 className="w-4 h-4 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'error':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const getStepColor = (status: string) => {
    switch (status) {
      case 'loading':
        return 'text-blue-400 bg-blue-400/20 border-blue-400/30';
      case 'completed':
        return 'text-green-400 bg-green-400/20 border-green-400/30';
      case 'error':
        return 'text-red-400 bg-red-400/20 border-red-400/30';
      default:
        return 'text-gray-400 bg-gray-400/20 border-gray-400/30';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {steps.map((step, index) => (
        <div key={step.id} className="flex items-start gap-3">
          <div className={`flex-shrink-0 w-8 h-8 rounded-full border-2 flex items-center justify-center ${getStepColor(step.status)}`}>
            {getStepIcon(step.status)}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <h4 className={`font-medium ${step.status === 'completed' ? 'text-green-400' : step.status === 'error' ? 'text-red-400' : 'text-white'}`}>
                {step.label}
              </h4>
              {step.status === 'loading' && (
                <div className="flex space-x-1">
                  <div className="w-1 h-1 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                  <div className="w-1 h-1 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                  <div className="w-1 h-1 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                </div>
              )}
            </div>
            {step.description && (
              <p className="text-sm text-gray-400 mt-1">{step.description}</p>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

interface SmartLoadingProps {
  isLoading: boolean;
  loadingSteps?: Array<{
    id: string;
    label: string;
    estimatedTime: number; // in milliseconds
  }>;
  onComplete?: () => void;
  className?: string;
}

export const SmartLoading: React.FC<SmartLoadingProps> = ({
  isLoading,
  loadingSteps = [],
  onComplete,
  className = ''
}) => {
  const { t } = useTranslation();
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (!isLoading) {
      setCurrentStepIndex(0);
      setProgress(0);
      return;
    }

    if (loadingSteps.length === 0) return;

    const currentStep = loadingSteps[currentStepIndex];
    if (!currentStep) return;

    const stepProgress = 100 / loadingSteps.length;
    const startProgress = currentStepIndex * stepProgress;

    let progressInterval: NodeJS.Timeout;
    let stepTimeout: NodeJS.Timeout;

    // Simulate progress within the current step
    progressInterval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + (stepProgress / (currentStep.estimatedTime / 100));
        return Math.min(newProgress, startProgress + stepProgress);
      });
    }, 100);

    // Move to next step after estimated time
    stepTimeout = setTimeout(() => {
      if (currentStepIndex < loadingSteps.length - 1) {
        setCurrentStepIndex(prev => prev + 1);
      } else {
        setProgress(100);
        onComplete?.();
      }
    }, currentStep.estimatedTime);

    return () => {
      clearInterval(progressInterval);
      clearTimeout(stepTimeout);
    };
  }, [isLoading, currentStepIndex, loadingSteps, onComplete]);

  if (!isLoading) return null;

  const currentStep = loadingSteps[currentStepIndex];

  return (
    <div className={`bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 ${className}`}>
      <div className="flex items-center gap-3 mb-4">
        <div className="animate-spin w-6 h-6 border-2 border-purple-400 border-t-transparent rounded-full"></div>
        <h3 className="text-lg font-semibold text-white">
          {currentStep?.label || t('community.loading.default', 'Loading...')}
        </h3>
      </div>

      {loadingSteps.length > 0 && (
        <>
          <div className="w-full bg-white/10 rounded-full h-2 mb-4">
            <div 
              className="bg-gradient-to-r from-purple-400 to-blue-400 h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>

          <div className="flex justify-between text-sm text-gray-400">
            <span>
              {t('community.loading.step', 'Step {{current}} of {{total}}', {
                current: currentStepIndex + 1,
                total: loadingSteps.length
              })}
            </span>
            <span>{Math.round(progress)}%</span>
          </div>
        </>
      )}
    </div>
  );
};

interface ConnectionIndicatorProps {
  isOnline: boolean;
  connectionQuality?: 'excellent' | 'good' | 'poor' | 'offline';
  className?: string;
}

export const ConnectionIndicator: React.FC<ConnectionIndicatorProps> = ({
  isOnline,
  connectionQuality = 'good',
  className = ''
}) => {
  const { t } = useTranslation();

  const getConnectionConfig = () => {
    if (!isOnline) {
      return {
        icon: WifiOff,
        color: 'text-red-400',
        bgColor: 'bg-red-400/20',
        label: t('community.connection.offline', 'Offline'),
        description: t('community.connection.offlineDesc', 'Check your internet connection')
      };
    }

    switch (connectionQuality) {
      case 'excellent':
        return {
          icon: Wifi,
          color: 'text-green-400',
          bgColor: 'bg-green-400/20',
          label: t('community.connection.excellent', 'Excellent'),
          description: t('community.connection.excellentDesc', 'Fast and stable connection')
        };
      case 'good':
        return {
          icon: Wifi,
          color: 'text-blue-400',
          bgColor: 'bg-blue-400/20',
          label: t('community.connection.good', 'Good'),
          description: t('community.connection.goodDesc', 'Stable connection')
        };
      case 'poor':
        return {
          icon: Wifi,
          color: 'text-yellow-400',
          bgColor: 'bg-yellow-400/20',
          label: t('community.connection.poor', 'Poor'),
          description: t('community.connection.poorDesc', 'Slow connection detected')
        };
      default:
        return {
          icon: WifiOff,
          color: 'text-gray-400',
          bgColor: 'bg-gray-400/20',
          label: t('community.connection.unknown', 'Unknown'),
          description: t('community.connection.unknownDesc', 'Connection status unknown')
        };
    }
  };

  const config = getConnectionConfig();
  const Icon = config.icon;

  return (
    <div className={`flex items-center gap-2 px-3 py-2 rounded-lg ${config.bgColor} ${className}`}>
      <Icon className={`w-4 h-4 ${config.color}`} />
      <div className="flex flex-col">
        <span className={`text-sm font-medium ${config.color}`}>
          {config.label}
        </span>
        <span className="text-xs text-gray-400">
          {config.description}
        </span>
      </div>
    </div>
  );
};

interface ActivityIndicatorProps {
  activities: Array<{
    id: string;
    type: 'like' | 'comment' | 'share' | 'post';
    user: string;
    timestamp: Date;
  }>;
  maxItems?: number;
  className?: string;
}

export const ActivityIndicator: React.FC<ActivityIndicatorProps> = ({
  activities,
  maxItems = 5,
  className = ''
}) => {
  const { t } = useTranslation();
  const recentActivities = activities.slice(0, maxItems);

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'like':
        return '❤️';
      case 'comment':
        return '💬';
      case 'share':
        return '🔄';
      case 'post':
        return '📝';
      default:
        return '📌';
    }
  };

  const getActivityLabel = (type: string) => {
    switch (type) {
      case 'like':
        return t('community.activity.liked', 'liked a post');
      case 'comment':
        return t('community.activity.commented', 'commented on a post');
      case 'share':
        return t('community.activity.shared', 'shared a post');
      case 'post':
        return t('community.activity.posted', 'created a new post');
      default:
        return t('community.activity.activity', 'had activity');
    }
  };

  if (recentActivities.length === 0) return null;

  return (
    <div className={`bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4 ${className}`}>
      <h4 className="text-sm font-semibold text-white mb-3 flex items-center gap-2">
        <span className="animate-pulse">🔥</span>
        {t('community.activity.recent', 'Recent Activity')}
      </h4>
      <div className="space-y-2">
        {recentActivities.map((activity) => (
          <div key={activity.id} className="flex items-center gap-3 text-sm">
            <span className="text-lg">{getActivityIcon(activity.type)}</span>
            <div className="flex-1 min-w-0">
              <span className="text-white font-medium">{activity.user}</span>
              <span className="text-gray-400 ml-1">{getActivityLabel(activity.type)}</span>
            </div>
            <span className="text-xs text-gray-500">
              {new Date(activity.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default {
  ProgressSteps,
  SmartLoading,
  ConnectionIndicator,
  ActivityIndicator
};
