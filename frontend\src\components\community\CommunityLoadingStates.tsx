/**
 * Enhanced Loading States for Community Features
 * Provides skeleton loaders, progress indicators, and contextual loading messages
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'secondary' | 'white';
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  color = 'primary',
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  const colorClasses = {
    primary: 'border-purple-400 border-t-transparent',
    secondary: 'border-blue-400 border-t-transparent',
    white: 'border-white border-t-transparent'
  };

  return (
    <div 
      className={`animate-spin ${sizeClasses[size]} border-2 ${colorClasses[color]} rounded-full ${className}`}
      role="status"
      aria-label="Loading"
    />
  );
};

interface PostSkeletonProps {
  count?: number;
}

export const PostSkeleton: React.FC<PostSkeletonProps> = ({ count = 3 }) => {
  return (
    <div className="space-y-6">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 animate-pulse">
          {/* Author skeleton */}
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-white/10 rounded-full"></div>
            <div className="flex-1">
              <div className="h-4 bg-white/10 rounded w-24 mb-2"></div>
              <div className="h-3 bg-white/10 rounded w-16"></div>
            </div>
          </div>
          
          {/* Content skeleton */}
          <div className="space-y-3 mb-4">
            <div className="h-4 bg-white/10 rounded w-full"></div>
            <div className="h-4 bg-white/10 rounded w-3/4"></div>
            <div className="h-4 bg-white/10 rounded w-1/2"></div>
          </div>
          
          {/* Actions skeleton */}
          <div className="flex items-center gap-4">
            <div className="h-8 bg-white/10 rounded w-16"></div>
            <div className="h-8 bg-white/10 rounded w-16"></div>
            <div className="h-8 bg-white/10 rounded w-16"></div>
          </div>
        </div>
      ))}
    </div>
  );
};

export const StatsSkeleton: React.FC = () => {
  return (
    <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4 animate-pulse">
      <div className="flex items-center gap-2 mb-4">
        <div className="w-5 h-5 bg-white/10 rounded"></div>
        <div className="h-4 bg-white/10 rounded w-32"></div>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="text-center">
            <div className="h-6 bg-white/10 rounded w-8 mx-auto mb-2"></div>
            <div className="h-3 bg-white/10 rounded w-16 mx-auto"></div>
          </div>
        ))}
      </div>
    </div>
  );
};

export const SidebarSkeleton: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Trending topics skeleton */}
      <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4 animate-pulse">
        <div className="flex items-center gap-2 mb-4">
          <div className="w-5 h-5 bg-white/10 rounded"></div>
          <div className="h-4 bg-white/10 rounded w-24"></div>
        </div>
        <div className="space-y-2">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="flex justify-between items-center">
              <div className="h-3 bg-white/10 rounded w-20"></div>
              <div className="h-3 bg-white/10 rounded w-6"></div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Quick actions skeleton */}
      <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4 animate-pulse">
        <div className="h-4 bg-white/10 rounded w-24 mb-4"></div>
        <div className="h-10 bg-white/10 rounded w-full"></div>
      </div>
    </div>
  );
};

interface LoadingMessageProps {
  message?: string;
  submessage?: string;
  showSpinner?: boolean;
  className?: string;
}

export const LoadingMessage: React.FC<LoadingMessageProps> = ({
  message,
  submessage,
  showSpinner = true,
  className = ''
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const defaultMessage = t('community.loading.default', 'Loading community data...');
  const displayMessage = message || defaultMessage;

  return (
    <div className={`flex flex-col items-center justify-center py-12 ${className}`}>
      {showSpinner && (
        <LoadingSpinner size="lg" color="primary" className="mb-4" />
      )}
      <p className="text-white/80 text-lg font-medium mb-2">
        {displayMessage}
      </p>
      {submessage && (
        <p className="text-white/60 text-sm text-center max-w-md">
          {submessage}
        </p>
      )}
    </div>
  );
};

interface ProgressBarProps {
  progress: number; // 0-100
  label?: string;
  showPercentage?: boolean;
  className?: string;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  label,
  showPercentage = true,
  className = ''
}) => {
  const clampedProgress = Math.max(0, Math.min(100, progress));

  return (
    <div className={`w-full ${className}`}>
      {(label || showPercentage) && (
        <div className="flex justify-between items-center mb-2">
          {label && <span className="text-white/80 text-sm">{label}</span>}
          {showPercentage && (
            <span className="text-white/60 text-sm">{Math.round(clampedProgress)}%</span>
          )}
        </div>
      )}
      <div className="w-full bg-white/10 rounded-full h-2">
        <div 
          className="bg-gradient-to-r from-purple-400 to-blue-400 h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${clampedProgress}%` }}
        />
      </div>
    </div>
  );
};

interface ConnectionStatusProps {
  status: 'connected' | 'connecting' | 'disconnected';
  className?: string;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ status, className = '' }) => {
  const { t } = useTranslation();

  const statusConfig = {
    connected: {
      icon: '🟢',
      text: t('community.connection.connected', 'Connected'),
      color: 'text-green-400'
    },
    connecting: {
      icon: '🟡',
      text: t('community.connection.connecting', 'Connecting'),
      color: 'text-yellow-400'
    },
    disconnected: {
      icon: '🔴',
      text: t('community.connection.disconnected', 'Disconnected'),
      color: 'text-red-400'
    }
  };

  const config = statusConfig[status];

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <span className="text-sm">{config.icon}</span>
      <span className={`text-sm font-medium ${config.color}`}>
        {config.text}
      </span>
      {status === 'connecting' && (
        <LoadingSpinner size="sm" color="secondary" />
      )}
    </div>
  );
};

interface EmptyStateProps {
  icon?: string;
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon = '📭',
  title,
  description,
  action,
  className = ''
}) => {
  return (
    <div className={`flex flex-col items-center justify-center py-16 px-6 text-center ${className}`}>
      <div className="text-6xl mb-4">{icon}</div>
      <h3 className="text-xl font-semibold text-white/90 mb-2">{title}</h3>
      {description && (
        <p className="text-white/60 mb-6 max-w-md">{description}</p>
      )}
      {action && (
        <button
          onClick={action.onClick}
          className="px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors"
        >
          {action.label}
        </button>
      )}
    </div>
  );
};

// Export all components as a single object for easier importing
export const CommunityLoadingStates = {
  LoadingSpinner,
  PostSkeleton,
  StatsSkeleton,
  SidebarSkeleton,
  LoadingMessage,
  ProgressBar,
  ConnectionStatus,
  EmptyState
};

export default CommunityLoadingStates;
