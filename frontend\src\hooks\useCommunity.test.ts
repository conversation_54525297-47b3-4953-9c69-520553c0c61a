/**
 * Test file to verify the refactored useCommunity hook works correctly
 * This tests the orchestrator pattern implementation
 */

import { renderHook } from '@testing-library/react';
import { Provider } from 'react-redux';
import { store } from '../store';
import { useCommunity } from './useCommunity';

// Mock the focused hooks
jest.mock('./useCommunityPosts', () => ({
  useCommunityPosts: () => ({
    posts: [],
    searchResults: [],
    isLoading: false,
    isSearching: false,
    handleViewChange: jest.fn(),
    handleSearchChange: jest.fn(),
    handleClearSearch: jest.fn(),
    refreshPosts: jest.fn(),
    handleLikePost: jest.fn(),
    handleCreatePost: jest.fn(),
    handleCommentPost: jest.fn(),
    handleSharePost: jest.fn(),
    handleSavePost: jest.fn(),
  })
}));

jest.mock('./useCommunityStats', () => ({
  useCommunityStats: () => ({
    stats: null,
    connectionStatus: 'connected' as const,
    isLoading: false,
    refreshStats: jest.fn(),
    checkConnection: jest.fn(),
  })
}));

jest.mock('./useCommunityModals', () => ({
  useCommunityModals: () => ({
    showCreateModal: false,
    showAdvancedSearch: false,
    showUserDiscovery: false,
    showPostAnalytics: null,
    openCreateModal: jest.fn(),
    closeCreateModal: jest.fn(),
    openAdvancedSearch: jest.fn(),
    closeAdvancedSearch: jest.fn(),
    openUserDiscovery: jest.fn(),
    closeUserDiscovery: jest.fn(),
    openPostAnalytics: jest.fn(),
    closePostAnalytics: jest.fn(),
    handleCreatePostClick: jest.fn(),
    handleAdvancedSearchResults: jest.fn(),
  })
}));

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <Provider store={store}>{children}</Provider>
);

describe('useCommunity Hook - Refactored Orchestrator', () => {
  it('should render without errors', () => {
    const { result } = renderHook(() => useCommunity(), { wrapper });
    
    expect(result.current).toBeDefined();
    expect(result.current.posts).toEqual([]);
    expect(result.current.stats).toBeNull();
    expect(result.current.connectionStatus).toBe('connected');
  });

  it('should provide all expected interface methods', () => {
    const { result } = renderHook(() => useCommunity(), { wrapper });
    
    // Check that all expected methods are present
    expect(typeof result.current.handleViewChange).toBe('function');
    expect(typeof result.current.handleSearchChange).toBe('function');
    expect(typeof result.current.handleClearSearch).toBe('function');
    expect(typeof result.current.refreshPosts).toBe('function');
    expect(typeof result.current.refreshStats).toBe('function');
    expect(typeof result.current.checkConnection).toBe('function');
    
    // Post operations
    expect(typeof result.current.handleLikePost).toBe('function');
    expect(typeof result.current.handleCreatePost).toBe('function');
    expect(typeof result.current.handleCommentPost).toBe('function');
    expect(typeof result.current.handleSharePost).toBe('function');
    expect(typeof result.current.handleSavePost).toBe('function');
    
    // Modal actions
    expect(typeof result.current.openCreateModal).toBe('function');
    expect(typeof result.current.closeCreateModal).toBe('function');
    expect(typeof result.current.openAdvancedSearch).toBe('function');
    expect(typeof result.current.closeAdvancedSearch).toBe('function');
    expect(typeof result.current.openUserDiscovery).toBe('function');
    expect(typeof result.current.closeUserDiscovery).toBe('function');
    expect(typeof result.current.openPostAnalytics).toBe('function');
    expect(typeof result.current.closePostAnalytics).toBe('function');
    expect(typeof result.current.handleCreatePostClick).toBe('function');
    expect(typeof result.current.handleAdvancedSearchResults).toBe('function');
  });

  it('should compute stats correctly', () => {
    const { result } = renderHook(() => useCommunity(), { wrapper });
    
    expect(result.current.computedStats).toEqual({
      trending_posts_count: 0,
      verified_authors_count: 0,
      saved_posts_count: 0,
    });
  });
});
