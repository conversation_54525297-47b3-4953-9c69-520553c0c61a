"""
Test Security Features in Community Views
Tests enhanced authentication, authorization, and security logging in views
"""

import json
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.contrib.auth.models import User
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.urls import reverse

from ..models import CommunityPost, CommunityComment
from ..views import CommunityPostViewSet, CommunityCommentViewSet


class CommunityPostViewSecurityTest(APITestCase):
    """Test security features in CommunityPostViewSet"""
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.inactive_user = User.objects.create_user(
            username='inactiveuser',
            email='<EMAIL>',
            password='testpass123',
            is_active=False
        )
        self.other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_authenticated_post_creation(self):
        """Test that only authenticated users can create posts"""
        data = {
            'title': 'Test Post',
            'content': 'Test content',
            'visibility': 'public',
            'allow_comments': True
        }
        
        # Unauthenticated request
        response = self.client.post('/api/community/posts/', data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Authenticated request
        self.client.force_authenticate(user=self.user)
        response = self.client.post('/api/community/posts/', data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
    
    @patch('community.views.security_logger.log_authentication_failure')
    def test_inactive_user_post_creation_blocked(self, mock_log):
        """Test that inactive users cannot create posts"""
        self.client.force_authenticate(user=self.inactive_user)
        
        data = {
            'title': 'Test Post',
            'content': 'Test content',
            'visibility': 'public',
            'allow_comments': True
        }
        
        response = self.client.post('/api/community/posts/', data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Account is inactive', str(response.data))
        
        # Should log authentication failure
        mock_log.assert_called_once()
        call_args = mock_log.call_args[1]
        self.assertEqual(call_args['user_id'], self.inactive_user.id)
        self.assertEqual(call_args['reason'], 'inactive_user_post_creation')
    
    @patch('community.views.security_logger.log_suspicious_activity')
    def test_post_creation_security_logging(self, mock_log):
        """Test that post creation is logged for security monitoring"""
        self.client.force_authenticate(user=self.user)
        
        data = {
            'title': 'Test Post',
            'content': 'Test content for security logging',
            'visibility': 'public',
            'allow_comments': True
        }
        
        response = self.client.post('/api/community/posts/', data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Should log post creation
        mock_log.assert_called_once()
        call_args = mock_log.call_args[1]
        self.assertEqual(call_args['user_id'], self.user.id)
        self.assertEqual(call_args['activity_type'], 'post_created')
        self.assertIn('post_id', call_args['details'])
        self.assertIn('title_length', call_args['details'])
        self.assertIn('content_length', call_args['details'])
    
    def test_like_own_post_prevention(self):
        """Test that users cannot like their own posts"""
        self.client.force_authenticate(user=self.user)
        
        # Create a post
        post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.user,
            visibility='public'
        )
        
        # Try to like own post
        response = self.client.post(f'/api/community/posts/{post.id}/like/')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Cannot like your own post', str(response.data))
    
    def test_like_authentication_required(self):
        """Test that liking requires authentication"""
        # Create a post by another user
        post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.other_user,
            visibility='public'
        )
        
        # Try to like without authentication
        response = self.client.post(f'/api/community/posts/{post.id}/like/')
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    @patch('community.views.security_logger.log_authentication_failure')
    def test_inactive_user_like_blocked(self, mock_log):
        """Test that inactive users cannot like posts"""
        self.client.force_authenticate(user=self.inactive_user)
        
        # Create a post by another user
        post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.other_user,
            visibility='public'
        )
        
        response = self.client.post(f'/api/community/posts/{post.id}/like/')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn('Account is inactive', str(response.data))
        
        # Should log authentication failure
        mock_log.assert_called_once()
        call_args = mock_log.call_args[1]
        self.assertEqual(call_args['user_id'], self.inactive_user.id)
        self.assertEqual(call_args['reason'], 'inactive_user_like_attempt')
    
    @patch('community.views.security_logger.log_suspicious_activity')
    def test_like_action_security_logging(self, mock_log):
        """Test that like actions are logged for security monitoring"""
        self.client.force_authenticate(user=self.user)
        
        # Create a post by another user
        post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.other_user,
            visibility='public'
        )
        
        response = self.client.post(f'/api/community/posts/{post.id}/like/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Should log like action
        mock_log.assert_called_once()
        call_args = mock_log.call_args[1]
        self.assertEqual(call_args['user_id'], self.user.id)
        self.assertEqual(call_args['activity_type'], 'post_liked')
        self.assertIn('post_id', call_args['details'])
        self.assertIn('post_author', call_args['details'])
    
    def test_comment_on_post_with_comments_disabled(self):
        """Test that comments cannot be added to posts with comments disabled"""
        self.client.force_authenticate(user=self.user)
        
        # Create a post with comments disabled
        post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.other_user,
            visibility='public',
            allow_comments=False
        )
        
        data = {'content': 'Test comment'}
        response = self.client.post(f'/api/community/posts/{post.id}/add_comment/', data)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn('Comments are not allowed', str(response.data))
    
    @patch('community.views.security_logger.log_authentication_failure')
    def test_unauthenticated_comment_blocked(self, mock_log):
        """Test that unauthenticated users cannot comment"""
        # Create a post
        post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.user,
            visibility='public',
            allow_comments=True
        )
        
        data = {'content': 'Test comment'}
        response = self.client.post(f'/api/community/posts/{post.id}/add_comment/', data)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Should log authentication failure
        mock_log.assert_called_once()
        call_args = mock_log.call_args[1]
        self.assertIsNone(call_args['user_id'])
        self.assertEqual(call_args['reason'], 'unauthenticated_comment_attempt')
    
    @patch('community.views.security_logger.log_suspicious_activity')
    def test_comment_creation_security_logging(self, mock_log):
        """Test that comment creation is logged for security monitoring"""
        self.client.force_authenticate(user=self.user)
        
        # Create a post
        post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.other_user,
            visibility='public',
            allow_comments=True
        )
        
        data = {'content': 'Test comment for security logging'}
        response = self.client.post(f'/api/community/posts/{post.id}/add_comment/', data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Should log comment creation
        mock_log.assert_called_once()
        call_args = mock_log.call_args[1]
        self.assertEqual(call_args['user_id'], self.user.id)
        self.assertEqual(call_args['activity_type'], 'comment_created')
        self.assertIn('comment_id', call_args['details'])
        self.assertIn('post_id', call_args['details'])
        self.assertIn('content_length', call_args['details'])
    
    @patch('community.views.security_logger.log_suspicious_activity')
    def test_comment_validation_failure_logging(self, mock_log):
        """Test that comment validation failures are logged"""
        self.client.force_authenticate(user=self.user)
        
        # Create a post
        post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.other_user,
            visibility='public',
            allow_comments=True
        )
        
        # Send invalid comment data (empty content)
        data = {'content': ''}
        response = self.client.post(f'/api/community/posts/{post.id}/add_comment/', data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Should log validation failure
        mock_log.assert_called_once()
        call_args = mock_log.call_args[1]
        self.assertEqual(call_args['user_id'], self.user.id)
        self.assertEqual(call_args['activity_type'], 'comment_validation_failed')
        self.assertIn('errors', call_args['details'])
        self.assertIn('post_id', call_args['details'])


class CommunityCommentViewSecurityTest(APITestCase):
    """Test security features in CommunityCommentViewSet"""
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create a test post
        self.post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.user,
            visibility='public',
            allow_comments=True
        )
    
    def test_comment_creation_requires_authentication(self):
        """Test that comment creation requires authentication"""
        data = {
            'content': 'Test comment',
            'post': self.post.id
        }
        
        # Unauthenticated request
        response = self.client.post('/api/community/comments/', data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Authenticated request
        self.client.force_authenticate(user=self.user)
        response = self.client.post('/api/community/comments/', data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
    
    def test_comment_content_sanitization(self):
        """Test that comment content is sanitized"""
        self.client.force_authenticate(user=self.user)
        
        data = {
            'content': '<script>alert("xss")</script>Safe comment content',
            'post': self.post.id
        }
        
        response = self.client.post('/api/community/comments/', data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check that malicious content was removed
        comment = CommunityComment.objects.get(id=response.data['id'])
        self.assertNotIn('<script>', comment.content)
        self.assertNotIn('alert', comment.content)
        self.assertIn('Safe comment content', comment.content)
    
    def test_comment_length_validation(self):
        """Test comment length validation"""
        self.client.force_authenticate(user=self.user)
        
        # Too long comment
        data = {
            'content': 'x' * 1001,  # Exceeds 1000 character limit
            'post': self.post.id
        }
        
        response = self.client.post('/api/community/comments/', data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('content', response.data)
    
    def test_comment_parent_validation(self):
        """Test comment parent validation"""
        self.client.force_authenticate(user=self.user)
        
        # Create a parent comment
        parent_comment = CommunityComment.objects.create(
            content='Parent comment',
            author=self.user,
            post=self.post
        )
        
        # Valid reply
        data = {
            'content': 'Reply to comment',
            'post': self.post.id,
            'parent': parent_comment.id
        }
        
        response = self.client.post('/api/community/comments/', data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Create a reply to the reply (should fail - max 2 levels)
        reply = CommunityComment.objects.get(id=response.data['id'])
        
        data = {
            'content': 'Reply to reply',
            'post': self.post.id,
            'parent': reply.id
        }
        
        response = self.client.post('/api/community/comments/', data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Replies to replies are not allowed', str(response.data))
    
    def test_comment_cross_post_parent_validation(self):
        """Test that parent comment must belong to the same post"""
        self.client.force_authenticate(user=self.user)
        
        # Create another post
        other_post = CommunityPost.objects.create(
            title='Other Post',
            content='Other content',
            author=self.other_user,
            visibility='public',
            allow_comments=True
        )
        
        # Create a comment on the other post
        other_comment = CommunityComment.objects.create(
            content='Comment on other post',
            author=self.user,
            post=other_post
        )
        
        # Try to create a reply on different post
        data = {
            'content': 'Invalid reply',
            'post': self.post.id,
            'parent': other_comment.id
        }
        
        response = self.client.post('/api/community/comments/', data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Parent comment must belong to the same post', str(response.data))


class SecurityIntegrationTest(APITestCase):
    """Integration tests for security features across views"""
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    @patch('community.views.security_logger')
    def test_comprehensive_security_logging_flow(self, mock_logger):
        """Test comprehensive security logging across different operations"""
        self.client.force_authenticate(user=self.user)
        
        # Create a post
        post_data = {
            'title': 'Security Test Post',
            'content': 'Content for security testing',
            'visibility': 'public',
            'allow_comments': True
        }
        
        response = self.client.post('/api/community/posts/', post_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        post_id = response.data['id']
        
        # Like the post (should fail - own post)
        self.client.post(f'/api/community/posts/{post_id}/like/')
        
        # Add a comment
        comment_data = {'content': 'Test comment for security'}
        response = self.client.post(f'/api/community/posts/{post_id}/add_comment/', comment_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Verify security logging was called multiple times
        self.assertGreater(mock_logger.log_suspicious_activity.call_count, 1)
        
        # Check that different activity types were logged
        logged_activities = [
            call[1]['activity_type'] for call in mock_logger.log_suspicious_activity.call_args_list
        ]
        self.assertIn('post_created', logged_activities)
        self.assertIn('comment_created', logged_activities)
    
    def test_malicious_content_sanitization_flow(self):
        """Test end-to-end malicious content sanitization"""
        self.client.force_authenticate(user=self.user)
        
        # Create post with malicious content
        malicious_post_data = {
            'title': '<script>alert("title xss")</script>Malicious Title',
            'content': '<script>alert("content xss")</script><iframe src="evil.com"></iframe>Malicious content',
            'visibility': 'public',
            'allow_comments': True
        }
        
        response = self.client.post('/api/community/posts/', malicious_post_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Verify content was sanitized
        post = CommunityPost.objects.get(id=response.data['id'])
        self.assertNotIn('<script>', post.title)
        self.assertNotIn('<script>', post.content)
        self.assertNotIn('<iframe>', post.content)
        self.assertNotIn('alert', post.title)
        self.assertNotIn('alert', post.content)
        
        # Add malicious comment
        malicious_comment_data = {
            'content': '<script>alert("comment xss")</script>Malicious comment'
        }
        
        response = self.client.post(f'/api/community/posts/{post.id}/add_comment/', malicious_comment_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Verify comment was sanitized
        comment = CommunityComment.objects.get(id=response.data['id'])
        self.assertNotIn('<script>', comment.content)
        self.assertNotIn('alert', comment.content)
        self.assertIn('Malicious comment', comment.content)
