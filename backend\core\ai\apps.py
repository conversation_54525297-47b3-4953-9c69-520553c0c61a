"""
Django App Configuration for Consolidated AI Services
"""

from django.apps import AppConfig


class AIConfig(AppConfig):
    """
    Django app configuration for the consolidated AI services
    """
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'core.ai'
    label = 'core_ai'
    verbose_name = 'AI Services'
    
    def ready(self):
        """
        Initialize AI services when Django starts
        """
        # Import signals if any
        # from . import signals
        pass
