"""
🎯 AI VIEWS PACKAGE
Consolidated AI API views and endpoints

This package provides access to consolidated AI views.
All AI endpoints are now handled through core.views.consolidated_ai_views
which is used by the URL configuration.
"""

# Import serializers for use by other modules

from .serializers import (
    AIRecommendationSerializer,
    AIRecommendationFeedbackSerializer,
    AIConfigurationSerializer,
    AIUsageLogSerializer,
    AIServiceStatusSerializer,
    AISessionSerializer,
    AIInteractionSerializer,
    ChatRequestSerializer,
    BusinessAnalysisRequestSerializer,
    PredictiveAnalyticsRequestSerializer,
    ComputerVisionRequestSerializer,
    VoiceAIRequestSerializer,
    RecommendationGenerationRequestSerializer
)

__all__ = [
    # Serializers (main exports from this package)
    'AIRecommendationSerializer',
    'AIRecommendationFeedbackSerializer',
    'AIConfigurationSerializer',
    'AIUsageLogSerializer',
    'AIServiceStatusSerializer',
    'AISessionSerializer',
    'AIInteractionSerializer',
    'ChatRequestSerializer',
    'BusinessAnalysisRequestSerializer',
    'PredictiveAnalyticsRequestSerializer',
    'ComputerVisionRequestSerializer',
    'VoiceAIRequestSerializer',
    'RecommendationGenerationRequestSerializer'
]
