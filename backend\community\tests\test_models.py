"""
Test Community Models
Tests model validation, relationships, and data integrity
"""

from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.db import IntegrityError, transaction

from ..models import CommunityPost, CommunityComment, PostSave, UserFollow
from api.models import Tag


class CommunityPostModelTest(TestCase):
    """Test CommunityPost model functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_post_creation_with_valid_data(self):
        """Test creating a post with valid data"""
        post = CommunityPost.objects.create(
            title='Test Post',
            content='This is a test post content.',
            author=self.user,
            visibility='public',
            allow_comments=True
        )
        
        self.assertEqual(post.title, 'Test Post')
        self.assertEqual(post.author, self.user)
        self.assertEqual(post.visibility, 'public')
        self.assertTrue(post.allow_comments)
        self.assertIsNotNone(post.created_at)
        self.assertIsNotNone(post.updated_at)
    
    def test_post_title_max_length(self):
        """Test post title maximum length validation"""
        long_title = 'x' * 201  # Exceeds 200 character limit
        
        with self.assertRaises(ValidationError):
            post = CommunityPost(
                title=long_title,
                content='Valid content',
                author=self.user,
                visibility='public'
            )
            post.full_clean()
    
    def test_post_content_max_length(self):
        """Test post content maximum length validation"""
        long_content = 'x' * 5001  # Exceeds 5000 character limit
        
        with self.assertRaises(ValidationError):
            post = CommunityPost(
                title='Valid title',
                content=long_content,
                author=self.user,
                visibility='public'
            )
            post.full_clean()
    
    def test_post_visibility_choices(self):
        """Test post visibility field choices"""
        # Valid visibility
        post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.user,
            visibility='private'
        )
        self.assertEqual(post.visibility, 'private')
        
        # Invalid visibility should raise ValidationError
        with self.assertRaises(ValidationError):
            post = CommunityPost(
                title='Test Post',
                content='Test content',
                author=self.user,
                visibility='invalid_visibility'
            )
            post.full_clean()
    
    def test_post_str_representation(self):
        """Test post string representation"""
        post = CommunityPost.objects.create(
            title='Test Post Title',
            content='Test content',
            author=self.user,
            visibility='public'
        )
        
        expected_str = f"Test Post Title by {self.user.username}"
        self.assertEqual(str(post), expected_str)
    
    def test_post_like_count_property(self):
        """Test post like count property"""
        post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.user,
            visibility='public'
        )

        # Initially no likes
        self.assertEqual(post.like_count, 0)

        # Add a like
        post.likes.add(self.other_user)

        # Refresh from database
        post.refresh_from_db()
        self.assertEqual(post.like_count, 1)
    
    def test_post_comment_count_property(self):
        """Test post comment count property"""
        post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.user,
            visibility='public',
            allow_comments=True
        )

        # Initially no comments (using comments_count field)
        self.assertEqual(post.comments_count, 0)

        # Add a comment
        CommunityComment.objects.create(
            content='Test comment',
            author=self.other_user,
            post=post
        )

        # Check actual comment count via relationship
        self.assertEqual(post.comments.count(), 1)
    
    def test_post_tags_relationship(self):
        """Test post tags many-to-many relationship"""
        post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.user,
            visibility='public'
        )

        # Create tags
        tag1 = Tag.objects.create(name='python')
        tag2 = Tag.objects.create(name='django')

        # Add tags to post
        post.tags.add(tag1, tag2)

        # Test relationship
        self.assertEqual(post.tags.count(), 2)
        self.assertIn(tag1, post.tags.all())
        self.assertIn(tag2, post.tags.all())
    
    def test_post_ordering(self):
        """Test post default ordering"""
        # Create posts with different timestamps
        post1 = CommunityPost.objects.create(
            title='First Post',
            content='First content',
            author=self.user,
            visibility='public'
        )
        
        post2 = CommunityPost.objects.create(
            title='Second Post',
            content='Second content',
            author=self.user,
            visibility='public'
        )
        
        # Should be ordered by created_at descending
        posts = list(CommunityPost.objects.all())
        self.assertEqual(posts[0], post2)  # Most recent first
        self.assertEqual(posts[1], post1)


class CommunityCommentModelTest(TestCase):
    """Test CommunityComment model functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.user,
            visibility='public',
            allow_comments=True
        )
    
    def test_comment_creation_with_valid_data(self):
        """Test creating a comment with valid data"""
        comment = CommunityComment.objects.create(
            content='This is a test comment.',
            author=self.other_user,
            post=self.post
        )
        
        self.assertEqual(comment.content, 'This is a test comment.')
        self.assertEqual(comment.author, self.other_user)
        self.assertEqual(comment.post, self.post)
        self.assertIsNone(comment.parent)
        self.assertIsNotNone(comment.created_at)
    
    def test_comment_content_max_length(self):
        """Test comment content maximum length validation"""
        long_content = 'x' * 1001  # Exceeds 1000 character limit
        
        with self.assertRaises(ValidationError):
            comment = CommunityComment(
                content=long_content,
                author=self.user,
                post=self.post
            )
            comment.full_clean()
    
    def test_comment_reply_functionality(self):
        """Test comment reply (parent-child) functionality"""
        # Create parent comment
        parent_comment = CommunityComment.objects.create(
            content='Parent comment',
            author=self.user,
            post=self.post
        )
        
        # Create reply
        reply = CommunityComment.objects.create(
            content='Reply to parent',
            author=self.other_user,
            post=self.post,
            parent=parent_comment
        )
        
        # Test relationships
        self.assertEqual(reply.parent, parent_comment)
        self.assertIn(reply, parent_comment.replies.all())
    
    def test_comment_str_representation(self):
        """Test comment string representation"""
        comment = CommunityComment.objects.create(
            content='Test comment content',
            author=self.user,
            post=self.post
        )
        
        expected_str = f"Comment by {self.user.username} on {self.post.title}"
        self.assertEqual(str(comment), expected_str)
    
    def test_comment_ordering(self):
        """Test comment default ordering"""
        # Create comments with different timestamps
        comment1 = CommunityComment.objects.create(
            content='First comment',
            author=self.user,
            post=self.post
        )
        
        comment2 = CommunityComment.objects.create(
            content='Second comment',
            author=self.other_user,
            post=self.post
        )
        
        # Should be ordered by created_at ascending (oldest first)
        comments = list(CommunityComment.objects.all())
        self.assertEqual(comments[0], comment1)  # Oldest first
        self.assertEqual(comments[1], comment2)


class PostSaveModelTest(TestCase):
    """Test PostSave model functionality"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.user,
            visibility='public'
        )

    def test_post_save_creation(self):
        """Test creating a post save"""
        post_save = PostSave.objects.create(
            user=self.other_user,
            post=self.post
        )

        self.assertEqual(post_save.user, self.other_user)
        self.assertEqual(post_save.post, self.post)
        self.assertIsNotNone(post_save.saved_at)

    def test_post_save_uniqueness_constraint(self):
        """Test that a user can only save a post once"""
        PostSave.objects.create(user=self.other_user, post=self.post)

        # Creating another save for same user and post should raise IntegrityError
        with self.assertRaises(IntegrityError):
            with transaction.atomic():
                PostSave.objects.create(user=self.other_user, post=self.post)


class UserFollowModelTest(TestCase):
    """Test UserFollow model functionality"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_user_follow_creation(self):
        """Test creating a user follow relationship"""
        follow = UserFollow.objects.create(
            follower=self.user,
            following=self.other_user
        )

        self.assertEqual(follow.follower, self.user)
        self.assertEqual(follow.following, self.other_user)
        self.assertIsNotNone(follow.followed_at)

    def test_user_follow_uniqueness_constraint(self):
        """Test that a user can only follow another user once"""
        UserFollow.objects.create(follower=self.user, following=self.other_user)

        # Creating another follow for same users should raise IntegrityError
        with self.assertRaises(IntegrityError):
            with transaction.atomic():
                UserFollow.objects.create(follower=self.user, following=self.other_user)

    def test_user_follow_str_representation(self):
        """Test user follow string representation"""
        follow = UserFollow.objects.create(
            follower=self.user,
            following=self.other_user
        )

        expected_str = f"{self.user.username} follows {self.other_user.username}"
        self.assertEqual(str(follow), expected_str)


class ModelRelationshipTest(TestCase):
    """Test relationships between models"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_post_deletion_cascades_to_comments(self):
        """Test that deleting a post deletes its comments"""
        post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.user,
            visibility='public',
            allow_comments=True
        )
        
        comment = CommunityComment.objects.create(
            content='Test comment',
            author=self.other_user,
            post=post
        )
        
        # Delete post
        post.delete()
        
        # Comment should be deleted too
        self.assertFalse(CommunityComment.objects.filter(id=comment.id).exists())
    
    def test_post_deletion_cascades_to_saves(self):
        """Test that deleting a post deletes its saves"""
        post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.user,
            visibility='public'
        )

        post_save = PostSave.objects.create(user=self.other_user, post=post)

        # Delete post
        post.delete()

        # Save should be deleted too
        self.assertFalse(PostSave.objects.filter(id=post_save.id).exists())
    
    def test_user_deletion_cascades_to_posts(self):
        """Test that deleting a user deletes their posts"""
        post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.user,
            visibility='public'
        )
        
        # Delete user
        self.user.delete()
        
        # Post should be deleted too
        self.assertFalse(CommunityPost.objects.filter(id=post.id).exists())
    
    def test_user_deletion_cascades_to_comments(self):
        """Test that deleting a user deletes their comments"""
        post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.user,
            visibility='public',
            allow_comments=True
        )
        
        comment = CommunityComment.objects.create(
            content='Test comment',
            author=self.other_user,
            post=post
        )
        
        # Delete user
        self.other_user.delete()
        
        # Comment should be deleted too
        self.assertFalse(CommunityComment.objects.filter(id=comment.id).exists())
    
    def test_tag_deletion_does_not_cascade_to_posts(self):
        """Test that deleting a tag does not delete posts"""
        post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.user,
            visibility='public'
        )

        tag = Tag.objects.create(name='test-tag')
        post.tags.add(tag)

        # Delete tag
        tag.delete()

        # Post should still exist
        self.assertTrue(CommunityPost.objects.filter(id=post.id).exists())

        # But tag should be removed from post
        post.refresh_from_db()
        self.assertEqual(post.tags.count(), 0)
