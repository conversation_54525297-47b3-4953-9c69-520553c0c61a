/**
 * Simple Test Runner to Check Basic Functionality
 * This will help us identify issues without complex testing frameworks
 */

console.log('🧪 Starting basic functionality tests...');

// Test 1: Check if basic JavaScript works
try {
  const result = 1 + 1;
  if (result === 2) {
    console.log('✅ Basic JavaScript operations work');
  } else {
    console.log('❌ Basic JavaScript operations failed');
  }
} catch (error) {
  console.log('❌ Basic JavaScript test failed:', error.message);
}

// Test 2: Check if we can import ES modules
try {
  // This will test if the module system works
  const fs = require('fs');
  const path = require('path');
  
  // Check if package.json exists
  const packageJsonPath = path.join(__dirname, 'package.json');
  if (fs.existsSync(packageJsonPath)) {
    console.log('✅ Package.json exists and is accessible');
    
    // Read and parse package.json
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    console.log('✅ Package.json is valid JSON');
    console.log(`📦 Project name: ${packageJson.name}`);
    console.log(`📦 Project version: ${packageJson.version}`);
    
    // Check if test scripts exist
    if (packageJson.scripts && packageJson.scripts.test) {
      console.log('✅ Test scripts are defined in package.json');
    } else {
      console.log('⚠️ Test scripts are missing from package.json');
    }
    
    // Check if testing dependencies exist
    const testingDeps = [
      'vitest',
      '@testing-library/react',
      '@testing-library/jest-dom',
      '@playwright/test'
    ];
    
    const devDeps = packageJson.devDependencies || {};
    const missingDeps = testingDeps.filter(dep => !devDeps[dep]);
    
    if (missingDeps.length === 0) {
      console.log('✅ All testing dependencies are listed in package.json');
    } else {
      console.log('⚠️ Missing testing dependencies:', missingDeps.join(', '));
    }
    
  } else {
    console.log('❌ Package.json not found');
  }
} catch (error) {
  console.log('❌ File system test failed:', error.message);
}

// Test 3: Check if we can access the src directory
try {
  const fs = require('fs');
  const path = require('path');
  
  const srcPath = path.join(__dirname, 'src');
  if (fs.existsSync(srcPath)) {
    console.log('✅ src directory exists');
    
    // Check for key directories
    const keyDirs = ['hooks', 'services', 'utils', 'components'];
    keyDirs.forEach(dir => {
      const dirPath = path.join(srcPath, dir);
      if (fs.existsSync(dirPath)) {
        console.log(`✅ ${dir} directory exists`);
      } else {
        console.log(`❌ ${dir} directory missing`);
      }
    });
    
    // Check for specific files we're testing
    const keyFiles = [
      'hooks/useCommunity.ts',
      'services/communityApi.ts',
      'utils/communityErrorHandler.ts'
    ];
    
    keyFiles.forEach(file => {
      const filePath = path.join(srcPath, file);
      if (fs.existsSync(filePath)) {
        console.log(`✅ ${file} exists`);
      } else {
        console.log(`❌ ${file} missing`);
      }
    });
    
  } else {
    console.log('❌ src directory not found');
  }
} catch (error) {
  console.log('❌ Source directory test failed:', error.message);
}

// Test 4: Check Node.js and npm versions
try {
  console.log(`📋 Node.js version: ${process.version}`);
  console.log(`📋 Platform: ${process.platform}`);
  console.log(`📋 Architecture: ${process.arch}`);
  console.log(`📋 Current working directory: ${process.cwd()}`);
} catch (error) {
  console.log('❌ Environment check failed:', error.message);
}

console.log('🏁 Basic functionality tests completed');
