/**
 * Community API Service
 * Handles all community-related API calls with Redux authentication integration
 */

import { authenticatedFetch, apiRequest, isAuthenticated } from '../utils/authUtils';
import { requestDeduplicator, generateCacheKey } from '../utils/requestDeduplication';

// Types matching the backend models
export interface CommunityAuthor {
  id: string;
  username: string;
  first_name: string;
  last_name: string;
  full_name: string;
  avatar: string | null;
  is_verified: boolean;
}

export interface CommunityComment {
  id: string;
  content: string;
  author: CommunityAuthor;
  author_name: string;
  time_ago: string;
  created_at: string;
  parent?: string | null;
  like_count: number;
  reply_count: number;
  is_liked: boolean;
  replies: CommunityComment[];
}

export interface CommunityPost {
  id: string;
  title?: string;
  content: string;
  author: CommunityAuthor;
  media: string[];
  tags: Array<{ id: string; name: string }>;
  visibility: 'public' | 'followers' | 'private';
  allow_comments: boolean;
  like_count: number;
  save_count: number;
  shares_count: number;
  comments_count: number;
  is_liked: boolean;
  is_saved: boolean;
  comments: CommunityComment[];
  created_at: string;
  updated_at: string;
}

export interface CommunityStats {
  total_posts: number;
  total_users: number;
  total_likes: number;
  total_comments: number;
  posts_today: number;
  active_users_today: number;
  online_users_count: number;
  updated_at: string;
}

export interface CreatePostData {
  title?: string;
  content: string;
  tag_names?: string[];
  visibility?: 'public' | 'followers' | 'private';
  allow_comments?: boolean;
}

// Removed old CreateCommentData - using simple { post: string; content: string } now

export interface UserProfile {
  id: string;
  username: string;
  first_name: string;
  last_name: string;
  full_name: string;
  avatar: string | null;
  is_verified: boolean;
  bio?: string;
  location?: string;
  website?: string;
  user_role: string;
  date_joined: string;
  followers_count: number;
  following_count: number;
  posts_count: number;
  is_following: boolean;
  is_followed_by: boolean;
}

// API endpoints
const API_BASE = '/community';

// Authentication helper functions
const requireAuth = (action: string = 'perform this action') => {
  if (!isAuthenticated()) {
    throw new Error(`Authentication required to ${action}`);
  }
};

const warnIfNotAuth = (action: string = 'get full functionality') => {
  if (!isAuthenticated()) {
    console.warn(`⚠️ Not authenticated - ${action} may be limited`);
  }
};

export const communityApi = {
  // Posts
  async getPosts(params?: {
    page?: number;
    search?: string;
    visibility?: string;
    author?: string;
    ordering?: string;
  }): Promise<{ results: CommunityPost[]; count: number; next: string | null; previous: string | null }> {
    // Warn if not authenticated - may get limited results
    warnIfNotAuth('access all posts');

    const cacheKey = generateCacheKey.communityPosts(params);

    return requestDeduplicator.deduplicate(cacheKey, async () => {
      const searchParams = new URLSearchParams();
      if (params?.page) searchParams.append('page', params.page.toString());
      if (params?.search) searchParams.append('search', params.search);
      if (params?.visibility) searchParams.append('visibility', params.visibility);
      if (params?.author) searchParams.append('author', params.author);
      if (params?.ordering) searchParams.append('ordering', params.ordering);

      const url = `${API_BASE}/posts/${searchParams.toString() ? '?' + searchParams.toString() : ''}`;
      return apiRequest<{ results: CommunityPost[]; count: number; next: string | null; previous: string | null }>(url);
    });
  },

  async getPost(id: string): Promise<CommunityPost> {
    return apiRequest<CommunityPost>(`${API_BASE}/posts/${id}/`);
  },

  async createPost(data: CreatePostData): Promise<CommunityPost> {
    requireAuth('create posts');
    return apiRequest<CommunityPost>(`${API_BASE}/posts/`, 'POST', data);
  },

  async updatePost(id: string, data: Partial<CreatePostData>): Promise<CommunityPost> {
    return apiRequest<CommunityPost>(`${API_BASE}/posts/${id}/`, 'PATCH', data);
  },

  async deletePost(id: string): Promise<void> {
    return apiRequest<void>(`${API_BASE}/posts/${id}/`, 'DELETE');
  },

  async likePost(id: string): Promise<{ liked: boolean; like_count: number }> {
    requireAuth('like posts');
    return apiRequest<{ liked: boolean; like_count: number }>(`${API_BASE}/posts/${id}/like/`, 'POST');
  },

  async savePost(id: string): Promise<{ saved: boolean; save_count: number }> {
    requireAuth('save posts');
    return apiRequest<{ saved: boolean; save_count: number }>(`${API_BASE}/posts/${id}/save/`, 'POST');
  },

  async sharePost(id: string, platform?: string): Promise<{ shared: boolean; shares_count: number }> {
    return apiRequest<{ shared: boolean; shares_count: number }>(`${API_BASE}/posts/${id}/share/`, 'POST', { platform });
  },

  async getFeed(): Promise<{ posts: CommunityPost[]; has_more: boolean; next_cursor: string | null }> {
    return apiRequest<{ posts: CommunityPost[]; has_more: boolean; next_cursor: string | null }>(`${API_BASE}/posts/feed/`);
  },

  async getTrendingPosts(): Promise<CommunityPost[]> {
    return apiRequest<CommunityPost[]>(`${API_BASE}/posts/trending/`);
  },

  // Facebook-style Comments with likes and replies
  async getComments(postId?: string): Promise<CommunityComment[]> {
    const url = postId ? `${API_BASE}/comments/?post=${postId}` : `${API_BASE}/comments/`;
    const response = await apiRequest<{ results: CommunityComment[] }>(url);
    return response.results || [];
  },

  async createComment(data: { post: string; content: string; parent?: string }): Promise<CommunityComment> {
    // Temporarily removed requireAuth for testing
    console.log('🎯 API: Creating comment with data:', data);
    console.log('🎯 API: API_BASE:', API_BASE);
    console.log('🎯 API: Full URL will be:', `${API_BASE}/comments/`);

    try {
      const result = await apiRequest<CommunityComment>(`${API_BASE}/comments/`, 'POST', data);
      console.log('✅ API: Comment created successfully:', result);
      return result;
    } catch (error) {
      console.error('❌ API: Comment creation failed:', error);
      throw error;
    }
  },

  async likeComment(commentId: string): Promise<{ liked: boolean; like_count: number }> {
    requireAuth('like comments');
    return apiRequest<{ liked: boolean; like_count: number }>(`${API_BASE}/comments/${commentId}/like/`, 'POST');
  },

  async replyToComment(commentId: string, content: string): Promise<CommunityComment> {
    requireAuth('reply to comments');
    return apiRequest<CommunityComment>(`${API_BASE}/comments/${commentId}/reply/`, 'POST', { content });
  },

  // User follows (REMOVED DUPLICATE - using the correct one below)

  // Stats
  async getStats(): Promise<CommunityStats> {
    const cacheKey = generateCacheKey.communityStats();

    return requestDeduplicator.deduplicate(cacheKey, async () => {
      return apiRequest<CommunityStats>(`${API_BASE}/stats/current/`);
    });
  },

  // Health check
  async ping(): Promise<{ status: string }> {
    return apiRequest<{ status: string }>(`${API_BASE}/ping/`);
  },

  // Search
  async searchPosts(query: string): Promise<CommunityPost[]> {
    const response = await apiRequest<{ results: CommunityPost[]; count: number; next: string | null; previous: string | null }>(`${API_BASE}/posts/?search=${encodeURIComponent(query)}`);
    return response.results;
  },

  // View filtering (new endpoints)
  async getFollowingPosts(): Promise<CommunityPost[]> {
    return apiRequest<CommunityPost[]>(`${API_BASE}/posts/following/`);
  },

  async getSavedPosts(): Promise<CommunityPost[]> {
    return apiRequest<CommunityPost[]>(`${API_BASE}/posts/saved/`);
  },

  // Media Upload
  async uploadMedia(file: File): Promise<{ url: string; type: string }> {
    const formData = new FormData();
    formData.append('file', file);

    return apiRequest<{ url: string; type: string }>(`${API_BASE}/posts/upload_media/`, {
      method: 'POST',
      body: formData,
      headers: {} // Let browser set Content-Type for FormData
    });
  },

  // User Profiles
  async getUserProfile(userId: string): Promise<UserProfile> {
    return apiRequest<UserProfile>(`${API_BASE}/follow/profile/?user_id=${userId}`);
  },

  async getUserPosts(userId: string): Promise<CommunityPost[]> {
    const response = await apiRequest<{ results: CommunityPost[] }>(`${API_BASE}/posts/?author=${userId}`);
    return response.results;
  },

  async followUser(userId: string): Promise<{ following: boolean; followers_count: number; following_count: number }> {
    return apiRequest<{ following: boolean; followers_count: number; following_count: number }>(`${API_BASE}/follow/follow_user/`, 'POST', { user_id: userId });
  },

  async getFollowers(userId: string): Promise<UserProfile[]> {
    return apiRequest<UserProfile[]>(`${API_BASE}/follow/followers/?user_id=${userId}`);
  },

  async getFollowing(userId: string): Promise<UserProfile[]> {
    return apiRequest<UserProfile[]>(`${API_BASE}/follow/following/?user_id=${userId}`);
  },

  // User activity
  async getUserActivity(userId?: string): Promise<any[]> {
    const url = userId ? `${API_BASE}/activity/?user=${userId}` : `${API_BASE}/activity/`;
    return apiRequest<any[]>(url);
  },

  // Real-time features
  async updateOnlineStatus(): Promise<void> {
    return apiRequest<void>(`${API_BASE}/users/update_online_status/`, 'POST');
  },

  async getOnlineUsers(): Promise<UserProfile[]> {
    return apiRequest<UserProfile[]>(`${API_BASE}/users/online/`);
  },

  // Content moderation
  async reportContent(contentType: 'post' | 'comment', contentId: string, action: string, reason: string): Promise<void> {
    return apiRequest<void>(`${API_BASE}/moderation/report/`, 'POST', {
      content_type: contentType,
      content_id: contentId,
      action,
      reason
    });
  },

  async reportPost(id: string, reason: string = 'inappropriate'): Promise<void> {
    return this.reportContent('post', id, reason);
  },

  // Advanced search
  async searchWithFilters(query: string, filters: {
    author?: string;
    dateFrom?: string;
    dateTo?: string;
    tags?: string[];
    contentType?: 'post' | 'comment' | 'all';
    location?: string;
    hasMedia?: boolean;
    minLikes?: number;
  }): Promise<CommunityPost[]> {
    const params = new URLSearchParams({ search: query });

    // Add filters to params
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(key, v));
        } else {
          params.append(key, value.toString());
        }
      }
    });

    const response = await apiRequest<{ results: CommunityPost[] }>(`${API_BASE}/posts/search/?${params}`);
    return response.results;
  },

  // Post analytics
  async getPostAnalytics(postId: string): Promise<any> {
    return apiRequest<any>(`${API_BASE}/posts/${postId}/analytics/`);
  },

  // User discovery and recommendations
  async getUserRecommendations(filters?: {
    role?: string;
    location?: string;
    interests?: string[];
    activity_level?: 'high' | 'medium' | 'low';
    verification_status?: 'verified' | 'unverified' | 'all';
  }): Promise<UserProfile[]> {
    const cacheKey = generateCacheKey.communityUserRecommendations(filters);

    return requestDeduplicator.deduplicate(cacheKey, async () => {
      const params = new URLSearchParams();

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(v => params.append(key, v));
            } else {
              params.append(key, value.toString());
            }
          }
        });
      }

      return apiRequest<UserProfile[]>(`${API_BASE}/users/recommendations/?${params}`);
    });
  },

  async searchUsers(query: string): Promise<UserProfile[]> {
    const response = await apiRequest<{ results: UserProfile[] }>(`${API_BASE}/users/search/?q=${encodeURIComponent(query)}`);
    return response.results;
  },

  // Mention system
  async getMentionSuggestions(query: string): Promise<UserProfile[]> {
    const response = await apiRequest<{ results: UserProfile[] }>(`${API_BASE}/users/mention_suggestions/?q=${encodeURIComponent(query)}`);
    return response.results;
  },

  // Hashtag system
  async getHashtagSuggestions(query: string): Promise<Array<{ tag: string; count: number; trending: boolean }>> {
    return apiRequest<Array<{ tag: string; count: number; trending: boolean }>>(`${API_BASE}/hashtags/suggestions/?q=${encodeURIComponent(query)}`);
  },

  async getTrendingHashtags(): Promise<Array<{ tag: string; count: number; trending: boolean }>> {
    const cacheKey = generateCacheKey.communityTrendingHashtags();

    return requestDeduplicator.deduplicate(cacheKey, async () => {
      return apiRequest<Array<{ tag: string; count: number; trending: boolean }>>(`${API_BASE}/hashtags/trending/`);
    });
  }
};

export default communityApi;
