# Generated by Django for performance optimization

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('community', '0001_initial'),
    ]

    operations = [
        # Add critical missing indexes for CommunityComment
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_community_comment_post_id ON community_communitycomment(post_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_community_comment_post_id;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_community_comment_created_at ON community_communitycomment(created_at DESC);",
            reverse_sql="DROP INDEX IF EXISTS idx_community_comment_created_at;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_community_comment_author_id ON community_communitycomment(author_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_community_comment_author_id;"
        ),
        
        # Add indexes for CommunityPost likes (many-to-many table)
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_community_post_likes_post_id ON community_communitypost_likes(communitypost_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_community_post_likes_post_id;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_community_post_likes_user_id ON community_communitypost_likes(user_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_community_post_likes_user_id;"
        ),
        
        # Add composite index for efficient like counting
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_community_post_likes_composite ON community_communitypost_likes(communitypost_id, user_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_community_post_likes_composite;"
        ),
        
        # Add indexes for PostSave table
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_community_postsave_post_id ON community_postsave(post_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_community_postsave_post_id;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_community_postsave_user_id ON community_postsave(user_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_community_postsave_user_id;"
        ),
    ]
