/**
 * Bundle Optimization Utilities
 *
 * Provides utilities for optimizing bundle size and loading performance
 * including dynamic imports, preloading, and resource optimization.
 */

import React from 'react';

// Dynamic import utilities
export const dynamicImports = {
  // Lazy load components with error handling
  lazyComponent: <T extends React.ComponentType<any>>(
    importFn: () => Promise<{ default: T }>,
    fallback?: React.ComponentType
  ) => {
    const LazyComponent = React.lazy(importFn);
    
    return React.forwardRef<any, React.ComponentProps<T>>((props, ref) => {
      const fallbackElement = fallback ? React.createElement(fallback) : React.createElement('div', {}, 'Loading...');
      return React.createElement(
        React.Suspense,
        { fallback: fallbackElement },
        React.createElement(LazyComponent, { ...props, ref })
      );
    });
  },

  // Preload component for faster loading
  preloadComponent: (importFn: () => Promise<any>) => {
    const componentImport = importFn();
    return componentImport;
  },

  // Load component on interaction
  loadOnInteraction: <T extends React.ComponentType<any>>(
    importFn: () => Promise<{ default: T }>,
    triggerEvents: string[] = ['mouseenter', 'focus']
  ) => {
    let componentPromise: Promise<{ default: T }> | null = null;
    
    const preload = () => {
      if (!componentPromise) {
        componentPromise = importFn();
      }
      return componentPromise;
    };

    const LazyComponent = React.lazy(() => {
      return componentPromise || importFn();
    });

    return React.forwardRef<any, React.ComponentProps<T>>((props, ref) => {
      const elementRef = React.useRef<HTMLElement>(null);

      React.useEffect(() => {
        const element = elementRef.current;
        if (!element) return;

        const handleInteraction = () => preload();

        triggerEvents.forEach(event => {
          element.addEventListener(event, handleInteraction, { once: true });
        });

        return () => {
          triggerEvents.forEach(event => {
            element.removeEventListener(event, handleInteraction);
          });
        };
      }, []);

      return (
        <div ref={elementRef}>
          <React.Suspense fallback={<div>Loading...</div>}>
            <LazyComponent {...props} ref={ref} />
          </React.Suspense>
        </div>
      );
    });
  }
};

// Resource preloading utilities
export const resourcePreloader = {
  // Preload critical resources only when needed
  preloadCriticalResources: () => {
    // Only preload resources that are actually used on the current page
    const currentPath = window.location.pathname;
    const criticalResources: string[] = [];

    // Disable resource preloading to reduce network requests
    // The browser's native loading mechanisms are more efficient
    if (import.meta.env.VITE_DEBUG_PERFORMANCE === 'true') {
      console.log('🚫 Resource preloading disabled to reduce network overhead');
    }

    // Only preload critical fonts for Arabic pages
    const isArabicPage = document.documentElement.dir === 'rtl' ||
                        document.documentElement.lang === 'ar';
    if (isArabicPage && currentPath.includes('/community')) {
      // Only preload one essential Arabic font
      criticalResources.push('https://fonts.gstatic.com/s/tajawal/v11/Iura6YBj_oCad4k1nzGBCw.woff2');
    }

    criticalResources.forEach(resource => {
      // Check if already preloaded
      if (document.querySelector(`link[href="${resource}"]`)) return;

      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = resource;

      if (resource.includes('.woff2')) {
        link.as = 'font';
        link.type = 'font/woff2';
        link.crossOrigin = 'anonymous';
      } else if (resource.includes('.svg')) {
        link.as = 'image';
        link.type = 'image/svg+xml';
      } else if (resource.includes('.webp')) {
        link.as = 'image';
        link.type = 'image/webp';
      }

      document.head.appendChild(link);
    });
  },

  // Preload route chunks
  preloadRouteChunks: (routes: string[]) => {
    routes.forEach(route => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = route;
      document.head.appendChild(link);
    });
  },

  // Preload on hover
  preloadOnHover: (element: HTMLElement, resource: string) => {
    let preloaded = false;
    
    const preload = () => {
      if (preloaded) return;
      preloaded = true;
      
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = resource;
      document.head.appendChild(link);
    };

    element.addEventListener('mouseenter', preload, { once: true });
    element.addEventListener('focus', preload, { once: true });
  }
};

// Image optimization utilities
export const imageOptimization = {
  // Create responsive image with WebP support
  createResponsiveImage: (
    src: string,
    alt: string,
    sizes: { width: number; height: number }[]
  ) => {
    const webpSources = sizes.map(size => 
      `${src.replace(/\.[^.]+$/, '')}-${size.width}x${size.height}.webp ${size.width}w`
    ).join(', ');
    
    const fallbackSources = sizes.map(size => 
      `${src.replace(/\.[^.]+$/, '')}-${size.width}x${size.height}.jpg ${size.width}w`
    ).join(', ');

    return {
      webpSrcSet: webpSources,
      fallbackSrcSet: fallbackSources,
      alt,
      loading: 'lazy' as const,
      decoding: 'async' as const
    };
  },

  // Lazy load images with intersection observer
  lazyLoadImage: (img: HTMLImageElement, src: string) => {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          img.src = src;
          img.classList.remove('lazy');
          observer.unobserve(img);
        }
      });
    }, {
      rootMargin: '50px'
    });

    observer.observe(img);
  }
};

// Code splitting utilities
export const codeSplitting = {
  // Split by route
  createRouteChunk: (routeName: string) => {
    return () => import(`../pages/${routeName}Page.tsx`);
  },

  // Split by feature
  createFeatureChunk: (featureName: string) => {
    return () => import(`../components/${featureName}/index.tsx`);
  },

  // Split vendor libraries
  createVendorChunk: (libraryName: string) => {
    const vendorChunks: Record<string, () => Promise<any>> = {
      'recharts': () => import('recharts'),
      'date': () => import('date-fns'),
      'markdown': () => import('marked'),
      'pdf': () => import('jspdf'),
      'excel': () => import('exceljs')
    };

    return vendorChunks[libraryName] || (() => Promise.reject(`Unknown library: ${libraryName}`));
  }
};

// Performance monitoring for bundle optimization
export const bundlePerformance = {
  // Measure chunk load time
  measureChunkLoadTime: async (chunkName: string, loadFn: () => Promise<any>) => {
    const startTime = performance.now();
    
    try {
      await loadFn();
      const loadTime = performance.now() - startTime;
      
      console.log(`📦 Chunk "${chunkName}" loaded in ${loadTime.toFixed(2)}ms`);
      
      // Store metrics for analysis
      if ('performance' in window && 'measure' in performance) {
        performance.mark(`chunk-${chunkName}-start`);
        performance.mark(`chunk-${chunkName}-end`);
        performance.measure(`chunk-${chunkName}`, `chunk-${chunkName}-start`, `chunk-${chunkName}-end`);
      }
      
      return loadTime;
    } catch (error) {
      console.error(`❌ Failed to load chunk "${chunkName}":`, error);
      throw error;
    }
  },

  // Get bundle size information
  getBundleInfo: () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    
    const jsResources = resources.filter(r => r.name.includes('.js'));
    const cssResources = resources.filter(r => r.name.includes('.css'));
    
    const totalJSSize = jsResources.reduce((sum, r) => sum + (r.transferSize || 0), 0);
    const totalCSSSize = cssResources.reduce((sum, r) => sum + (r.transferSize || 0), 0);
    
    return {
      totalSize: totalJSSize + totalCSSSize,
      jsSize: totalJSSize,
      cssSize: totalCSSSize,
      jsChunks: jsResources.length,
      cssChunks: cssResources.length,
      loadTime: navigation.loadEventEnd - navigation.fetchStart
    };
  },

  // Monitor Core Web Vitals
  monitorWebVitals: () => {
    // Only log performance metrics if debug mode is enabled
    const shouldLog = import.meta.env.VITE_DEBUG_PERFORMANCE === 'true';

    // Largest Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      if (shouldLog) console.log('🎯 LCP:', lastEntry.startTime);
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        if (shouldLog) console.log('⚡ FID:', entry.processingStart - entry.startTime);
      });
    }).observe({ entryTypes: ['first-input'] });

    // Cumulative Layout Shift
    new PerformanceObserver((list) => {
      let clsValue = 0;
      const entries = list.getEntries();
      entries.forEach(entry => {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      });
      if (shouldLog) console.log('📐 CLS:', clsValue);
    }).observe({ entryTypes: ['layout-shift'] });
  }
};

// Initialize bundle optimization
export const initializeBundleOptimization = () => {
  // Preload critical resources
  resourcePreloader.preloadCriticalResources();
  
  // Monitor web vitals in development
  if (process.env.NODE_ENV === 'development') {
    bundlePerformance.monitorWebVitals();
  }
  
  // Log bundle info after load (only in debug mode)
  if (import.meta.env.VITE_DEBUG_PERFORMANCE === 'true') {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const bundleInfo = bundlePerformance.getBundleInfo();
        console.log('📊 Bundle Info:', {
          totalSize: `${(bundleInfo.totalSize / 1024).toFixed(2)} KB`,
          jsSize: `${(bundleInfo.jsSize / 1024).toFixed(2)} KB`,
          cssSize: `${(bundleInfo.cssSize / 1024).toFixed(2)} KB`,
          chunks: bundleInfo.jsChunks + bundleInfo.cssChunks,
          loadTime: `${bundleInfo.loadTime.toFixed(2)}ms`
        });
      }, 1000);
    });
  }
};
