import React, { useState, useEffect } from 'react';
import { Users, UserPlus, TrendingUp, MapPin, Briefcase, Star, Filter, Search, X } from 'lucide-react';
import { communityApi, type UserProfile } from '../services/communityApi';

interface UserDiscoveryProps {
  isOpen: boolean;
  onClose: () => void;
  isRTL: boolean;
}

interface RecommendationReason {
  type: 'mutual_connections' | 'similar_interests' | 'location' | 'role' | 'trending' | 'new_user';
  description: string;
  strength: number; // 1-5 stars
}

interface UserRecommendation extends UserProfile {
  recommendation_reason: RecommendationReason;
  mutual_connections: number;
  similarity_score: number;
  activity_score: number;
}

interface DiscoveryFilters {
  role?: string;
  location?: string;
  interests?: string[];
  activity_level?: 'high' | 'medium' | 'low';
  verification_status?: 'verified' | 'unverified' | 'all';
}

const UserDiscovery: React.FC<UserDiscoveryProps> = ({ isOpen, onClose, isRTL }) => {
  const [recommendations, setRecommendations] = useState<UserRecommendation[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'recommended' | 'trending' | 'new' | 'search'>('recommended');
  const [filters, setFilters] = useState<DiscoveryFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [followingUsers, setFollowingUsers] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (isOpen) {
      loadRecommendations();
    }
  }, [isOpen, activeTab, filters]);

  const loadRecommendations = async () => {
    try {
      setLoading(true);
      
      // ✅ REAL DATA: Fetch user recommendations from API
      try {
        // Try to load the service
        const realDataModule = await import('../services/realDataService').catch(() => null);

        if (realDataModule?.userRecommendationsService) {
          const realRecommendations = await realDataModule.userRecommendationsService.getRecommendations(10);
          setRecommendations(realRecommendations);
          setLoading(false);
          return;
        }
      } catch (error) {
        console.error('Failed to load user recommendations:', error);
      }

      // Fallback to empty state
      setRecommendations([]);
      setLoading(false);

      // Mock data removed - using real API above
    } catch (error) {
      console.error('Failed to load recommendations:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFollow = async (userId: string) => {
    try {
      // ✅ REAL DATA: Use unified real data service
      const realDataModule = await import('../services/realDataService').catch(() => null);

      if (realDataModule?.userRecommendationsService) {
        await realDataModule.userRecommendationsService.followUser(userId);
        setFollowingUsers(prev => new Set([...prev, userId]));

        // Update recommendations
        setRecommendations(prev =>
          prev.map(user =>
            user.id === userId
              ? { ...user, is_following: true, followers_count: user.followers_count + 1 }
              : user
          )
        );
      }
    } catch (error) {
      console.error('Failed to follow user:', error);
    }
  };

  const getRecommendationIcon = (type: RecommendationReason['type']) => {
    switch (type) {
      case 'mutual_connections':
        return <Users className="w-4 h-4 text-blue-400" />;
      case 'similar_interests':
        return <Star className="w-4 h-4 text-yellow-400" />;
      case 'location':
        return <MapPin className="w-4 h-4 text-green-400" />;
      case 'role':
        return <Briefcase className="w-4 h-4 text-purple-400" />;
      case 'trending':
        return <TrendingUp className="w-4 h-4 text-red-400" />;
      case 'new_user':
        return <UserPlus className="w-4 h-4 text-gray-400" />;
      default:
        return <Users className="w-4 h-4 text-gray-400" />;
    }
  };

  const renderStars = (strength: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-3 h-3 ${
          i < strength ? 'text-yellow-400 fill-current' : 'text-gray-600'
        }`}
      />
    ));
  };

  const filteredRecommendations = recommendations.filter(user => {
    if (searchQuery && !user.full_name.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !user.username.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !user.bio?.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    
    if (filters.role && user.user_role !== filters.role) return false;
    if (filters.location && !user.location?.includes(filters.location)) return false;
    if (filters.verification_status === 'verified' && !user.is_verified) return false;
    if (filters.verification_status === 'unverified' && user.is_verified) return false;
    
    return true;
  });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-900 rounded-2xl border border-white/10 w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b border-white/10">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                <Users className="w-5 h-5 text-blue-400" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">
                  {isRTL ? 'اكتشاف المستخدمين' : 'Discover People'}
                </h2>
                <p className="text-sm text-gray-400">
                  {isRTL ? 'اعثر على أشخاص مثيرين للاهتمام للمتابعة' : 'Find interesting people to follow'}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Search and Filters */}
          <div className="flex gap-3">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={isRTL ? 'ابحث عن الأشخاص...' : 'Search people...'}
                className="w-full bg-white/10 border border-white/20 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400"
              />
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-2 rounded-lg border transition-colors ${
                showFilters 
                  ? 'bg-blue-500/20 border-blue-400 text-blue-400'
                  : 'bg-white/10 border-white/20 text-gray-400 hover:text-white'
              }`}
            >
              <Filter className="w-4 h-4" />
            </button>
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="mt-4 p-4 bg-white/5 rounded-lg border border-white/10">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {isRTL ? 'الدور' : 'Role'}
                  </label>
                  <select
                    value={filters.role || ''}
                    onChange={(e) => setFilters(prev => ({ ...prev, role: e.target.value || undefined }))}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white"
                  >
                    <option value="">{isRTL ? 'جميع الأدوار' : 'All Roles'}</option>
                    <option value="Entrepreneur">{isRTL ? 'رائد أعمال' : 'Entrepreneur'}</option>
                    <option value="Investor">{isRTL ? 'مستثمر' : 'Investor'}</option>
                    <option value="Developer">{isRTL ? 'مطور' : 'Developer'}</option>
                    <option value="Mentor">{isRTL ? 'موجه' : 'Mentor'}</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {isRTL ? 'الموقع' : 'Location'}
                  </label>
                  <input
                    type="text"
                    value={filters.location || ''}
                    onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value || undefined }))}
                    placeholder={isRTL ? 'المدينة أو البلد' : 'City or Country'}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {isRTL ? 'حالة التحقق' : 'Verification'}
                  </label>
                  <select
                    value={filters.verification_status || 'all'}
                    onChange={(e) => setFilters(prev => ({ ...prev, verification_status: e.target.value as any }))}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white"
                  >
                    <option value="all">{isRTL ? 'الكل' : 'All'}</option>
                    <option value="verified">{isRTL ? 'محقق' : 'Verified'}</option>
                    <option value="unverified">{isRTL ? 'غير محقق' : 'Unverified'}</option>
                  </select>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Tabs */}
        <div className="border-b border-white/10">
          <div className="flex">
            {[
              { id: 'recommended', label: isRTL ? 'مقترح' : 'Recommended', icon: Star },
              { id: 'trending', label: isRTL ? 'رائج' : 'Trending', icon: TrendingUp },
              { id: 'new', label: isRTL ? 'جديد' : 'New', icon: UserPlus }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex-1 flex items-center justify-center gap-2 px-6 py-4 transition-all ${
                  activeTab === tab.id
                    ? 'text-blue-400 border-b-2 border-blue-400 bg-blue-500/10'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span className="font-medium">{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-gray-400">{isRTL ? 'جاري التحميل...' : 'Loading...'}</p>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredRecommendations.map((user) => (
                <div key={user.id} className="bg-white/5 rounded-lg p-6 border border-white/10 hover:border-white/20 transition-all">
                  {/* User Header */}
                  <div className="flex items-start gap-4 mb-4">
                    <img
                      src={user.avatar || '/api/placeholder/64/64'}
                      alt={user.full_name}
                      className="w-16 h-16 rounded-full border-2 border-white/20"
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-semibold text-white">{user.full_name}</h3>
                        {user.is_verified && (
                          <span className="text-blue-400 text-sm">✓</span>
                        )}
                      </div>
                      <p className="text-gray-400 text-sm">@{user.username}</p>
                      <p className="text-purple-400 text-sm font-medium">{user.user_role}</p>
                    </div>
                  </div>

                  {/* Bio */}
                  {user.bio && (
                    <p className="text-gray-300 text-sm mb-4 line-clamp-2">{user.bio}</p>
                  )}

                  {/* Location */}
                  {user.location && (
                    <div className="flex items-center gap-2 mb-4">
                      <MapPin className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-400 text-sm">{user.location}</span>
                    </div>
                  )}

                  {/* Stats */}
                  <div className="flex items-center gap-4 mb-4 text-sm">
                    <div>
                      <span className="text-white font-medium">{user.followers_count.toLocaleString()}</span>
                      <span className="text-gray-400 ml-1">{isRTL ? 'متابع' : 'followers'}</span>
                    </div>
                    <div>
                      <span className="text-white font-medium">{user.posts_count}</span>
                      <span className="text-gray-400 ml-1">{isRTL ? 'منشور' : 'posts'}</span>
                    </div>
                  </div>

                  {/* Recommendation Reason */}
                  <div className="flex items-center gap-2 mb-4 p-3 bg-white/5 rounded-lg">
                    {getRecommendationIcon(user.recommendation_reason.type)}
                    <div className="flex-1">
                      <p className="text-white text-sm">{user.recommendation_reason.description}</p>
                      <div className="flex items-center gap-1 mt-1">
                        {renderStars(user.recommendation_reason.strength)}
                      </div>
                    </div>
                  </div>

                  {/* Follow Button */}
                  <button
                    onClick={() => handleFollow(user.id)}
                    disabled={user.is_following || followingUsers.has(user.id)}
                    className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${
                      user.is_following || followingUsers.has(user.id)
                        ? 'bg-gray-600 text-gray-300 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }`}
                  >
                    {user.is_following || followingUsers.has(user.id)
                      ? (isRTL ? 'تتم المتابعة' : 'Following')
                      : (isRTL ? 'متابعة' : 'Follow')
                    }
                  </button>
                </div>
              ))}
            </div>
          )}

          {!loading && filteredRecommendations.length === 0 && (
            <div className="text-center py-12">
              <Users className="w-12 h-12 text-gray-600 mx-auto mb-4" />
              <p className="text-gray-400">
                {isRTL ? 'لم يتم العثور على مستخدمين' : 'No users found'}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserDiscovery;
