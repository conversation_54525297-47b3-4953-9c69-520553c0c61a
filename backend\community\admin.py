"""
Community Admin
Django admin interface for community features
"""

from django.contrib import admin
from django.utils.html import format_html
from .models import (
    CommunityPost, PostSave, PostShare, CommunityComment,
    UserFollow, UserActivity, OnlineUser, CommunityStats
)


@admin.register(CommunityPost)
class CommunityPostAdmin(admin.ModelAdmin):
    list_display = ['id', 'author', 'content_preview', 'visibility', 'like_count', 'comments_count', 'created_at']
    list_filter = ['visibility', 'allow_comments', 'created_at']
    search_fields = ['content', 'author__username', 'author__first_name', 'author__last_name']
    readonly_fields = ['like_count', 'save_count', 'shares_count', 'comments_count', 'created_at', 'updated_at']
    filter_horizontal = ['tags', 'likes']
    
    def content_preview(self, obj):
        return obj.content[:100] + '...' if len(obj.content) > 100 else obj.content
    content_preview.short_description = 'Content Preview'


@admin.register(CommunityComment)
class CommunityCommentAdmin(admin.ModelAdmin):
    list_display = ['id', 'author', 'post', 'content_preview', 'like_count', 'created_at']
    list_filter = ['created_at']
    search_fields = ['content', 'author__username', 'post__content']
    readonly_fields = ['like_count', 'created_at', 'updated_at']
    
    def content_preview(self, obj):
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_preview.short_description = 'Content Preview'


@admin.register(UserFollow)
class UserFollowAdmin(admin.ModelAdmin):
    list_display = ['follower', 'following', 'followed_at']
    list_filter = ['followed_at']
    search_fields = ['follower__username', 'following__username']
    readonly_fields = ['followed_at']


@admin.register(UserActivity)
class UserActivityAdmin(admin.ModelAdmin):
    list_display = ['user', 'activity_type', 'content_type', 'object_id', 'created_at']
    list_filter = ['activity_type', 'content_type', 'created_at']
    search_fields = ['user__username']
    readonly_fields = ['created_at']


@admin.register(OnlineUser)
class OnlineUserAdmin(admin.ModelAdmin):
    list_display = ['user', 'is_online', 'last_seen']
    list_filter = ['is_online', 'last_seen']
    search_fields = ['user__username']
    readonly_fields = ['last_seen']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(CommunityStats)
class CommunityStatsAdmin(admin.ModelAdmin):
    list_display = [
        'total_posts', 'total_users', 'total_likes', 'total_comments',
        'posts_today', 'active_users_today', 'updated_at'
    ]
    readonly_fields = [
        'total_posts', 'total_users', 'total_likes', 'total_comments',
        'posts_today', 'active_users_today', 'updated_at'
    ]


@admin.register(PostSave)
class PostSaveAdmin(admin.ModelAdmin):
    list_display = ['user', 'post', 'saved_at']
    list_filter = ['saved_at']
    search_fields = ['user__username', 'post__content']
    readonly_fields = ['saved_at']


@admin.register(PostShare)
class PostShareAdmin(admin.ModelAdmin):
    list_display = ['user', 'post', 'platform', 'shared_at']
    list_filter = ['platform', 'shared_at']
    search_fields = ['user__username', 'post__content']
    readonly_fields = ['shared_at']
