"""
REDIS CACHE CONFIGURATION
High-performance caching setup for Django
"""

import os
from django.core.cache import cache
from django.conf import settings

# Cache configuration for Django settings
CACHE_SETTINGS = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'yasmeen-cache',
        'TIMEOUT': 300,  # 5 minutes default
        'OPTIONS': {
            'MAX_ENTRIES': 10000,
            'CULL_FREQUENCY': 3,
        }
    },
    'sessions': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'yasmeen-sessions',
        'TIMEOUT': 3600,  # 1 hour for sessions
    }
}

# If Redis is available, use it instead
try:
    import redis
    
    # Test Redis connection
    redis_client = redis.Redis(host='localhost', port=6379, db=0)
    redis_client.ping()
    
    # Redis is available, use it
    CACHE_SETTINGS = {
        'default': {
            'BACKEND': 'django.core.cache.backends.redis.RedisCache',
            'LOCATION': 'redis://127.0.0.1:6379/1',
            'TIMEOUT': 300,  # 5 minutes default
            'OPTIONS': {
                'CLIENT_CLASS': 'django_redis.client.DefaultClient',
                'CONNECTION_POOL_KWARGS': {
                    'max_connections': 50,
                    'retry_on_timeout': True,
                },
                'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
                'IGNORE_EXCEPTIONS': True,
            }
        },
        'sessions': {
            'BACKEND': 'django.core.cache.backends.redis.RedisCache',
            'LOCATION': 'redis://127.0.0.1:6379/2',
            'TIMEOUT': 3600,  # 1 hour for sessions
        }
    }
    
    print("[CACHE] Redis cache configured successfully")
    
except (ImportError, redis.ConnectionError, redis.RedisError):
    print("[CACHE] Redis not available, using local memory cache")


class CacheManager:
    """
    Centralized cache management for the application
    """
    
    # Cache timeouts (in seconds)
    TIMEOUTS = {
        'posts': 300,        # 5 minutes
        'comments': 120,     # 2 minutes
        'business_ideas': 600,  # 10 minutes
        'business_plans': 600,  # 10 minutes
        'user_profiles': 900,   # 15 minutes
        'stats': 900,        # 15 minutes
        'auth': 3600,        # 1 hour
    }
    
    @staticmethod
    def get_cache_key(prefix, *args, **kwargs):
        """
        Generate consistent cache keys
        """
        key_parts = [prefix]
        key_parts.extend(str(arg) for arg in args)
        
        if kwargs:
            sorted_kwargs = sorted(kwargs.items())
            key_parts.extend(f"{k}_{v}" for k, v in sorted_kwargs)
        
        return "_".join(key_parts)
    
    @staticmethod
    def cache_view_response(cache_key, data, timeout=None):
        """
        Cache view response data
        """
        if timeout is None:
            timeout = CacheManager.TIMEOUTS.get('posts', 300)
        
        cache.set(cache_key, data, timeout)
    
    @staticmethod
    def get_cached_response(cache_key):
        """
        Get cached response data
        """
        return cache.get(cache_key)
    
    @staticmethod
    def invalidate_pattern(pattern):
        """
        Invalidate cache keys matching a pattern
        """
        try:
            if hasattr(cache, 'delete_pattern'):
                cache.delete_pattern(pattern)
            else:
                # Fallback for non-Redis backends
                # This is less efficient but works
                pass
        except Exception as e:
            print(f"[CACHE] Error invalidating pattern {pattern}: {e}")
    
    @staticmethod
    def warm_critical_caches():
        """
        Pre-warm critical application caches
        """
        from community.optimized_views import warm_community_caches
        from incubator.optimized_views import warm_incubator_caches
        
        try:
            warm_community_caches()
            warm_incubator_caches()
            print("[CACHE] Critical caches warmed successfully")
        except Exception as e:
            print(f"[CACHE] Error warming caches: {e}")
    
    @staticmethod
    def get_cache_stats():
        """
        Get cache statistics for monitoring
        """
        try:
            # This works with Redis backend
            if hasattr(cache, '_cache') and hasattr(cache._cache, 'get_client'):
                client = cache._cache.get_client()
                info = client.info()
                return {
                    'used_memory': info.get('used_memory_human', 'N/A'),
                    'connected_clients': info.get('connected_clients', 'N/A'),
                    'keyspace_hits': info.get('keyspace_hits', 'N/A'),
                    'keyspace_misses': info.get('keyspace_misses', 'N/A'),
                }
        except:
            pass
        
        return {'status': 'Cache stats not available'}


# Cache decorators for views
def cache_view(timeout=300, key_prefix='view'):
    """
    Decorator to cache view responses
    """
    def decorator(view_func):
        def wrapper(self, request, *args, **kwargs):
            # Generate cache key from request
            cache_key = CacheManager.get_cache_key(
                key_prefix,
                request.path,
                request.GET.urlencode()
            )
            
            # Try to get from cache
            cached_response = CacheManager.get_cached_response(cache_key)
            if cached_response:
                return cached_response
            
            # Get fresh data
            response = view_func(self, request, *args, **kwargs)
            
            # Cache the response
            if hasattr(response, 'data'):
                CacheManager.cache_view_response(cache_key, response.data, timeout)
            
            return response
        
        return wrapper
    return decorator


def cache_queryset(timeout=300, key_prefix='queryset'):
    """
    Decorator to cache queryset results
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Generate cache key from function args
            cache_key = CacheManager.get_cache_key(
                key_prefix,
                func.__name__,
                *args,
                **kwargs
            )
            
            # Try to get from cache
            cached_result = CacheManager.get_cached_response(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Get fresh data
            result = func(*args, **kwargs)
            
            # Cache the result
            CacheManager.cache_view_response(cache_key, result, timeout)
            
            return result
        
        return wrapper
    return decorator


# Cache invalidation signals
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver

@receiver(post_save)
def invalidate_cache_on_save(sender, instance, **kwargs):
    """
    Invalidate relevant caches when models are saved
    """
    model_name = sender.__name__.lower()
    
    if model_name in ['communitypost', 'communitycomment']:
        CacheManager.invalidate_pattern('community_*')
    elif model_name in ['businessidea', 'businessplan', 'progressupdate']:
        CacheManager.invalidate_pattern('business_*')
        CacheManager.invalidate_pattern('incubator_*')
    elif model_name in ['user', 'userprofile']:
        CacheManager.invalidate_pattern('user_*')
        CacheManager.invalidate_pattern('auth_*')


@receiver(post_delete)
def invalidate_cache_on_delete(sender, instance, **kwargs):
    """
    Invalidate relevant caches when models are deleted
    """
    invalidate_cache_on_save(sender, instance, **kwargs)


# Performance monitoring
class CachePerformanceMiddleware:
    """
    Middleware to monitor cache performance
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Record cache stats before request
        start_stats = CacheManager.get_cache_stats()
        
        response = self.get_response(request)
        
        # Record cache stats after request
        end_stats = CacheManager.get_cache_stats()
        
        # Log cache performance if needed
        if hasattr(response, 'cache_hit'):
            print(f"[CACHE] {request.path}: {'HIT' if response.cache_hit else 'MISS'}")
        
        return response
