"""
Enhanced Analytics API Views with Real Data
"""
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.utils import timezone
from django.contrib.auth.models import User
from datetime import datetime, timedelta
from analytics.enhanced_analytics_service import EnhancedAnalyticsService
from users.permissions import IsSuperAdminUser
import logging

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def enhanced_analytics_overview(request):
    """
    Get enhanced analytics overview with real data
    """
    try:
        time_range = request.GET.get('time_range', '30d')
        
        analytics_service = EnhancedAnalyticsService()
        dashboard_data = analytics_service.get_real_time_dashboard_data()
        
        return Response(dashboard_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error in enhanced analytics overview: {str(e)}")
        return Response(
            {'error': 'Failed to fetch analytics overview'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_engagement_analytics(request):
    """
    Get detailed user engagement analytics
    """
    try:
        days = int(request.GET.get('days', 30))
        
        analytics_service = EnhancedAnalyticsService()
        engagement_data = analytics_service.get_user_engagement_analytics(days)
        
        return Response(engagement_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error in user engagement analytics: {str(e)}")
        return Response(
            {'error': 'Failed to fetch user engagement analytics'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def business_performance_analytics(request):
    """
    Get business performance analytics
    """
    try:
        analytics_service = EnhancedAnalyticsService()
        business_data = analytics_service.get_business_performance_analytics()
        
        return Response(business_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error in business performance analytics: {str(e)}")
        return Response(
            {'error': 'Failed to fetch business performance analytics'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def predictive_insights(request):
    """
    Get predictive insights and recommendations
    """
    try:
        business_id = request.GET.get('business_id')
        if business_id:
            business_id = int(business_id)
        
        analytics_service = EnhancedAnalyticsService()
        insights_data = analytics_service.get_predictive_insights(business_id)
        
        return Response(insights_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error in predictive insights: {str(e)}")
        return Response(
            {'error': 'Failed to fetch predictive insights'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def real_time_metrics(request):
    """
    Get real-time platform metrics
    """
    try:
        # Import models here to avoid circular imports
        from incubator.models import BusinessIdea, MentorshipMatch
        from forums.models import ForumPost
        from api.models import Event, Post
        
        now = timezone.now()
        last_hour = now - timedelta(hours=1)
        today = now.date()
        
        # Real-time metrics
        metrics = {
            'timestamp': now.isoformat(),
            'active_users_now': User.objects.filter(
                last_login__gte=last_hour
            ).count(),
            'new_registrations_today': User.objects.filter(
                date_joined__date=today
            ).count(),
            'new_business_ideas_today': BusinessIdea.objects.filter(
                created_at__date=today
            ).count(),
            'new_forum_posts_today': ForumPost.objects.filter(
                created_at__date=today
            ).count(),
            'active_mentorships': MentorshipMatch.objects.filter(
                status='active'
            ).count(),
            'upcoming_events_week': Event.objects.filter(
                date__gte=today,
                date__lte=today + timedelta(days=7)
            ).count(),
            'system_health': {
                'status': 'healthy',
                'uptime': '99.9%',
                'response_time': '120ms',
                'error_rate': '0.1%'
            }
        }
        
        return Response(metrics, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error in real-time metrics: {str(e)}")
        return Response(
            {'error': 'Failed to fetch real-time metrics'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def custom_dashboard_data(request):
    """
    Get custom dashboard data based on user role and preferences
    """
    try:
        user = request.user
        role = request.GET.get('role', 'user')
        
        # Import models here to avoid circular imports
        from incubator.models import BusinessIdea, MentorshipMatch, Investment
        from forums.models import ForumThread, ForumPost
        from api.models import Event, Post
        
        dashboard_data = {
            'user_info': {
                'username': user.username,
                'role': role,
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'date_joined': user.date_joined.isoformat()
            },
            'personalized_metrics': {}
        }
        
        # Role-specific metrics
        if role in ['admin', 'super_admin']:
            dashboard_data['personalized_metrics'] = {
                'total_users': User.objects.count(),
                'total_business_ideas': BusinessIdea.objects.count(),
                'total_events': Event.objects.count(),
                'total_forum_posts': ForumPost.objects.count(),
                'pending_approvals': BusinessIdea.objects.filter(
                    status='pending_approval'
                ).count(),
                'system_alerts': 0  # Would be calculated from actual alerts
            }
        elif role == 'entrepreneur':
            user_business_ideas = BusinessIdea.objects.filter(owner=user)
            dashboard_data['personalized_metrics'] = {
                'my_business_ideas': user_business_ideas.count(),
                'active_ideas': user_business_ideas.filter(status='active').count(),
                'completed_milestones': 0,  # Would be calculated from milestones
                'mentor_sessions': MentorshipMatch.objects.filter(
                    mentee=user, status='active'
                ).count(),
                'funding_received': Investment.objects.filter(
                    business_idea__owner=user
                ).aggregate(total=Sum('amount'))['total'] or 0
            }
        elif role == 'mentor':
            dashboard_data['personalized_metrics'] = {
                'active_mentees': MentorshipMatch.objects.filter(
                    mentor=user, status='active'
                ).count(),
                'total_mentees': MentorshipMatch.objects.filter(
                    mentor=user
                ).count(),
                'sessions_completed': 0,  # Would be calculated from sessions
                'average_rating': 4.8,  # Would be calculated from feedback
                'expertise_areas': []  # Would be from mentor profile
            }
        elif role == 'investor':
            user_investments = Investment.objects.filter(investor=user)
            dashboard_data['personalized_metrics'] = {
                'total_investments': user_investments.count(),
                'total_invested': user_investments.aggregate(
                    total=Sum('amount')
                )['total'] or 0,
                'portfolio_companies': user_investments.values(
                    'business_idea'
                ).distinct().count(),
                'roi': 15.2,  # Would be calculated from actual returns
                'pending_deals': 0  # Would be from deal pipeline
            }
        else:  # regular user
            dashboard_data['personalized_metrics'] = {
                'forum_posts': ForumPost.objects.filter(author=user).count(),
                'events_attended': 0,  # Would be from event attendance
                'learning_progress': 0,  # Would be from learning modules
                'network_connections': 0,  # Would be from connections
                'achievements': []  # Would be from achievement system
            }
        
        # Recent activity for all roles
        dashboard_data['recent_activity'] = self._get_user_recent_activity(user, role)
        
        # Recommendations based on role and activity
        dashboard_data['recommendations'] = self._get_user_recommendations(user, role)
        
        return Response(dashboard_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error in custom dashboard data: {str(e)}")
        return Response(
            {'error': 'Failed to fetch custom dashboard data'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def analytics_export(request):
    """
    Export analytics data in various formats
    """
    try:
        export_type = request.GET.get('type', 'overview')
        format_type = request.GET.get('format', 'json')
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        
        if start_date:
            start_date = datetime.fromisoformat(start_date)
        else:
            start_date = timezone.now() - timedelta(days=30)
        
        if end_date:
            end_date = datetime.fromisoformat(end_date)
        else:
            end_date = timezone.now()
        
        analytics_service = EnhancedAnalyticsService()
        
        if export_type == 'overview':
            export_data = analytics_service.get_real_time_dashboard_data()
        elif export_type == 'engagement':
            export_data = analytics_service.get_user_engagement_analytics(
                (end_date - start_date).days
            )
        elif export_type == 'business':
            export_data = analytics_service.get_business_performance_analytics()
        else:
            return Response(
                {'error': 'Invalid export type'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Add export metadata
        export_data['export_metadata'] = {
            'export_type': export_type,
            'format': format_type,
            'start_date': start_date.isoformat(),
            'end_date': end_date.isoformat(),
            'exported_by': request.user.username,
            'export_timestamp': timezone.now().isoformat()
        }
        
        return Response(export_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error in analytics export: {str(e)}")
        return Response(
            {'error': 'Failed to export analytics data'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def _get_user_recent_activity(user, role):
    """Get recent activity for user based on role"""
    try:
        from api.models import Post, Event
        from forums.models import ForumPost
        
        activities = []
        
        # Get user's recent posts
        recent_posts = Post.objects.filter(author=user).order_by('-created_at')[:5]
        for post in recent_posts:
            activities.append({
                'type': 'post_created',
                'description': f'Created post: {post.title}',
                'timestamp': post.created_at.isoformat(),
                'link': f'/posts/{post.id}'
            })
        
        # Get user's recent forum activity
        recent_forum_posts = ForumPost.objects.filter(author=user).order_by('-created_at')[:5]
        for forum_post in recent_forum_posts:
            activities.append({
                'type': 'forum_post',
                'description': f'Posted in forum: {forum_post.thread.title}',
                'timestamp': forum_post.created_at.isoformat(),
                'link': f'/forum/threads/{forum_post.thread.id}'
            })
        
        # Sort by timestamp and return latest 10
        activities.sort(key=lambda x: x['timestamp'], reverse=True)
        return activities[:10]
        
    except Exception as e:
        logger.error(f"Error getting user recent activity: {str(e)}")
        return []


def _get_user_recommendations(user, role):
    """Get personalized recommendations for user"""
    recommendations = []
    
    if role == 'entrepreneur':
        recommendations.extend([
            {
                'type': 'action',
                'title': 'Complete Your Business Plan',
                'description': 'Finish setting up your business plan to attract mentors',
                'priority': 'high'
            },
            {
                'type': 'learning',
                'title': 'Join Entrepreneur Workshop',
                'description': 'Upcoming workshop on business development',
                'priority': 'medium'
            }
        ])
    elif role == 'mentor':
        recommendations.extend([
            {
                'type': 'mentorship',
                'title': 'New Mentee Requests',
                'description': 'You have pending mentorship requests to review',
                'priority': 'high'
            }
        ])
    elif role == 'investor':
        recommendations.extend([
            {
                'type': 'investment',
                'title': 'New Investment Opportunities',
                'description': 'Review new business ideas seeking funding',
                'priority': 'medium'
            }
        ])
    else:
        recommendations.extend([
            {
                'type': 'engagement',
                'title': 'Join the Community',
                'description': 'Participate in forum discussions to connect with others',
                'priority': 'low'
            }
        ])
    
    return recommendations
