/**
 * 🎯 UNIFIED ROLE MANAGEMENT HOOK
 * Single source of truth for all role management in the application
 *
 * This hook replaces all other role hooks and provides consistent
 * capability-based access control across the entire application.
 */

import { useCallback, useMemo } from 'react';
import { useAppSelector } from '../store/hooks';
import { unifiedRoleService, type UnifiedRoleInfo } from '../services/unifiedRoleService';
import { type RoleCapabilities, type NavigationItem } from '../config/roleConfig';

export interface UnifiedRoleState extends UnifiedRoleInfo {
  // User info
  user: any;

  // Capability checkers
  canAccess: (capability: keyof RoleCapabilities) => boolean;
  canAccessRoute: (route: string) => boolean;

  // Navigation helpers
  getDashboardRoute: () => string;
  getHomeRoute: () => string;
  shouldShowSidebar: () => boolean;
}

export function useUnifiedRoles(): UnifiedRoleState {
  const { user, isAuthenticated } = useAppSelector(state => state.auth);

  // ✅ FIXED: Get comprehensive role information using unified service with debugging
  const roleInfo = useMemo(() => {
    console.log('🔄 useUnifiedRoles: Auth state changed', {
      userId: user?.id,
      username: user?.username,
      isAuthenticated,
      userRole: user?.user_role
    });

    const info = unifiedRoleService.getRoleInfo(user, isAuthenticated);

    console.log('✅ useUnifiedRoles: Role info extracted', {
      primaryRole: info.primaryRole,
      capabilities: Object.keys(info.capabilities).filter(key => info.capabilities[key as keyof RoleCapabilities]),
      sidebarType: info.sidebarType
    });

    return info;
  }, [user, isAuthenticated]);

  // Capability checker
  const canAccess = useCallback((capability: keyof RoleCapabilities): boolean => {
    return unifiedRoleService.hasCapability(roleInfo.primaryRole, capability);
  }, [roleInfo.primaryRole]);

  // Route access checker
  const canAccessRoute = useCallback((route: string): boolean => {
    return unifiedRoleService.canAccessRoute(roleInfo.primaryRole, route);
  }, [roleInfo.primaryRole]);

  // Navigation helpers
  const getDashboardRoute = useCallback((): string => {
    return unifiedRoleService.getDashboardRoute(roleInfo.primaryRole);
  }, [roleInfo.primaryRole]);

  const getHomeRoute = useCallback((): string => {
    return unifiedRoleService.getHomeRoute(roleInfo.primaryRole);
  }, [roleInfo.primaryRole]);

  const shouldShowSidebar = useCallback((): boolean => {
    return roleInfo.sidebarType !== 'none';
  }, [roleInfo.sidebarType]);

  return {
    // Spread all role info from service
    ...roleInfo,

    // Add user reference
    user,

    // Add function helpers
    canAccess,
    canAccessRoute,
    getDashboardRoute,
    getHomeRoute,
    shouldShowSidebar
  };
}

// Convenience hooks for specific capabilities
export function useCanAccessDashboard(): boolean {
  const { canAccess } = useUnifiedRoles();
  return canAccess('canAccessDashboard');
}

export function useCanManageUsers(): boolean {
  const { canAccess } = useUnifiedRoles();
  return canAccess('canManageUsers');
}

export function useCanAccessAI(): boolean {
  const { canAccess } = useUnifiedRoles();
  return canAccess('canAccessAI');
}

export function useCanCreateBusinessPlans(): boolean {
  const { canAccess } = useUnifiedRoles();
  return canAccess('canCreateBusinessPlans');
}

export function useCanModerateContent(): boolean {
  const { canAccess } = useUnifiedRoles();
  return canAccess('canModerateContent');
}

export function useCanAccessAnalytics(): boolean {
  const { canAccess } = useUnifiedRoles();
  return canAccess('canAccessAnalytics');
}

export function useCanAccessSystemSettings(): boolean {
  const { canAccess } = useUnifiedRoles();
  return canAccess('canAccessSystemSettings');
}

// Role-based component wrapper
export function useRoleGuard(requiredCapability: keyof RoleCapabilities) {
  const { validateAccess } = useUnifiedRoles();
  return validateAccess(requiredCapability);
}
