/**
 * Guest Onboarding Component
 * Enhanced onboarding experience for unauthenticated users
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useCommunityStats } from '../../hooks/useCommunityStats';
import { 
  Users, 
  MessageCircle, 
  TrendingUp, 
  BookOpen, 
  Sparkles, 
  ArrowRight, 
  X,
  Heart,
  Share2,
  Bookmark
} from 'lucide-react';

interface GuestOnboardingProps {
  onClose?: () => void;
  showWelcomeModal?: boolean;
}

export const GuestOnboarding: React.FC<GuestOnboardingProps> = ({
  onClose,
  showWelcomeModal = false
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { stats } = useCommunityStats();
  const [currentStep, setCurrentStep] = useState(0);
  const [showModal, setShowModal] = useState(showWelcomeModal);

  const features = [
    {
      icon: Users,
      title: t('community.onboarding.connect.title', 'Connect with Peers'),
      description: t('community.onboarding.connect.description', 'Join a vibrant community of Syrian data scientists and AI enthusiasts'),
      color: 'from-purple-500 to-blue-500'
    },
    {
      icon: MessageCircle,
      title: t('community.onboarding.discuss.title', 'Share Knowledge'),
      description: t('community.onboarding.discuss.description', 'Engage in meaningful discussions about AI, data science, and technology'),
      color: 'from-blue-500 to-indigo-500'
    },
    {
      icon: TrendingUp,
      title: t('community.onboarding.grow.title', 'Grow Together'),
      description: t('community.onboarding.grow.description', 'Learn from experts and contribute to the growing AI community'),
      color: 'from-indigo-500 to-purple-500'
    },
    {
      icon: BookOpen,
      title: t('community.onboarding.resources.title', 'Access Resources'),
      description: t('community.onboarding.resources.description', 'Get access to exclusive content, tutorials, and learning materials'),
      color: 'from-green-500 to-teal-500'
    }
  ];

  const handleJoinCommunity = () => {
    window.location.href = '/register';
  };

  const handleSignIn = () => {
    window.location.href = '/login';
  };

  const handleCloseModal = () => {
    setShowModal(false);
    onClose?.();
  };

  // Auto-advance steps
  useEffect(() => {
    if (showModal) {
      const interval = setInterval(() => {
        setCurrentStep((prev) => (prev + 1) % features.length);
      }, 4000);
      return () => clearInterval(interval);
    }
  }, [showModal, features.length]);

  if (!showModal) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gradient-to-br from-gray-900/95 to-purple-900/95 backdrop-blur-xl border border-white/20 rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <div className="relative p-6 pb-0">
          <button
            onClick={handleCloseModal}
            className="absolute top-4 right-4 text-white/70 hover:text-white transition-colors"
            aria-label="Close"
          >
            <X className="w-6 h-6" />
          </button>
          
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
              <Sparkles className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-white mb-2">
              {t('community.onboarding.welcome.title', 'Welcome to Yasmeen AI Community!')}
            </h2>
            <p className="text-gray-300">
              {t('community.onboarding.welcome.subtitle', 'Discover what makes our community special')}
            </p>
          </div>
        </div>

        {/* Feature Showcase */}
        <div className="px-6 py-4">
          <div className="relative h-48 mb-6">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              const isActive = index === currentStep;
              
              return (
                <div
                  key={index}
                  className={`absolute inset-0 transition-all duration-500 ${
                    isActive ? 'opacity-100 transform translate-x-0' : 'opacity-0 transform translate-x-4'
                  }`}
                >
                  <div className="flex items-center gap-6 h-full">
                    <div className={`w-20 h-20 bg-gradient-to-r ${feature.color} rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg`}>
                      <Icon className="w-10 h-10 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-white mb-2">
                        {feature.title}
                      </h3>
                      <p className="text-gray-300 leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Progress Indicators */}
          <div className="flex justify-center gap-2 mb-6">
            {features.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentStep(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentStep 
                    ? 'bg-purple-400 scale-125' 
                    : 'bg-white/30 hover:bg-white/50'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Community Stats */}
        <div className="px-6 py-4 border-t border-white/10">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-white mb-1">
                {stats?.total_users || 0}
              </div>
              <div className="text-xs text-gray-400">
                {t('community.onboarding.stats.members', 'Active Members')}
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-white mb-1">
                {stats?.posts_today || 0}
              </div>
              <div className="text-xs text-gray-400">
                {t('community.onboarding.stats.posts', 'Posts Today')}
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-white mb-1">
                {stats?.total_posts || 0}
              </div>
              <div className="text-xs text-gray-400">
                {t('community.onboarding.stats.totalPosts', 'Total Posts')}
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="p-6 pt-4">
          <div className="flex flex-col sm:flex-row gap-3">
            <button
              onClick={handleJoinCommunity}
              className="flex-1 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 hover:scale-105 hover:shadow-lg flex items-center justify-center gap-2"
            >
              <Sparkles className="w-5 h-5" />
              {t('community.onboarding.joinButton', 'Join Community')}
              <ArrowRight className="w-4 h-4" />
            </button>
            <button
              onClick={handleSignIn}
              className="flex-1 bg-white/10 hover:bg-white/20 border border-white/20 hover:border-white/30 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 hover:scale-105 backdrop-blur-sm"
            >
              {t('community.onboarding.signInButton', 'Sign In')}
            </button>
          </div>
          
          <div className="text-center mt-4">
            <button
              onClick={handleCloseModal}
              className="text-gray-400 hover:text-white text-sm transition-colors"
            >
              {t('community.onboarding.browseAsGuest', 'Continue browsing as guest')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

interface FeatureHighlightProps {
  isAuthenticated: boolean;
}

export const FeatureHighlight: React.FC<FeatureHighlightProps> = ({ isAuthenticated }) => {
  const { t } = useTranslation();

  if (isAuthenticated) return null;

  return (
    <div className="bg-gradient-to-r from-purple-600/10 to-blue-600/10 border border-purple-500/20 rounded-xl p-4 mb-6">
      <div className="flex items-start gap-4">
        <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
          <Heart className="w-5 h-5 text-white" />
        </div>
        <div className="flex-1">
          <h3 className="text-white font-semibold mb-2">
            {t('community.highlight.title', 'Join the conversation!')}
          </h3>
          <p className="text-gray-300 text-sm mb-3">
            {t('community.highlight.description', 'Sign up to like posts, share your thoughts, and connect with fellow AI enthusiasts.')}
          </p>
          <div className="flex items-center gap-4 text-xs text-gray-400">
            <div className="flex items-center gap-1">
              <Heart className="w-3 h-3" />
              <span>Like posts</span>
            </div>
            <div className="flex items-center gap-1">
              <MessageCircle className="w-3 h-3" />
              <span>Comment</span>
            </div>
            <div className="flex items-center gap-1">
              <Share2 className="w-3 h-3" />
              <span>Share</span>
            </div>
            <div className="flex items-center gap-1">
              <Bookmark className="w-3 h-3" />
              <span>Save</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GuestOnboarding;
