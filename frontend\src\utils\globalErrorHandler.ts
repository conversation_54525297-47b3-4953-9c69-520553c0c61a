/**
 * Global Error Handler
 * Centralized error handling for the entire application
 */

export interface ErrorReport {
  message: string;
  stack?: string;
  url?: string;
  lineNumber?: number;
  columnNumber?: number;
  timestamp: Date;
  userAgent: string;
  userId?: string;
  sessionId: string;
  errorType: 'javascript' | 'promise' | 'network' | 'chunk' | 'custom';
  severity: 'low' | 'medium' | 'high' | 'critical';
  context?: Record<string, any>;
}

export interface ErrorHandlerConfig {
  enableConsoleLogging: boolean;
  enableRemoteLogging: boolean;
  remoteEndpoint?: string;
  maxErrorsPerSession: number;
  enableUserNotification: boolean;
  enableRetryMechanism: boolean;
}

class GlobalErrorHandler {
  private config: ErrorHandlerConfig;
  private errorCount = 0;
  private sessionId: string;
  private errorQueue: ErrorReport[] = [];

  constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = {
      enableConsoleLogging: true,
      enableRemoteLogging: false,
      maxErrorsPerSession: 50,
      enableUserNotification: true,
      enableRetryMechanism: true,
      ...config
    };

    this.sessionId = this.generateSessionId();
    this.setupErrorListeners();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private setupErrorListeners() {
    // Handle JavaScript errors
    window.addEventListener('error', (event) => {
      this.handleError({
        message: event.message,
        stack: event.error?.stack,
        url: event.filename,
        lineNumber: event.lineno,
        columnNumber: event.colno,
        timestamp: new Date(),
        userAgent: navigator.userAgent,
        sessionId: this.sessionId,
        errorType: 'javascript',
        severity: this.determineSeverity(event.error)
      });
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError({
        message: event.reason?.message || 'Unhandled Promise Rejection',
        stack: event.reason?.stack,
        timestamp: new Date(),
        userAgent: navigator.userAgent,
        sessionId: this.sessionId,
        errorType: 'promise',
        severity: this.determineSeverity(event.reason),
        context: { reason: event.reason }
      });
    });

    // Handle network errors (fetch failures)
    this.interceptFetch();
  }

  private interceptFetch() {
    const originalFetch = window.fetch;
    
    window.fetch = async (...args) => {
      try {
        const response = await originalFetch(...args);
        
        if (!response.ok) {
          this.handleError({
            message: `Network error: ${response.status} ${response.statusText}`,
            timestamp: new Date(),
            userAgent: navigator.userAgent,
            sessionId: this.sessionId,
            errorType: 'network',
            severity: response.status >= 500 ? 'high' : 'medium',
            context: {
              url: args[0],
              status: response.status,
              statusText: response.statusText
            }
          });
        }
        
        return response;
      } catch (error) {
        this.handleError({
          message: `Fetch failed: ${error.message}`,
          stack: error.stack,
          timestamp: new Date(),
          userAgent: navigator.userAgent,
          sessionId: this.sessionId,
          errorType: 'network',
          severity: 'high',
          context: { url: args[0], error }
        });
        throw error;
      }
    };
  }

  private determineSeverity(error: any): ErrorReport['severity'] {
    if (!error) return 'low';
    
    const message = error.message?.toLowerCase() || '';
    
    // Critical errors
    if (message.includes('out of memory') || 
        message.includes('maximum call stack') ||
        message.includes('script error')) {
      return 'critical';
    }
    
    // High severity errors
    if (message.includes('chunkloaderror') ||
        message.includes('network error') ||
        message.includes('failed to fetch')) {
      return 'high';
    }
    
    // Medium severity errors
    if (message.includes('permission denied') ||
        message.includes('unauthorized') ||
        message.includes('validation')) {
      return 'medium';
    }
    
    return 'low';
  }

  public handleError(errorReport: ErrorReport) {
    // Prevent error spam
    if (this.errorCount >= this.config.maxErrorsPerSession) {
      return;
    }
    
    this.errorCount++;
    this.errorQueue.push(errorReport);

    // Console logging
    if (this.config.enableConsoleLogging) {
      console.group(`🚨 Error [${errorReport.severity.toUpperCase()}]`);
      console.error('Message:', errorReport.message);
      console.error('Type:', errorReport.errorType);
      console.error('Timestamp:', errorReport.timestamp);
      if (errorReport.stack) console.error('Stack:', errorReport.stack);
      if (errorReport.context) console.error('Context:', errorReport.context);
      console.groupEnd();
    }

    // Remote logging
    if (this.config.enableRemoteLogging && this.config.remoteEndpoint) {
      this.sendErrorToRemote(errorReport);
    }

    // User notification for critical errors
    if (this.config.enableUserNotification && errorReport.severity === 'critical') {
      this.notifyUser(errorReport);
    }
  }

  private async sendErrorToRemote(errorReport: ErrorReport) {
    try {
      await fetch(this.config.remoteEndpoint!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorReport)
      });
    } catch (error) {
      console.error('Failed to send error report:', error);
    }
  }

  private notifyUser(errorReport: ErrorReport) {
    // Create a user-friendly notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-red-600 text-white p-4 rounded-lg shadow-lg z-50 max-w-sm';
    notification.innerHTML = `
      <div class="flex items-start gap-3">
        <span class="text-xl">⚠️</span>
        <div class="flex-1">
          <h4 class="font-semibold mb-1">Critical Error</h4>
          <p class="text-sm opacity-90">The application encountered a critical error. Please refresh the page.</p>
          <button class="mt-2 text-xs bg-white/20 hover:bg-white/30 px-2 py-1 rounded" onclick="window.location.reload()">
            Refresh Page
          </button>
        </div>
        <button class="text-white/70 hover:text-white" onclick="this.parentElement.parentElement.remove()">
          ✕
        </button>
      </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 10000);
  }

  public getErrorReports(): ErrorReport[] {
    return [...this.errorQueue];
  }

  public clearErrorReports() {
    this.errorQueue = [];
    this.errorCount = 0;
  }

  public updateConfig(newConfig: Partial<ErrorHandlerConfig>) {
    this.config = { ...this.config, ...newConfig };
  }
}

// Create and export global instance
export const globalErrorHandler = new GlobalErrorHandler({
  enableConsoleLogging: import.meta.env.DEV,
  enableRemoteLogging: import.meta.env.PROD,
  remoteEndpoint: import.meta.env.VITE_ERROR_REPORTING_ENDPOINT,
  enableUserNotification: true,
  enableRetryMechanism: true
});

// Initialize error handling
export const initializeErrorHandling = () => {
  console.log('🛡️ Global error handling initialized');
  return globalErrorHandler;
};
