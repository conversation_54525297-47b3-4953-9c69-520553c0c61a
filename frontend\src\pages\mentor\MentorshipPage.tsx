/**
 * Mentorship Page - Mentor Dashboard
 * Comprehensive mentorship management and tracking
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import {
  Users,
  Calendar,
  MessageSquare,
  Star,
  Clock,
  TrendingUp,
  Award,
  Target,
  BookOpen,
  Video,
  Phone,
  Mail,
  CheckCircle,
  AlertCircle,
  Plus,
  Filter,
  Search,
  BarChart3,
  User,
  Heart,
  Zap,
  Globe
} from 'lucide-react';

interface Mentee {
  id: string;
  name: string;
  avatar?: string;
  businessIdea: string;
  industry: string;
  stage: 'idea' | 'planning' | 'development' | 'launch' | 'growth';
  joinedAt: string;
  lastSession: string;
  nextSession?: string;
  progress: number;
  rating: number;
  totalSessions: number;
  goals: string[];
  status: 'active' | 'paused' | 'completed' | 'inactive';
}

interface MentorshipSession {
  id: string;
  menteeId: string;
  menteeName: string;
  type: 'video' | 'phone' | 'in_person' | 'chat';
  scheduledAt: string;
  duration: number;
  status: 'scheduled' | 'completed' | 'cancelled' | 'no_show';
  topic: string;
  notes?: string;
  rating?: number;
}

const MentorshipPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [mentees, setMentees] = useState<Mentee[]>([]);
  const [upcomingSessions, setUpcomingSessions] = useState<MentorshipSession[]>([]);
  const [selectedTab, setSelectedTab] = useState<'mentees' | 'sessions' | 'analytics'>('mentees');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');

  useEffect(() => {
    loadMentorshipData();
  }, []);

  const loadMentorshipData = async () => {
    setLoading(true);
    try {
      // Simulate API call - replace with real API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setMentees([
        {
          id: '1',
          name: 'Sarah Al-Mahmoud',
          avatar: '/avatars/sarah.jpg',
          businessIdea: 'Sustainable Fashion Brand',
          industry: 'Fashion & Retail',
          stage: 'planning',
          joinedAt: '2024-01-10T10:00:00Z',
          lastSession: '2024-01-14T15:00:00Z',
          nextSession: '2024-01-18T14:00:00Z',
          progress: 65,
          rating: 4.8,
          totalSessions: 8,
          goals: ['Business Plan Development', 'Market Research', 'Funding Strategy'],
          status: 'active'
        },
        {
          id: '2',
          name: 'Ahmad Khalil',
          avatar: '/avatars/ahmad.jpg',
          businessIdea: 'EdTech Platform for Arabic Learning',
          industry: 'Education Technology',
          stage: 'development',
          joinedAt: '2024-01-05T09:00:00Z',
          lastSession: '2024-01-15T11:00:00Z',
          nextSession: '2024-01-19T16:00:00Z',
          progress: 80,
          rating: 4.9,
          totalSessions: 12,
          goals: ['Product Development', 'User Testing', 'Go-to-Market Strategy'],
          status: 'active'
        },
        {
          id: '3',
          name: 'Layla Hassan',
          businessIdea: 'Organic Food Delivery Service',
          industry: 'Food & Beverage',
          stage: 'idea',
          joinedAt: '2024-01-12T14:00:00Z',
          lastSession: '2024-01-16T10:00:00Z',
          progress: 25,
          rating: 4.5,
          totalSessions: 3,
          goals: ['Idea Validation', 'Market Analysis', 'Business Model'],
          status: 'active'
        },
        {
          id: '4',
          name: 'Omar Abdallah',
          businessIdea: 'Renewable Energy Solutions',
          industry: 'Clean Energy',
          stage: 'launch',
          joinedAt: '2023-12-01T08:00:00Z',
          lastSession: '2024-01-13T13:00:00Z',
          progress: 95,
          rating: 4.7,
          totalSessions: 20,
          goals: ['Launch Strategy', 'Customer Acquisition', 'Scaling'],
          status: 'completed'
        }
      ]);

      setUpcomingSessions([
        {
          id: '1',
          menteeId: '1',
          menteeName: 'Sarah Al-Mahmoud',
          type: 'video',
          scheduledAt: '2024-01-18T14:00:00Z',
          duration: 60,
          status: 'scheduled',
          topic: 'Business Plan Review and Feedback'
        },
        {
          id: '2',
          menteeId: '2',
          menteeName: 'Ahmad Khalil',
          type: 'video',
          scheduledAt: '2024-01-19T16:00:00Z',
          duration: 90,
          status: 'scheduled',
          topic: 'Product Development Strategy'
        },
        {
          id: '3',
          menteeId: '3',
          menteeName: 'Layla Hassan',
          type: 'phone',
          scheduledAt: '2024-01-20T11:00:00Z',
          duration: 45,
          status: 'scheduled',
          topic: 'Market Research Discussion'
        }
      ]);
    } catch (error) {
      console.error('Error loading mentorship data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'idea':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'planning':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'development':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'launch':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'growth':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'completed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'inactive':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSessionTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Video className="w-4 h-4" />;
      case 'phone':
        return <Phone className="w-4 h-4" />;
      case 'in_person':
        return <Users className="w-4 h-4" />;
      case 'chat':
        return <MessageSquare className="w-4 h-4" />;
      default:
        return <Calendar className="w-4 h-4" />;
    }
  };

  const filteredMentees = mentees.filter(mentee => {
    const matchesSearch = searchQuery === '' || 
      mentee.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      mentee.businessIdea.toLowerCase().includes(searchQuery.toLowerCase()) ||
      mentee.industry.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesFilter = selectedFilter === 'all' || mentee.status === selectedFilter;
    
    return matchesSearch && matchesFilter;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 text-white p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
          <span className="ml-4 text-lg">{t('Loading mentorship data...')}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className={`flex items-center justify-between mb-8 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="w-12 h-12 bg-purple-600/30 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-purple-400" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">{t('My Mentorship')}</h1>
              <p className="text-purple-200">{t('Guide and support aspiring entrepreneurs')}</p>
            </div>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div>
                <p className="text-purple-200 text-sm">{t('Active Mentees')}</p>
                <p className="text-2xl font-bold">{mentees.filter(m => m.status === 'active').length}</p>
              </div>
              <Users className="w-8 h-8 text-purple-400" />
            </div>
          </div>

          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div>
                <p className="text-purple-200 text-sm">{t('Total Sessions')}</p>
                <p className="text-2xl font-bold">{mentees.reduce((sum, m) => sum + m.totalSessions, 0)}</p>
              </div>
              <Calendar className="w-8 h-8 text-blue-400" />
            </div>
          </div>

          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div>
                <p className="text-purple-200 text-sm">{t('Average Rating')}</p>
                <p className="text-2xl font-bold">{(mentees.reduce((sum, m) => sum + m.rating, 0) / mentees.length).toFixed(1)}</p>
              </div>
              <Star className="w-8 h-8 text-yellow-400" />
            </div>
          </div>

          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div>
                <p className="text-purple-200 text-sm">{t('Upcoming Sessions')}</p>
                <p className="text-2xl font-bold">{upcomingSessions.length}</p>
              </div>
              <Clock className="w-8 h-8 text-green-400" />
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg border border-indigo-800/50 mb-6">
          <div className="flex border-b border-indigo-800/50">
            {[
              { id: 'mentees', label: t('My Mentees'), icon: Users },
              { id: 'sessions', label: t('Upcoming Sessions'), icon: Calendar },
              { id: 'analytics', label: t('Analytics'), icon: BarChart3 }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setSelectedTab(tab.id as any)}
                  className={`flex items-center gap-2 px-6 py-4 font-medium transition-colors ${
                    selectedTab === tab.id
                      ? 'text-purple-400 border-b-2 border-purple-400'
                      : 'text-purple-200 hover:text-white'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                </button>
              );
            })}
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {selectedTab === 'mentees' && (
              <div>
                {/* Search and Filter */}
                <div className={`flex items-center gap-4 mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className="flex items-center gap-2 flex-1 max-w-md">
                    <Search className="w-4 h-4 text-purple-400" />
                    <input
                      type="text"
                      placeholder={t('Search mentees...')}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="flex-1 bg-indigo-900/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-purple-300 text-sm"
                    />
                  </div>

                  <div className="flex items-center gap-2">
                    <Filter className="w-4 h-4 text-purple-400" />
                    <select
                      value={selectedFilter}
                      onChange={(e) => setSelectedFilter(e.target.value)}
                      className="bg-indigo-900/50 border border-indigo-700 rounded-lg px-3 py-2 text-white text-sm"
                    >
                      <option value="all">{t('All Status')}</option>
                      <option value="active">{t('Active')}</option>
                      <option value="paused">{t('Paused')}</option>
                      <option value="completed">{t('Completed')}</option>
                      <option value="inactive">{t('Inactive')}</option>
                    </select>
                  </div>
                </div>

                {/* Mentees Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredMentees.map((mentee) => (
                    <div key={mentee.id} className="bg-indigo-900/20 rounded-lg p-6 border border-indigo-800/30 hover:border-purple-500/50 transition-all duration-300">
                      {/* Header */}
                      <div className={`flex items-center gap-3 mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                          {mentee.name.charAt(0)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-white truncate">{mentee.name}</h3>
                          <p className="text-sm text-purple-200 truncate">{mentee.businessIdea}</p>
                        </div>
                      </div>

                      {/* Status and Stage */}
                      <div className={`flex items-center gap-2 mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(mentee.status)}`}>
                          {t(mentee.status)}
                        </span>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStageColor(mentee.stage)}`}>
                          {t(mentee.stage)}
                        </span>
                      </div>

                      {/* Progress */}
                      <div className="mb-4">
                        <div className={`flex items-center justify-between mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <span className="text-sm text-purple-200">{t('Progress')}</span>
                          <span className="text-sm font-medium text-white">{mentee.progress}%</span>
                        </div>
                        <div className="w-full bg-indigo-900/50 rounded-full h-2">
                          <div 
                            className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${mentee.progress}%` }}
                          />
                        </div>
                      </div>

                      {/* Stats */}
                      <div className="grid grid-cols-3 gap-2 mb-4 text-center">
                        <div>
                          <div className="text-lg font-semibold text-white">{mentee.totalSessions}</div>
                          <div className="text-xs text-purple-200">{t('Sessions')}</div>
                        </div>
                        <div>
                          <div className="flex items-center justify-center gap-1">
                            <Star className="w-3 h-3 text-yellow-400" />
                            <span className="text-lg font-semibold text-white">{mentee.rating}</span>
                          </div>
                          <div className="text-xs text-purple-200">{t('Rating')}</div>
                        </div>
                        <div>
                          <div className="text-lg font-semibold text-white">{mentee.goals.length}</div>
                          <div className="text-xs text-purple-200">{t('Goals')}</div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <button
                          onClick={() => navigate(`/mentor/mentees/${mentee.id}`)}
                          className="flex-1 px-3 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-sm transition-colors"
                        >
                          {t('View Profile')}
                        </button>
                        <button
                          className="px-3 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg text-sm transition-colors"
                          title={t('Schedule Session')}
                        >
                          <Calendar className="w-4 h-4" />
                        </button>
                        <button
                          className="px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm transition-colors"
                          title={t('Send Message')}
                        >
                          <MessageSquare className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {filteredMentees.length === 0 && (
                  <div className="text-center py-12">
                    <Users className="w-16 h-16 text-purple-400 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-white mb-2">{t('No mentees found')}</h3>
                    <p className="text-purple-200">{t('Start mentoring entrepreneurs to see them here')}</p>
                  </div>
                )}
              </div>
            )}

            {selectedTab === 'sessions' && (
              <div>
                <div className="space-y-4">
                  {upcomingSessions.map((session) => (
                    <div key={session.id} className="bg-indigo-900/20 rounded-lg p-6 border border-indigo-800/30">
                      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <div className="w-10 h-10 bg-purple-600/30 rounded-lg flex items-center justify-center">
                            {getSessionTypeIcon(session.type)}
                          </div>
                          <div>
                            <h3 className="font-semibold text-white">{session.topic}</h3>
                            <p className="text-sm text-purple-200">{t('with')} {session.menteeName}</p>
                            <p className="text-xs text-purple-300">
                              {new Date(session.scheduledAt).toLocaleDateString()} • {session.duration} {t('minutes')}
                            </p>
                          </div>
                        </div>
                        
                        <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <button className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm transition-colors">
                            {t('Join Session')}
                          </button>
                          <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm transition-colors">
                            {t('Reschedule')}
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {upcomingSessions.length === 0 && (
                  <div className="text-center py-12">
                    <Calendar className="w-16 h-16 text-purple-400 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-white mb-2">{t('No upcoming sessions')}</h3>
                    <p className="text-purple-200">{t('Schedule sessions with your mentees to see them here')}</p>
                  </div>
                )}
              </div>
            )}

            {selectedTab === 'analytics' && (
              <div className="text-center py-12">
                <BarChart3 className="w-16 h-16 text-purple-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">{t('Analytics Coming Soon')}</h3>
                <p className="text-purple-200">{t('Detailed mentorship analytics and insights will be available here')}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MentorshipPage;
