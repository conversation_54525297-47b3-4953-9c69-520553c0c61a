import { useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import { type CommunityPost, type CommunityStats } from '../services/communityApi';

// Import focused hooks
import { useCommunityPosts } from './useCommunityPosts';
import { useCommunityStats } from './useCommunityStats';
import { useCommunityModals } from './useCommunityModals';

/**
 * Refactored Community Hook - Orchestrator Pattern
 *
 * This hook now follows the Single Responsibility Principle by orchestrating
 * multiple focused hooks instead of containing all logic itself.
 *
 * Focused hooks used:
 * - useCommunityPosts: Posts data and operations
 * - useCommunityStats: Stats and connection status
 * - useCommunityModals: Modal state management
 */
export interface UseCommunityReturn {
  // Posts data (from useCommunityPosts)
  posts: CommunityPost[];
  searchResults: CommunityPost[];
  displayPosts: CommunityPost[];
  activeView: string;
  searchQuery: string;

  // Stats data (from useCommunityStats)
  stats: CommunityStats | null;
  connectionStatus: 'connected' | 'connecting' | 'disconnected';

  // Loading states
  isLoading: boolean;
  isSearching: boolean;
  isStatsLoading: boolean;

  // Computed stats
  computedStats: {
    trending_posts_count: number;
    verified_authors_count: number;
    saved_posts_count: number;
  };

  // Modal states (from useCommunityModals)
  showCreateModal: boolean;
  showAdvancedSearch: boolean;
  showUserDiscovery: boolean;
  showPostAnalytics: string | null;

  // Actions
  handleViewChange: (view: string) => void;
  handleSearchChange: (query: string) => void;
  handleClearSearch: () => void;
  refreshPosts: () => Promise<void>;
  refreshStats: () => Promise<void>;
  checkConnection: () => Promise<void>;

  // Post operations (from useCommunityPosts)
  handleLikePost: (postId: string) => Promise<void>;
  handleCreatePost: (postData: any) => Promise<void>;
  handleCommentPost: (postId: string, content: string) => Promise<void>;
  handleSharePost: (postId: string) => Promise<void>;
  handleSavePost: (postId: string) => Promise<void>;

  // Modal actions (from useCommunityModals)
  openCreateModal: () => void;
  closeCreateModal: () => void;
  openAdvancedSearch: () => void;
  closeAdvancedSearch: () => void;
  openUserDiscovery: () => void;
  closeUserDiscovery: () => void;
  openPostAnalytics: (postId: string) => void;
  closePostAnalytics: () => void;
  handleCreatePostClick: () => void;
  handleAdvancedSearchResults: (results: any[]) => void;
}

/**
 * Main Community Hook - Orchestrator Pattern
 *
 * This refactored hook now orchestrates multiple focused hooks instead of
 * containing all logic itself, following the Single Responsibility Principle.
 */
export const useCommunity = (
  onSearchResults?: (results: any[]) => void,
  onSetSearchQuery?: (query: string) => void
): UseCommunityReturn => {

  // Redux state
  const activeView = useSelector((state: RootState) => state.forum.activeView);
  const searchQuery = useSelector((state: RootState) => state.forum.searchQuery);

  // Use focused hooks for specific responsibilities
  const postsHook = useCommunityPosts();
  const statsHook = useCommunityStats();
  const modalsHook = useCommunityModals(onSearchResults, onSetSearchQuery);

  // Progressive loading: Load posts first, then stats in background
  useEffect(() => {
    // Phase 1: Load critical content (posts) immediately
    postsHook.loadPosts();
  }, [postsHook.loadPosts]);

  // Phase 2: Load secondary content (stats) after posts are loaded
  useEffect(() => {
    // Only load stats after posts are loaded or if posts failed to load
    if (postsHook.posts.length > 0 || !postsHook.isLoading) {
      // Small delay to ensure posts are rendered first
      const timer = setTimeout(() => {
        statsHook.loadStats();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [statsHook.loadStats, postsHook.posts.length, postsHook.isLoading]);

  // Computed values - delegate to focused hooks
  const displayPosts = useMemo(() => {
    return searchQuery.trim() ? postsHook.searchResults : postsHook.posts;
  }, [searchQuery, postsHook.searchResults, postsHook.posts]);

  const computedStats = useMemo(() => {
    const postsArray = Array.isArray(postsHook.posts) ? postsHook.posts : [];
    return {
      trending_posts_count: postsArray.filter(p => p.like_count > 10).length,
      verified_authors_count: postsArray.filter(p => p.author?.is_verified).length,
      saved_posts_count: postsArray.filter(p => p.is_saved).length,
    };
  }, [postsHook.posts]);

  // Simple orchestrator - delegate complex logic to focused hooks
  // Search functionality is now handled by useCommunityPosts hook

  /**
   * Return orchestrated interface combining all focused hooks
   * This follows the Facade pattern, providing a unified interface
   * while delegating actual work to specialized hooks.
   */

  return {
    // Posts data - delegated to useCommunityPosts
    posts: postsHook.posts,
    searchResults: postsHook.searchResults,
    displayPosts,
    activeView,
    searchQuery,

    // Stats data - delegated to useCommunityStats
    stats: statsHook.stats,
    connectionStatus: statsHook.connectionStatus,

    // Loading states
    isLoading: postsHook.isLoading,
    isSearching: postsHook.isSearching,
    isStatsLoading: statsHook.isLoading,

    // Computed stats
    computedStats,

    // Modal states - delegated to useCommunityModals
    showCreateModal: modalsHook.showCreateModal,
    showAdvancedSearch: modalsHook.showAdvancedSearch,
    showUserDiscovery: modalsHook.showUserDiscovery,
    showPostAnalytics: modalsHook.showPostAnalytics,

    // Actions - delegated to focused hooks
    handleViewChange: postsHook.handleViewChange,
    handleSearchChange: postsHook.handleSearchChange,
    handleClearSearch: postsHook.handleClearSearch,
    refreshPosts: postsHook.refreshPosts,
    refreshStats: statsHook.refreshStats,
    checkConnection: statsHook.checkConnection,

    // Post operations - delegated to useCommunityPosts
    handleLikePost: postsHook.handleLikePost,
    handleCreatePost: postsHook.handleCreatePost,
    handleCommentPost: postsHook.handleCommentPost,
    handleSharePost: postsHook.handleSharePost,
    handleSavePost: postsHook.handleSavePost,

    // Modal actions - delegated to useCommunityModals
    openCreateModal: modalsHook.openCreateModal,
    closeCreateModal: modalsHook.closeCreateModal,
    openAdvancedSearch: modalsHook.openAdvancedSearch,
    closeAdvancedSearch: modalsHook.closeAdvancedSearch,
    openUserDiscovery: modalsHook.openUserDiscovery,
    closeUserDiscovery: modalsHook.closeUserDiscovery,
    openPostAnalytics: modalsHook.openPostAnalytics,
    closePostAnalytics: modalsHook.closePostAnalytics,
    handleCreatePostClick: modalsHook.handleCreatePostClick,
    handleAdvancedSearchResults: modalsHook.handleAdvancedSearchResults,
  };
};

    try {
      await communityApi.likePost(postId);
      setPosts(prev => prev.map(post =>
        post.id === postId
          ? {
              ...post,
              is_liked: !post.is_liked,
              like_count: post.is_liked ? post.like_count - 1 : post.like_count + 1
            }
          : post
      ));
    } catch (error) {
      const communityError = CommunityErrorHandler.parseError(error, 'like_post');
      console.error('Like error:', communityError);
      const errorMessage = CommunityErrorHandler.getLocalizedMessage(communityError, t, isRTL);
      showError(errorMessage);

      // If it's a retryable error, suggest retry
      if (CommunityErrorHandler.shouldRetry(communityError)) {
        const retryDelay = CommunityErrorHandler.getRetryDelay(communityError);
        setTimeout(() => {
          showInfo(t('community.messages.retryAvailable', 'You can try again now.'));
        }, retryDelay);
      }
    }
  }, [isAuthenticated, user, checkRateLimit, showInfo, showError, t, isRTL]);

  const handleCreatePost = useCallback(async (postData: any) => {
    if (!isAuthenticated || !user) {
      showInfo(t('community.messages.loginToCreate'));
      return;
    }

    const rateLimitResult = checkRateLimit(user.id.toString(), 'POST_CREATE');
    if (!rateLimitResult.allowed) {
      const message = getRateLimitMessage('POST_CREATE', rateLimitResult.retryAfter || 0, isRTL);
      showError(message);
      return;
    }

    const sanitizedData = {
      ...postData,
      content: sanitizePostContent(postData.content || ''),
      title: postData.title ? sanitizePostContent(postData.title) : undefined
    };

    if (!sanitizedData.content.trim()) {
      showError(t('community.messages.emptyPostContent'));
      return;
    }

    try {
      const newPost = await communityApi.createPost(sanitizedData);
      setPosts(prev => [newPost, ...prev]);
      showSuccess(t('community.messages.postCreated'));
      setShowCreateModal(false);
    } catch (error) {
      const communityError = CommunityErrorHandler.parseError(error, 'create_post');
      console.error('Create post error:', communityError);
      const errorMessage = CommunityErrorHandler.getLocalizedMessage(communityError, t, isRTL);
      showError(errorMessage);

      // For validation errors, don't close the modal so user can fix the issue
      if (communityError.type === 'CONTENT_VALIDATION' ||
          communityError.type === 'CONTENT_TOO_LONG' ||
          communityError.type === 'INVALID_MEDIA' ||
          communityError.type === 'SPAM_DETECTED') {
        // Keep modal open for user to fix the issue
        return;
      }

      // If it's a retryable error, suggest retry
      if (CommunityErrorHandler.shouldRetry(communityError)) {
        const retryDelay = CommunityErrorHandler.getRetryDelay(communityError);
        setTimeout(() => {
          showInfo(t('community.messages.retryAvailable', 'You can try again now.'));
        }, retryDelay);
      }
    }
  }, [isAuthenticated, user, checkRateLimit, showInfo, showError, showSuccess, t, isRTL]);

  const handleCommentPost = useCallback(async (postId: string, content: string) => {
    if (!isAuthenticated || !user) {
      showInfo(t('community.messages.loginToComment'));
      return;
    }

    const rateLimitResult = checkRateLimit(user.id.toString(), 'COMMENT_CREATE');
    if (!rateLimitResult.allowed) {
      const message = getRateLimitMessage('COMMENT_CREATE', rateLimitResult.retryAfter || 0, isRTL);
      showError(message);
      return;
    }

    const sanitizedContent = sanitizeCommentContent(content);
    if (!sanitizedContent.trim()) {
      showError(t('community.messages.emptyCommentContent'));
      return;
    }

    try {
      await communityApi.commentPost(postId, sanitizedContent);
      setPosts(prev => prev.map(post =>
        post.id === postId
          ? { ...post, comment_count: post.comment_count + 1 }
          : post
      ));
      showSuccess(t('community.messages.commentAdded'));
    } catch (error) {
      console.error('Comment error:', error);
      showError(t('community.messages.commentError'));
    }
  }, [isAuthenticated, user, checkRateLimit, showInfo, showError, showSuccess, t, isRTL]);

  const handleSharePost = useCallback(async (postId: string) => {
    if (!isAuthenticated || !user) {
      showInfo(t('community.messages.loginRequired'));
      return;
    }

    const rateLimitResult = checkRateLimit(user.id.toString(), 'SHARE_ACTION');
    if (!rateLimitResult.allowed) {
      const message = getRateLimitMessage('SHARE_ACTION', rateLimitResult.retryAfter || 0, isRTL);
      showError(message);
      return;
    }

    try {
      await communityApi.sharePost(postId);
      setPosts(prev => prev.map(post =>
        post.id === postId
          ? { ...post, share_count: post.share_count + 1 }
          : post
      ));
      showSuccess(t('community.messages.postShared'));
    } catch (error) {
      console.error('Share error:', error);
      showError(t('community.messages.shareError'));
    }
  }, [isAuthenticated, user, checkRateLimit, showInfo, showError, showSuccess, t, isRTL]);

  const handleSavePost = useCallback(async (postId: string) => {
    if (!isAuthenticated || !user) {
      showInfo(t('community.messages.loginRequired'));
      return;
    }

    const rateLimitResult = checkRateLimit(user.id.toString(), 'SAVE_ACTION');
    if (!rateLimitResult.allowed) {
      const message = getRateLimitMessage('SAVE_ACTION', rateLimitResult.retryAfter || 0, isRTL);
      showError(message);
      return;
    }

    try {
      await communityApi.savePost(postId);
      setPosts(prev => prev.map(post =>
        post.id === postId
          ? { ...post, is_saved: !post.is_saved }
          : post
      ));
      showSuccess(t('community.messages.postSaved'));
    } catch (error) {
      console.error('Save error:', error);
      showError(t('community.messages.saveError'));
    }
  }, [isAuthenticated, user, checkRateLimit, showInfo, showError, showSuccess, t, isRTL]);

  // Modal actions
  const openCreateModal = useCallback(() => {
    setShowCreateModal(true);
  }, []);

  const closeCreateModal = useCallback(() => {
    setShowCreateModal(false);
  }, []);

  const openAdvancedSearch = useCallback(() => {
    setShowAdvancedSearch(true);
  }, []);

  const closeAdvancedSearch = useCallback(() => {
    setShowAdvancedSearch(false);
  }, []);

  const openUserDiscovery = useCallback(() => {
    setShowUserDiscovery(true);
  }, []);

  const closeUserDiscovery = useCallback(() => {
    setShowUserDiscovery(false);
  }, []);

  const openPostAnalytics = useCallback((postId: string) => {
    setShowPostAnalytics(postId);
  }, []);

  const closePostAnalytics = useCallback(() => {
    setShowPostAnalytics(null);
  }, []);

  const handleCreatePostClick = useCallback(() => {
    if (!isAuthenticated) {
      showInfo(t('community.messages.loginToCreate'));
      return;
    }
    openCreateModal();
  }, [isAuthenticated, showInfo, t, openCreateModal]);

  const handleAdvancedSearchResults = useCallback((results: any[]) => {
    if (onSearchResults) {
      onSearchResults(results);
    }
    if (onSetSearchQuery) {
      onSetSearchQuery('advanced search results');
    }
    closeAdvancedSearch();
  }, [onSearchResults, onSetSearchQuery, closeAdvancedSearch]);

  // Progressive loading - Load posts first (critical), then stats (secondary)
  useEffect(() => {
    // Phase 1: Load critical content (posts) immediately
    loadPosts();
  }, [loadPosts]);

  // Phase 2: Load secondary content (stats) after posts are loaded
  useEffect(() => {
    // Only load stats after posts are loaded or if posts failed to load
    if (posts.length > 0 || !isLoading) {
      // Small delay to ensure posts are rendered first
      const timer = setTimeout(() => {
        loadStats();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [loadStats, posts.length, isLoading]);

  // Intelligent polling system with adaptive intervals
  useEffect(() => {
    let statsInterval: NodeJS.Timeout;
    let connectionInterval: NodeJS.Timeout;

    // Stats refresh - adaptive interval based on user activity
    const setupStatsPolling = () => {
      // Check if user is active (page is visible and user has interacted recently)
      const isUserActive = !document.hidden;
      const statsRefreshInterval = isUserActive ? 300000 : 600000; // 5 min active, 10 min inactive

      statsInterval = setInterval(() => {
        if (connectionStatus === 'connected' && !document.hidden) {
          refreshStats();
        }
      }, statsRefreshInterval);
    };

    // Connection health check - exponential backoff for failed connections
    const setupConnectionPolling = () => {
      let retryCount = 0;
      const maxRetries = 5;

      const scheduleConnectionCheck = () => {
        if (connectionStatus === 'disconnected' && retryCount < maxRetries) {
          // Exponential backoff: 30s, 60s, 120s, 240s, 480s
          const delay = Math.min(30000 * Math.pow(2, retryCount), 480000);

          connectionInterval = setTimeout(async () => {
            try {
              await checkConnection();
              retryCount = 0; // Reset on successful connection
            } catch (error) {
              retryCount++;
              scheduleConnectionCheck(); // Schedule next retry
            }
          }, delay);
        } else if (connectionStatus === 'connected') {
          retryCount = 0; // Reset retry count when connected
        }
      };

      scheduleConnectionCheck();
    };

    setupStatsPolling();
    setupConnectionPolling();

    // Listen for visibility changes to optimize polling
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Page is hidden, clear intervals to save resources
        clearInterval(statsInterval);
      } else {
        // Page is visible, restart polling
        setupStatsPolling();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      clearInterval(statsInterval);
      clearTimeout(connectionInterval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [connectionStatus, refreshStats, checkConnection]);

  return {
    // Posts data
    posts,
    searchResults,
    displayPosts,
    activeView,
    searchQuery,

    // Stats data
    stats,
    connectionStatus,

    // Loading states
    isLoading,
    isSearching,
    isStatsLoading,

    // Computed stats
    computedStats,

    // Modal states
    showCreateModal,
    showAdvancedSearch,
    showUserDiscovery,
    showPostAnalytics,

    // Actions
    handleViewChange,
    handleSearchChange,
    handleClearSearch,
    refreshPosts,
    refreshStats,
    checkConnection,

    // Post operations
    handleLikePost,
    handleCreatePost,
    handleCommentPost,
    handleSharePost,
    handleSavePost,

    // Modal actions
    openCreateModal,
    closeCreateModal,
    openAdvancedSearch,
    closeAdvancedSearch,
    openUserDiscovery,
    closeUserDiscovery,
    openPostAnalytics,
    closePostAnalytics,
    handleCreatePostClick,
    handleAdvancedSearchResults,
  };
};
