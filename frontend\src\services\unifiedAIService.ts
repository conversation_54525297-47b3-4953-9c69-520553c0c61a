/**
 * 🎯 UNIFIED AI SERVICE
 * Centralized AI service with enhanced error handling, rate limiting, and consistent API patterns
 */

import { apiRequest } from './api';
import { getMaxAIRequests } from '../config/roleConfig';

// ========================================
// TYPES & INTERFACES
// ========================================

export interface AIRequest {
  id: string;
  prompt: string;
  context?: Record<string, any>;
  type: 'chat' | 'business_plan' | 'analysis' | 'recommendation';
  priority: 'low' | 'normal' | 'high';
  userId: string;
  timestamp: string;
}

export interface AIResponse {
  id: string;
  requestId: string;
  content: string;
  confidence: number;
  processingTime: number;
  tokens: {
    input: number;
    output: number;
    total: number;
  };
  metadata: {
    model: string;
    version: string;
    temperature: number;
    maxTokens: number;
  };
  timestamp: string;
}

export interface AIError {
  code: string;
  message: string;
  details?: Record<string, any>;
  retryable: boolean;
  retryAfter?: number;
}

export interface RateLimitInfo {
  remaining: number;
  resetTime: number;
  limit: number;
  windowSize: number;
}

// ========================================
// RATE LIMITING
// ========================================

class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  
  canMakeRequest(userId: string, userRole: string): { allowed: boolean; info: RateLimitInfo } {
    const limit = getMaxAIRequests(userRole);
    if (limit === -1) {
      // Unlimited for super admin
      return {
        allowed: true,
        info: { remaining: -1, resetTime: 0, limit: -1, windowSize: 3600 }
      };
    }

    const now = Date.now();
    const windowSize = 3600 * 1000; // 1 hour window
    const userRequests = this.requests.get(userId) || [];
    
    // Remove old requests outside the window
    const validRequests = userRequests.filter(timestamp => now - timestamp < windowSize);
    this.requests.set(userId, validRequests);

    const remaining = Math.max(0, limit - validRequests.length);
    const allowed = validRequests.length < limit;

    if (allowed) {
      validRequests.push(now);
      this.requests.set(userId, validRequests);
    }

    return {
      allowed,
      info: {
        remaining,
        resetTime: validRequests.length > 0 ? validRequests[0] + windowSize : now + windowSize,
        limit,
        windowSize: windowSize / 1000
      }
    };
  }

  getRemainingRequests(userId: string, userRole: string): number {
    const { info } = this.canMakeRequest(userId, userRole);
    return info.remaining;
  }
}

// ========================================
// UNIFIED AI SERVICE CLASS
// ========================================

class UnifiedAIService {
  private rateLimiter = new RateLimiter();
  private requestQueue: Map<string, AIRequest[]> = new Map();
  private activeRequests = new Set<string>();

  // ========================================
  // CORE AI METHODS
  // ========================================

  async sendChatMessage(
    message: string,
    context?: Record<string, any>,
    userId?: string,
    userRole?: string
  ): Promise<AIResponse> {
    return this.makeAIRequest({
      prompt: message,
      context,
      type: 'chat',
      priority: 'normal'
    }, userId, userRole);
  }

  async generateBusinessPlan(
    businessIdea: string,
    requirements: Record<string, any>,
    userId?: string,
    userRole?: string
  ): Promise<AIResponse> {
    return this.makeAIRequest({
      prompt: `Generate a comprehensive business plan for: ${businessIdea}`,
      context: { businessIdea, requirements, type: 'business_plan_generation' },
      type: 'business_plan',
      priority: 'high'
    }, userId, userRole);
  }

  async analyzeBusinessIdea(
    businessIdea: string,
    userId?: string,
    userRole?: string
  ): Promise<AIResponse> {
    return this.makeAIRequest({
      prompt: `Analyze this business idea: ${businessIdea}`,
      context: { businessIdea, type: 'idea_analysis' },
      type: 'analysis',
      priority: 'normal'
    }, userId, userRole);
  }

  async getRecommendations(
    type: 'business' | 'mentor' | 'investor' | 'template',
    context: Record<string, any>,
    userId?: string,
    userRole?: string
  ): Promise<AIResponse> {
    return this.makeAIRequest({
      prompt: `Provide ${type} recommendations based on the given context`,
      context: { ...context, recommendationType: type },
      type: 'recommendation',
      priority: 'normal'
    }, userId, userRole);
  }

  // ========================================
  // PRIVATE METHODS
  // ========================================

  private async makeAIRequest(
    requestData: Omit<AIRequest, 'id' | 'userId' | 'timestamp'>,
    userId?: string,
    userRole?: string
  ): Promise<AIResponse> {
    // Generate request ID
    const requestId = this.generateRequestId();
    
    // Get user info from auth if not provided
    if (!userId || !userRole) {
      const authData = await this.getCurrentUser();
      userId = userId || authData.userId;
      userRole = userRole || authData.userRole;
    }

    // Check rate limits
    const rateLimitCheck = this.rateLimiter.canMakeRequest(userId!, userRole!);
    if (!rateLimitCheck.allowed) {
      throw this.createAIError(
        'RATE_LIMIT_EXCEEDED',
        `Rate limit exceeded. ${rateLimitCheck.info.remaining} requests remaining.`,
        { rateLimitInfo: rateLimitCheck.info },
        false,
        rateLimitCheck.info.resetTime
      );
    }

    // Create full request object
    const request: AIRequest = {
      ...requestData,
      id: requestId,
      userId: userId!,
      timestamp: new Date().toISOString()
    };

    try {
      // Add to active requests
      this.activeRequests.add(requestId);

      // Make API call with retry logic
      const response = await this.executeAIRequest(request);
      
      return response;
    } catch (error) {
      // Enhanced error handling
      throw this.handleAIError(error, request);
    } finally {
      // Remove from active requests
      this.activeRequests.delete(requestId);
    }
  }

  private async executeAIRequest(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();

    try {
      // Call the actual AI API
      const response = await apiRequest<{
        content: string;
        confidence: number;
        tokens: { input: number; output: number; total: number };
        metadata: { model: string; version: string; temperature: number; maxTokens: number };
      }>('/ai/chat/', 'POST', {
        message: request.prompt,
        context: request.context,
        type: request.type,
        priority: request.priority,
        requestId: request.id
      });

      const processingTime = Date.now() - startTime;

      return {
        id: this.generateRequestId(),
        requestId: request.id,
        content: response.content,
        confidence: response.confidence,
        processingTime,
        tokens: response.tokens,
        metadata: response.metadata,
        timestamp: new Date().toISOString()
      };

    } catch (error: any) {
      // Handle specific API errors
      if (error.status === 429) {
        throw this.createAIError(
          'RATE_LIMIT_EXCEEDED',
          'AI service rate limit exceeded',
          { originalError: error },
          true,
          Date.now() + (error.retryAfter || 60) * 1000
        );
      }

      if (error.status === 503) {
        throw this.createAIError(
          'SERVICE_UNAVAILABLE',
          'AI service is temporarily unavailable',
          { originalError: error },
          true,
          Date.now() + 30000 // Retry after 30 seconds
        );
      }

      if (error.status === 400) {
        throw this.createAIError(
          'INVALID_REQUEST',
          'Invalid AI request parameters',
          { originalError: error, request },
          false
        );
      }

      // Generic error
      throw this.createAIError(
        'AI_REQUEST_FAILED',
        'AI request failed unexpectedly',
        { originalError: error, request },
        true
      );
    }
  }

  private async getCurrentUser(): Promise<{ userId: string; userRole: string }> {
    try {
      // This would typically come from your auth store/context
      const authData = await apiRequest<{ user: { id: string; role: string } }>('/auth/me/');
      return {
        userId: authData.user.id,
        userRole: authData.user.role
      };
    } catch (error) {
      throw this.createAIError(
        'AUTH_REQUIRED',
        'Authentication required for AI requests',
        { originalError: error },
        false
      );
    }
  }

  private generateRequestId(): string {
    return `ai_req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private createAIError(
    code: string,
    message: string,
    details?: Record<string, any>,
    retryable: boolean = false,
    retryAfter?: number
  ): AIError {
    return {
      code,
      message,
      details,
      retryable,
      retryAfter
    };
  }

  private handleAIError(error: any, request: AIRequest): AIError {
    console.error('AI Request Error:', {
      requestId: request.id,
      error,
      request: {
        type: request.type,
        priority: request.priority,
        userId: request.userId
      }
    });

    if (error.code) {
      // Already an AIError
      return error;
    }

    // Convert generic error to AIError
    return this.createAIError(
      'UNKNOWN_ERROR',
      error.message || 'An unknown error occurred',
      { originalError: error, request },
      true
    );
  }

  // ========================================
  // PUBLIC UTILITY METHODS
  // ========================================

  getRateLimitInfo(userId: string, userRole: string): RateLimitInfo {
    const { info } = this.rateLimiter.canMakeRequest(userId, userRole);
    return info;
  }

  getActiveRequestsCount(): number {
    return this.activeRequests.size;
  }

  isRequestActive(requestId: string): boolean {
    return this.activeRequests.has(requestId);
  }

  // Health check method
  async healthCheck(): Promise<{ status: 'healthy' | 'degraded' | 'unhealthy'; details: Record<string, any> }> {
    try {
      const startTime = Date.now();
      await apiRequest('/ai/health/', 'GET');
      const responseTime = Date.now() - startTime;

      return {
        status: responseTime < 1000 ? 'healthy' : 'degraded',
        details: {
          responseTime,
          activeRequests: this.activeRequests.size,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          activeRequests: this.activeRequests.size,
          timestamp: new Date().toISOString()
        }
      };
    }
  }
}

// ========================================
// SINGLETON INSTANCE
// ========================================

export const unifiedAIService = new UnifiedAIService();

// ========================================
// CONVENIENCE FUNCTIONS
// ========================================

export async function sendAIMessage(message: string, context?: Record<string, any>): Promise<AIResponse> {
  return unifiedAIService.sendChatMessage(message, context);
}

export async function generateBusinessPlan(businessIdea: string, requirements: Record<string, any>): Promise<AIResponse> {
  return unifiedAIService.generateBusinessPlan(businessIdea, requirements);
}

export async function analyzeBusinessIdea(businessIdea: string): Promise<AIResponse> {
  return unifiedAIService.analyzeBusinessIdea(businessIdea);
}

export async function getAIRecommendations(
  type: 'business' | 'mentor' | 'investor' | 'template',
  context: Record<string, any>
): Promise<AIResponse> {
  return unifiedAIService.getRecommendations(type, context);
}

export default unifiedAIService;
