"""
Management command to show cache statistics and health
"""

from django.core.management.base import BaseCommand
from django.core.cache import cache, caches
from django.conf import settings
from community.cache import CommunityCache
import json


class Command(BaseCommand):
    help = 'Show cache statistics and health information'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear all community cache entries',
        )
        parser.add_argument(
            '--test',
            action='store_true',
            help='Test cache functionality',
        )
        parser.add_argument(
            '--keys',
            action='store_true',
            help='Show sample cache keys',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.clear_cache()
        elif options['test']:
            self.test_cache()
        elif options['keys']:
            self.show_cache_keys()
        else:
            self.show_cache_stats()

    def show_cache_stats(self):
        """Show cache configuration and statistics"""
        self.stdout.write(self.style.SUCCESS('=== Cache Configuration ==='))
        
        # Show cache backends
        for cache_name, cache_config in settings.CACHES.items():
            self.stdout.write(f"\n{cache_name.upper()} Cache:")
            self.stdout.write(f"  Backend: {cache_config['BACKEND']}")
            if 'LOCATION' in cache_config:
                self.stdout.write(f"  Location: {cache_config['LOCATION']}")
            self.stdout.write(f"  Timeout: {cache_config.get('TIMEOUT', 'Default')}s")
        
        # Show cache timeouts
        self.stdout.write(f"\n=== Cache Timeouts ===")
        cache_timeouts = getattr(settings, 'CACHE_TIMEOUTS', {})
        for key, timeout in cache_timeouts.items():
            self.stdout.write(f"  {key}: {timeout}s")
        
        # Test cache connectivity
        self.stdout.write(f"\n=== Cache Health ===")
        try:
            # Test default cache
            cache.set('health_check', 'ok', 10)
            if cache.get('health_check') == 'ok':
                self.stdout.write(self.style.SUCCESS("✓ Default cache: OK"))
                cache.delete('health_check')
            else:
                self.stdout.write(self.style.ERROR("✗ Default cache: FAILED"))
            
            # Test community cache
            if 'community' in settings.CACHES:
                community_cache = caches['community']
                community_cache.set('health_check', 'ok', 10)
                if community_cache.get('health_check') == 'ok':
                    self.stdout.write(self.style.SUCCESS("✓ Community cache: OK"))
                    community_cache.delete('health_check')
                else:
                    self.stdout.write(self.style.ERROR("✗ Community cache: FAILED"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Cache health check failed: {e}"))

    def clear_cache(self):
        """Clear all community cache entries"""
        self.stdout.write('Clearing community cache...')
        
        try:
            # Clear community-specific patterns
            patterns = [
                f"{CommunityCache.POSTS_PREFIX}:*",
                f"{CommunityCache.STATS_PREFIX}:*",
                f"{CommunityCache.HASHTAGS_PREFIX}:*",
                f"{CommunityCache.USER_PREFIX}:*",
                f"{CommunityCache.FEED_PREFIX}:*",
                f"{CommunityCache.RECOMMENDATIONS_PREFIX}:*",
            ]
            
            cleared_count = 0
            for pattern in patterns:
                if CommunityCache.delete_pattern(pattern):
                    cleared_count += 1
            
            self.stdout.write(
                self.style.SUCCESS(f'✓ Cleared {cleared_count} cache pattern(s)')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'✗ Failed to clear cache: {e}')
            )

    def test_cache(self):
        """Test cache functionality"""
        self.stdout.write('Testing cache functionality...')
        
        test_key = 'test_cache_functionality'
        test_value = {'test': True, 'timestamp': 'now'}
        
        try:
            # Test set
            CommunityCache.set_with_timeout(test_key, test_value, 'community_posts')
            self.stdout.write("✓ Cache set: OK")
            
            # Test get
            retrieved_value = CommunityCache.get(test_key)
            if retrieved_value == test_value:
                self.stdout.write("✓ Cache get: OK")
            else:
                self.stdout.write(self.style.ERROR("✗ Cache get: FAILED"))
            
            # Test delete
            if CommunityCache.delete(test_key):
                self.stdout.write("✓ Cache delete: OK")
            else:
                self.stdout.write(self.style.ERROR("✗ Cache delete: FAILED"))
            
            # Verify deletion
            if CommunityCache.get(test_key) is None:
                self.stdout.write("✓ Cache deletion verified: OK")
            else:
                self.stdout.write(self.style.ERROR("✗ Cache deletion verification: FAILED"))
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'✗ Cache test failed: {e}')
            )

    def show_cache_keys(self):
        """Show sample cache keys that would be generated"""
        self.stdout.write('=== Sample Cache Keys ===')
        
        # Sample keys
        sample_keys = [
            CommunityCache.get_posts_cache_key(user_id=1, visibility='public'),
            CommunityCache.get_stats_cache_key(),
            CommunityCache.get_hashtags_cache_key(),
            CommunityCache.get_user_cache_key(user_id=1, data_type='profile'),
            CommunityCache.get_feed_cache_key(user_id=1),
            CommunityCache.get_recommendations_cache_key(role='developer', activity_level='high'),
        ]
        
        for key in sample_keys:
            self.stdout.write(f"  {key}")
