/**
 * Accessibility Hook
 * Custom hook for managing accessibility features in React components
 */

import { useEffect, useRef, useCallback } from 'react';
import { 
  FocusTrap, 
  announceToScreenReader, 
  manageFocusForDynamicContent,
  setupArrowKeyNavigation 
} from '../utils/focusManagement';

export const useAccessibility = () => {
  // Announce messages to screen readers
  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    announceToScreenReader(message, priority);
  }, []);

  // Manage focus for dynamic content
  const manageFocus = useCallback((
    container: HTMLElement | null,
    announcement?: string
  ) => {
    if (container) {
      manageFocusForDynamicContent(container, announcement);
    }
  }, []);

  return {
    announce,
    manageFocus
  };
};

// Hook for focus trap in modals
export const useFocusTrap = (isActive: boolean) => {
  const containerRef = useRef<HTMLElement>(null);
  const focusTrapRef = useRef<FocusTrap | null>(null);

  useEffect(() => {
    if (isActive && containerRef.current) {
      focusTrapRef.current = new FocusTrap(containerRef.current);
      focusTrapRef.current.activate();
    }

    return () => {
      if (focusTrapRef.current) {
        focusTrapRef.current.deactivate();
        focusTrapRef.current = null;
      }
    };
  }, [isActive]);

  return containerRef;
};

// Hook for arrow key navigation
export const useArrowKeyNavigation = (
  itemSelector: string,
  options?: {
    wrap?: boolean;
    orientation?: 'horizontal' | 'vertical' | 'both';
  }
) => {
  const containerRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (containerRef.current) {
      setupArrowKeyNavigation(containerRef.current, itemSelector, options);
    }
  }, [itemSelector, options]);

  return containerRef;
};

// Hook for managing ARIA live regions
export const useAriaLiveRegion = () => {
  const liveRegionRef = useRef<HTMLDivElement>(null);

  const updateLiveRegion = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (liveRegionRef.current) {
      liveRegionRef.current.setAttribute('aria-live', priority);
      liveRegionRef.current.textContent = message;
    }
  }, []);

  const clearLiveRegion = useCallback(() => {
    if (liveRegionRef.current) {
      liveRegionRef.current.textContent = '';
    }
  }, []);

  return {
    liveRegionRef,
    updateLiveRegion,
    clearLiveRegion
  };
};

// Hook for keyboard shortcuts
export const useKeyboardShortcuts = (shortcuts: Record<string, () => void>) => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Create key combination string
      const keys = [];
      if (event.ctrlKey) keys.push('ctrl');
      if (event.altKey) keys.push('alt');
      if (event.shiftKey) keys.push('shift');
      if (event.metaKey) keys.push('meta');
      keys.push(event.key.toLowerCase());
      
      const combination = keys.join('+');
      
      if (shortcuts[combination]) {
        event.preventDefault();
        shortcuts[combination]();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [shortcuts]);
};

// Hook for reduced motion preferences
export const useReducedMotion = () => {
  const prefersReducedMotion = useRef(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    prefersReducedMotion.current = mediaQuery.matches;

    const handleChange = (event: MediaQueryListEvent) => {
      prefersReducedMotion.current = event.matches;
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion.current;
};

// Hook for high contrast mode detection
export const useHighContrast = () => {
  const prefersHighContrast = useRef(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    prefersHighContrast.current = mediaQuery.matches;

    const handleChange = (event: MediaQueryListEvent) => {
      prefersHighContrast.current = event.matches;
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersHighContrast.current;
};

// Hook for managing page title and meta description for screen readers
export const usePageAnnouncement = (title: string, description?: string) => {
  useEffect(() => {
    // Update document title
    const originalTitle = document.title;
    document.title = title;

    // Update meta description
    let metaDescription = document.querySelector('meta[name="description"]') as HTMLMetaElement;
    const originalDescription = metaDescription?.content;
    
    if (description) {
      if (!metaDescription) {
        metaDescription = document.createElement('meta');
        metaDescription.name = 'description';
        document.head.appendChild(metaDescription);
      }
      metaDescription.content = description;
    }

    // Announce page change
    announceToScreenReader(`Page loaded: ${title}`, 'assertive');

    return () => {
      document.title = originalTitle;
      if (originalDescription && metaDescription) {
        metaDescription.content = originalDescription;
      }
    };
  }, [title, description]);
};
