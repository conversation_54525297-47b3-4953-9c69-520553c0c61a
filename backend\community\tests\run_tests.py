#!/usr/bin/env python
"""
Community Tests Runner
Comprehensive test runner for community app with coverage reporting
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(backend_dir))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')

import django
django.setup()


class CommunityTestRunner:
    """Test runner for community app with various options"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.backend_dir = self.base_dir.parent.parent
        self.community_dir = self.base_dir.parent
    
    def run_django_tests(self, test_pattern=None, verbosity=2, keepdb=False):
        """Run Django tests using manage.py test"""
        cmd = [
            sys.executable, 
            str(self.backend_dir / 'manage.py'), 
            'test'
        ]
        
        if test_pattern:
            cmd.append(f'community.tests.{test_pattern}')
        else:
            cmd.append('community.tests')
        
        cmd.extend([
            f'--verbosity={verbosity}',
            '--parallel', 'auto',
        ])
        
        if keepdb:
            cmd.append('--keepdb')
        
        print(f"Running Django tests: {' '.join(cmd)}")
        return subprocess.run(cmd, cwd=self.backend_dir)
    
    def run_pytest(self, test_pattern=None, coverage=True, markers=None):
        """Run tests using pytest"""
        cmd = ['pytest']
        
        # Test path
        if test_pattern:
            cmd.append(str(self.base_dir / f'{test_pattern}.py'))
        else:
            cmd.append(str(self.base_dir))
        
        # Coverage options
        if coverage:
            cmd.extend([
                '--cov=community',
                '--cov-report=html:htmlcov',
                '--cov-report=term-missing',
                '--cov-fail-under=80',
            ])
        
        # Markers
        if markers:
            cmd.extend(['-m', markers])
        
        # Additional options
        cmd.extend([
            '-v',
            '--tb=short',
            '--strict-markers',
            '--disable-warnings',
        ])
        
        print(f"Running pytest: {' '.join(cmd)}")
        return subprocess.run(cmd, cwd=self.backend_dir)
    
    def run_security_tests(self):
        """Run security-specific tests"""
        print("Running security tests...")
        return self.run_pytest(markers='security', coverage=True)
    
    def run_integration_tests(self):
        """Run integration tests"""
        print("Running integration tests...")
        return self.run_pytest(markers='integration', coverage=True)
    
    def run_unit_tests(self):
        """Run unit tests"""
        print("Running unit tests...")
        return self.run_pytest(markers='unit', coverage=True)
    
    def run_performance_tests(self):
        """Run performance tests"""
        print("Running performance tests...")
        return self.run_pytest(markers='slow', coverage=False)
    
    def run_specific_test_file(self, test_file):
        """Run a specific test file"""
        print(f"Running specific test file: {test_file}")
        return self.run_pytest(test_pattern=test_file, coverage=True)
    
    def run_coverage_report(self):
        """Generate detailed coverage report"""
        cmd = [
            'coverage', 'html',
            '--directory=htmlcov',
            '--title=Community App Coverage Report'
        ]
        
        print("Generating coverage report...")
        result = subprocess.run(cmd, cwd=self.backend_dir)
        
        if result.returncode == 0:
            print(f"Coverage report generated: {self.backend_dir}/htmlcov/index.html")
        
        return result
    
    def run_all_tests(self):
        """Run all tests with comprehensive reporting"""
        print("Running comprehensive test suite...")
        
        # Run all tests with coverage
        result = self.run_pytest(coverage=True)
        
        if result.returncode == 0:
            print("✅ All tests passed!")
            self.run_coverage_report()
        else:
            print("❌ Some tests failed!")
        
        return result
    
    def lint_code(self):
        """Run code linting"""
        print("Running code linting...")
        
        # Run flake8
        flake8_cmd = ['flake8', str(self.community_dir), '--max-line-length=100']
        flake8_result = subprocess.run(flake8_cmd, cwd=self.backend_dir)
        
        # Run black check
        black_cmd = ['black', '--check', str(self.community_dir)]
        black_result = subprocess.run(black_cmd, cwd=self.backend_dir)
        
        if flake8_result.returncode == 0 and black_result.returncode == 0:
            print("✅ Code linting passed!")
        else:
            print("❌ Code linting failed!")
        
        return max(flake8_result.returncode, black_result.returncode)
    
    def check_migrations(self):
        """Check for missing migrations"""
        print("Checking for missing migrations...")
        
        cmd = [
            sys.executable,
            str(self.backend_dir / 'manage.py'),
            'makemigrations',
            '--check',
            '--dry-run',
            'community'
        ]
        
        result = subprocess.run(cmd, cwd=self.backend_dir)
        
        if result.returncode == 0:
            print("✅ No missing migrations!")
        else:
            print("❌ Missing migrations detected!")
        
        return result
    
    def run_quality_checks(self):
        """Run all quality checks"""
        print("Running quality checks...")
        
        # Check migrations
        migration_result = self.check_migrations()
        
        # Lint code
        lint_result = self.lint_code()
        
        # Run tests
        test_result = self.run_all_tests()
        
        overall_result = max(
            migration_result.returncode,
            lint_result,
            test_result.returncode
        )
        
        if overall_result == 0:
            print("🎉 All quality checks passed!")
        else:
            print("💥 Some quality checks failed!")
        
        return overall_result


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Community App Test Runner')
    parser.add_argument(
        'command',
        choices=[
            'all', 'django', 'pytest', 'security', 'integration', 
            'unit', 'performance', 'coverage', 'lint', 'migrations',
            'quality', 'specific'
        ],
        help='Test command to run'
    )
    parser.add_argument(
        '--test-file',
        help='Specific test file to run (for specific command)'
    )
    parser.add_argument(
        '--pattern',
        help='Test pattern to match'
    )
    parser.add_argument(
        '--no-coverage',
        action='store_true',
        help='Disable coverage reporting'
    )
    parser.add_argument(
        '--keepdb',
        action='store_true',
        help='Keep test database (Django tests only)'
    )
    parser.add_argument(
        '--verbosity',
        type=int,
        default=2,
        help='Verbosity level (Django tests only)'
    )
    
    args = parser.parse_args()
    runner = CommunityTestRunner()
    
    try:
        if args.command == 'all':
            result = runner.run_all_tests()
        elif args.command == 'django':
            result = runner.run_django_tests(
                test_pattern=args.pattern,
                verbosity=args.verbosity,
                keepdb=args.keepdb
            )
        elif args.command == 'pytest':
            result = runner.run_pytest(
                test_pattern=args.pattern,
                coverage=not args.no_coverage
            )
        elif args.command == 'security':
            result = runner.run_security_tests()
        elif args.command == 'integration':
            result = runner.run_integration_tests()
        elif args.command == 'unit':
            result = runner.run_unit_tests()
        elif args.command == 'performance':
            result = runner.run_performance_tests()
        elif args.command == 'coverage':
            result = runner.run_coverage_report()
        elif args.command == 'lint':
            result = runner.lint_code()
        elif args.command == 'migrations':
            result = runner.check_migrations()
        elif args.command == 'quality':
            result = runner.run_quality_checks()
        elif args.command == 'specific':
            if not args.test_file:
                print("Error: --test-file is required for specific command")
                return 1
            result = runner.run_specific_test_file(args.test_file)
        else:
            print(f"Unknown command: {args.command}")
            return 1
        
        return result.returncode if hasattr(result, 'returncode') else result
    
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
        return 130
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
