"""
Community Views
API views for community features
"""

from rest_framework import viewsets, permissions, status, filters, serializers
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny, IsAuthenticatedOrReadOnly
from django.utils import timezone
from django.db import models
from django.db.models import Q, Count, Prefetch
from django_filters.rest_framework import DjangoFilterBackend
from django.contrib.auth.models import User
from datetime import timedelta

from .models import (
    CommunityPost, PostSave, PostShare, CommunityComment,
    UserFollow, UserActivity, OnlineUser, CommunityStats
)
from api.models import Tag
from .serializers import (
    CommunityPostSerializer, SimpleCommunityCommentSerializer,
    UserFollowSerializer, UserActivitySerializer, OnlineUserSerializer,
    CommunityStatsSerializer, CommunityFeedSerializer
)
from .permissions import CommunityReadOnlyOrAuthenticated, CommunityStatsReadOnly, AuthenticatedWriteOnly
from .security import security_logger
from .cache import CommunityCache, cache_result, cache_queryset


class CommunityPostViewSet(viewsets.ModelViewSet):
    """
    ViewSet for community posts with social features
    Allows read-only access for anonymous users to public posts
    """
    serializer_class = CommunityPostSerializer
    permission_classes = [CommunityReadOnlyOrAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['visibility', 'author']
    search_fields = ['title', 'content', 'tags__name']
    ordering_fields = ['created_at', 'like_count', 'comments_count']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """Filter posts based on visibility and user permissions"""
        user = self.request.user
        queryset = CommunityPost.objects.select_related('author').prefetch_related(
            'tags', 'likes', 'saves',
            Prefetch('comments', queryset=CommunityComment.objects.select_related('author'))
        )
        
        # Filter by visibility
        if user.is_authenticated:
            # Show public posts, user's own posts, and posts from followed users
            following_users = UserFollow.objects.filter(follower=user).values_list('following', flat=True)
            queryset = queryset.filter(
                Q(visibility='public') |
                Q(author=user) |
                Q(visibility='followers', author__in=following_users)
            )
        else:
            # Only public posts for anonymous users
            queryset = queryset.filter(visibility='public')
        
        return queryset
    
    def perform_create(self, serializer):
        """Set the author to the current user with security validation"""
        # Additional security check - ensure user is authenticated
        if not self.request.user.is_authenticated:
            security_logger.log_authentication_failure(
                user_id=None,
                reason='unauthenticated_post_creation',
                request_info={'path': self.request.path, 'method': self.request.method}
            )
            raise serializers.ValidationError("Authentication required to create posts.")

        # Check if user is active and not banned
        if not self.request.user.is_active:
            security_logger.log_authentication_failure(
                user_id=self.request.user.id,
                reason='inactive_user_post_creation',
                request_info={'path': self.request.path, 'method': self.request.method}
            )
            raise serializers.ValidationError("Account is inactive.")

        # Save the post with the authenticated user as author
        post = serializer.save(author=self.request.user)

        # Log post creation for security monitoring
        security_logger.log_suspicious_activity(
            user_id=self.request.user.id,
            activity_type='post_created',
            details={
                'post_id': post.id,
                'title_length': len(post.title),
                'content_length': len(post.content),
                'visibility': post.visibility
            }
        )

        # Create activity record
        UserActivity.objects.create(
            user=self.request.user,
            activity_type='post_created',
            content_type='post',
            object_id=post.id
        )
    
    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def like(self, request, pk=None):
        """Like or unlike a post with security validation"""
        # pk parameter used implicitly by get_object() method
        # Additional security checks
        if not request.user.is_authenticated:
            security_logger.log_authentication_failure(
                user_id=None,
                reason='unauthenticated_like_attempt',
                request_info={'path': request.path, 'post_id': pk}
            )
            return Response({'error': 'Authentication required'}, status=status.HTTP_401_UNAUTHORIZED)

        if not request.user.is_active:
            security_logger.log_authentication_failure(
                user_id=request.user.id,
                reason='inactive_user_like_attempt',
                request_info={'path': request.path, 'post_id': pk}
            )
            return Response({'error': 'Account is inactive'}, status=status.HTTP_403_FORBIDDEN)

        post = self.get_object()
        user = request.user

        # Prevent users from liking their own posts (optional business rule)
        if post.author == user:
            return Response({
                'error': 'Cannot like your own post',
                'liked': False,
                'like_count': post.like_count
            }, status=status.HTTP_400_BAD_REQUEST)

        if post.likes.filter(id=user.id).exists():
            # Unlike
            post.likes.remove(user)
            liked = False
            action_type = 'post_unliked'
        else:
            # Like
            post.likes.add(user)
            liked = True
            action_type = 'post_liked'

            # Create activity record
            UserActivity.objects.create(
                user=user,
                activity_type=action_type,
                content_type='post',
                object_id=post.id
            )

        # Log the action for security monitoring
        security_logger.log_suspicious_activity(
            user_id=user.id,
            activity_type=action_type,
            details={'post_id': post.id, 'post_author': post.author.id}
        )

        return Response({
            'liked': liked,
            'like_count': post.like_count
        })
    
    @action(detail=True, methods=['post'])
    def save(self, request, pk=None):
        """Save or unsave a post"""
        # pk parameter used implicitly by get_object() method
        post = self.get_object()
        user = request.user
        
        save_obj, created = PostSave.objects.get_or_create(user=user, post=post)
        if not created:
            # Already saved, so unsave
            save_obj.delete()
            saved = False
        else:
            saved = True
        
        return Response({
            'saved': saved,
            'save_count': post.save_count
        })
    
    @action(detail=True, methods=['post'])
    def share(self, request, pk=None):
        """Share a post"""
        # pk parameter used implicitly by get_object() method
        post = self.get_object()
        user = request.user
        platform = request.data.get('platform', 'internal')
        
        # Create share record
        PostShare.objects.create(user=user, post=post, platform=platform)
        
        # Increment share count
        post.shares_count += 1
        post.save(update_fields=['shares_count'])
        
        # Create activity record
        UserActivity.objects.create(
            user=user,
            activity_type='post_shared',
            content_type='post',
            object_id=post.id,
            data={'platform': platform}
        )
        
        return Response({
            'shared': True,
            'shares_count': post.shares_count
        })

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def add_comment(self, request, pk=None):
        """Add a comment to a post with security validation"""
        # Additional security checks
        if not request.user.is_authenticated:
            security_logger.log_authentication_failure(
                user_id=None,
                reason='unauthenticated_comment_attempt',
                request_info={'path': request.path, 'post_id': pk}
            )
            return Response({'error': 'Authentication required'}, status=status.HTTP_401_UNAUTHORIZED)

        if not request.user.is_active:
            security_logger.log_authentication_failure(
                user_id=request.user.id,
                reason='inactive_user_comment_attempt',
                request_info={'path': request.path, 'post_id': pk}
            )
            return Response({'error': 'Account is inactive'}, status=status.HTTP_403_FORBIDDEN)

        post = self.get_object()

        # Check if post allows comments
        if not post.allow_comments:
            return Response({
                'error': 'Comments are not allowed on this post'
            }, status=status.HTTP_403_FORBIDDEN)

        # Add post to request data for serializer validation
        comment_data = request.data.copy()
        comment_data['post'] = post.id

        serializer = SimpleCommunityCommentSerializer(data=comment_data, context={'request': request})

        if serializer.is_valid():
            comment = serializer.save(
                author=request.user,
                post=post
            )

            # Log comment creation for security monitoring
            security_logger.log_suspicious_activity(
                user_id=request.user.id,
                activity_type='comment_created',
                details={
                    'comment_id': comment.id,
                    'post_id': post.id,
                    'content_length': len(comment.content)
                }
            )

            # Create activity record
            UserActivity.objects.create(
                user=request.user,
                activity_type='comment_created',
                content_type='comment',
                object_id=comment.id
            )

            return Response(SimpleCommunityCommentSerializer(comment, context={'request': request}).data, status=status.HTTP_201_CREATED)

        # Log validation errors for security monitoring
        security_logger.log_suspicious_activity(
            user_id=request.user.id,
            activity_type='comment_validation_failed',
            details={'errors': serializer.errors, 'post_id': pk}
        )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def comments(self, request, pk=None):
        """Get all comments for a post"""
        # request and pk parameters required by Django REST framework
        post = self.get_object()
        comments = CommunityComment.objects.filter(post=post).order_by('created_at')  # Removed parent filter since we simplified
        serializer = SimpleCommunityCommentSerializer(comments, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def feed(self, request):
        """Get personalized feed for the user with caching"""
        user = request.user

        # Try to get from cache first
        cache_key = CommunityCache.get_feed_cache_key(user.id)
        cached_feed = CommunityCache.get(cache_key)

        if cached_feed is not None:
            return Response(cached_feed)

        # Get posts from followed users and own posts
        following_users = UserFollow.objects.filter(follower=user).values_list('following', flat=True)
        feed_users = list(following_users) + [user.id]

        posts_queryset = self.get_queryset().filter(author__in=feed_users)
        posts = list(posts_queryset[:20])

        serializer = self.get_serializer(posts, many=True)
        feed_data = {
            'posts': serializer.data,
            'has_more': len(posts) == 20,
            'next_cursor': posts[-1].id if posts else None
        }

        # Cache the feed for 10 minutes
        CommunityCache.set_with_timeout(cache_key, feed_data, 'post_feed')

        return Response(feed_data)
    
    @action(detail=False, methods=['get'])
    def trending(self, request):
        """Get trending posts (most liked/commented in last 24 hours)"""
        # request parameter required by Django REST framework but not used
        yesterday = timezone.now() - timedelta(days=1)
        
        posts = self.get_queryset().filter(
            created_at__gte=yesterday
        ).annotate(
            engagement_score=Count('likes') + Count('comments') * 2
        ).order_by('-engagement_score')[:20]
        
        serializer = self.get_serializer(posts, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def following(self, request):
        """Get posts from users you follow"""
        user = request.user
        following_users = UserFollow.objects.filter(follower=user).values_list('following', flat=True)

        if not following_users:
            return Response([])

        posts = self.get_queryset().filter(author__in=following_users)[:20]
        serializer = self.get_serializer(posts, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def saved(self, request):
        """Get saved posts"""
        user = request.user
        saved_post_ids = PostSave.objects.filter(user=user).values_list('post', flat=True)

        if not saved_post_ids:
            return Response([])

        posts = self.get_queryset().filter(id__in=saved_post_ids)
        serializer = self.get_serializer(posts, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def upload_media(self, request):
        """Upload media file for posts"""
        if 'file' not in request.FILES:
            return Response({'error': 'No file provided'}, status=status.HTTP_400_BAD_REQUEST)

        file = request.FILES['file']

        # Validate file type
        allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'video/webm']
        if file.content_type not in allowed_types:
            return Response({'error': 'File type not allowed'}, status=status.HTTP_400_BAD_REQUEST)

        # Validate file size (10MB limit)
        if file.size > 10 * 1024 * 1024:
            return Response({'error': 'File too large'}, status=status.HTTP_400_BAD_REQUEST)

        # Save file (in production, use cloud storage)
        import os
        from django.conf import settings

        upload_dir = os.path.join(settings.MEDIA_ROOT, 'community', 'uploads')
        os.makedirs(upload_dir, exist_ok=True)

        file_path = os.path.join(upload_dir, file.name)
        with open(file_path, 'wb+') as destination:
            for chunk in file.chunks():
                destination.write(chunk)

        # Return file URL
        file_url = f"/media/community/uploads/{file.name}"
        file_type = 'image' if file.content_type.startswith('image/') else 'video'

        return Response({
            'url': file_url,
            'type': file_type
        })


class SimpleCommunityCommentViewSet(viewsets.ModelViewSet):
    """
    Facebook-style ViewSet for community post comments with replies and likes
    """
    serializer_class = SimpleCommunityCommentSerializer
    permission_classes = [AllowAny]  # Temporarily allow anonymous for testing

    def get_queryset(self):
        return CommunityComment.objects.select_related('author', 'post').prefetch_related('likes', 'replies').order_by('created_at')
    
    def perform_create(self, serializer):
        """Create simple comment"""
        import logging
        logger = logging.getLogger(__name__)

        # Debug: Log everything about the request
        print(f"🎯 BACKEND: Creating comment")
        print(f"🎯 BACKEND: Request data: {self.request.data}")
        print(f"🎯 BACKEND: Request method: {self.request.method}")
        print(f"🎯 BACKEND: User: {self.request.user}")
        print(f"🎯 BACKEND: User authenticated: {self.request.user.is_authenticated}")

        logger.info(f"Creating comment with data: {self.request.data}")
        logger.info(f"User: {self.request.user}")

        # Get post from request data
        post_id = self.request.data.get('post')
        print(f"🎯 BACKEND: Post ID from request: {post_id}")

        if not post_id:
            print("❌ BACKEND: Post ID is missing!")
            logger.error("Post ID is missing from request data")
            raise ValidationError("Post ID is required")

        try:
            post = CommunityPost.objects.get(id=post_id)
            logger.info(f"Found post: {post.id} - {post.title}")
        except CommunityPost.DoesNotExist:
            logger.error(f"Post not found: {post_id}")
            raise ValidationError("Post not found")

        # Check if comments are allowed
        if not post.allow_comments:
            logger.error(f"Comments not allowed on post: {post_id}")
            raise ValidationError("Comments are not allowed on this post")

        # Save comment
        try:
            # Handle anonymous users for testing
            author = self.request.user if self.request.user.is_authenticated else None
            if not author:
                # For testing, create or get a default user
                from django.contrib.auth.models import User
                author, _ = User.objects.get_or_create(
                    username='anonymous_user',
                    defaults={'email': '<EMAIL>'}
                )

            comment = serializer.save(author=author, post=post)
            logger.info(f"Comment created successfully: {comment.id}")
        except Exception as e:
            logger.error(f"Error saving comment: {e}")
            raise

        # Update post comment count
        post.comments_count = post.comments.count()
        post.save(update_fields=['comments_count'])

    def get_permissions(self):
        """Temporarily allow anyone to do anything for testing"""
        return [AllowAny()]

    @action(detail=True, methods=['post'])
    def like(self, request, pk=None):
        """Like/unlike a comment"""
        comment = self.get_object()
        user = request.user

        if comment.likes.filter(id=user.id).exists():
            # Unlike
            comment.likes.remove(user)
            liked = False
        else:
            # Like
            comment.likes.add(user)
            liked = True

        return Response({
            'liked': liked,
            'like_count': comment.like_count
        })

    @action(detail=True, methods=['post'])
    def reply(self, request, pk=None):
        """Reply to a comment"""
        parent_comment = self.get_object()

        # Prevent deep nesting (max 2 levels like Facebook)
        if parent_comment.parent is not None:
            return Response(
                {'error': 'Cannot reply to a reply. Please reply to the main comment.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create reply
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            reply = serializer.save(
                author=request.user,
                post=parent_comment.post,
                parent=parent_comment
            )
            return Response(
                SimpleCommunityCommentSerializer(reply, context={'request': request}).data,
                status=status.HTTP_201_CREATED
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserFollowViewSet(viewsets.ModelViewSet):
    """
    ViewSet for user follow relationships
    """
    serializer_class = UserFollowSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return UserFollow.objects.select_related('follower', 'following')
    
    @action(detail=False, methods=['post'])
    def follow_user(self, request):
        """Follow a user"""
        user_id = request.data.get('user_id')
        if not user_id:
            return Response({'error': 'user_id required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            from django.contrib.auth.models import User
            target_user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)
        
        if target_user == request.user:
            return Response({'error': 'Cannot follow yourself'}, status=status.HTTP_400_BAD_REQUEST)
        
        follow, created = UserFollow.objects.get_or_create(
            follower=request.user,
            following=target_user
        )
        
        if not created:
            # Already following, so unfollow
            follow.delete()
            following = False
        else:
            following = True
            
            # Create activity record
            UserActivity.objects.create(
                user=request.user,
                activity_type='user_followed',
                content_type='user',
                object_id=target_user.id
            )
        
        return Response({
            'following': following,
            'followers_count': target_user.followers.count(),
            'following_count': target_user.following.count()
        })

    @action(detail=False, methods=['get'])
    def profile(self, request):
        """Get user profile by ID"""
        user_id = request.query_params.get('user_id')
        if not user_id:
            return Response({'error': 'user_id required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            target_user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

        # Check if current user is following target user
        is_following = False
        is_followed_by = False
        if request.user.is_authenticated:
            is_following = UserFollow.objects.filter(
                follower=request.user,
                following=target_user
            ).exists()
            is_followed_by = UserFollow.objects.filter(
                follower=target_user,
                following=request.user
            ).exists()

        profile_data = {
            'id': target_user.id,
            'username': target_user.username,
            'first_name': target_user.first_name,
            'last_name': target_user.last_name,
            'full_name': f"{target_user.first_name} {target_user.last_name}".strip(),
            'avatar': target_user.userprofile.profile_image.url if hasattr(target_user, 'userprofile') and target_user.userprofile.profile_image else None,
            'is_verified': getattr(target_user, 'is_verified', False),
            'bio': getattr(target_user.userprofile, 'bio', '') if hasattr(target_user, 'userprofile') else '',
            'location': getattr(target_user.userprofile, 'location', '') if hasattr(target_user, 'userprofile') else '',
            'website': getattr(target_user.userprofile, 'website', '') if hasattr(target_user, 'userprofile') else '',
            'user_role': getattr(target_user, 'user_role', 'Member'),
            'date_joined': target_user.date_joined.isoformat(),
            'followers_count': target_user.followers.count(),
            'following_count': target_user.following.count(),
            'posts_count': CommunityPost.objects.filter(author=target_user).count(),
            'is_following': is_following,
            'is_followed_by': is_followed_by,
        }

        return Response(profile_data)

    @action(detail=False, methods=['get'])
    def followers(self, request):
        """Get user's followers"""
        user_id = request.query_params.get('user_id')
        if not user_id:
            return Response({'error': 'user_id required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            target_user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

        followers = UserFollow.objects.filter(following=target_user).select_related('follower')
        follower_data = []

        for follow in followers:
            user = follow.follower
            follower_data.append({
                'id': user.id,
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'full_name': f"{user.first_name} {user.last_name}".strip(),
                'avatar': user.userprofile.profile_image.url if hasattr(user, 'userprofile') and user.userprofile.profile_image else None,
                'is_verified': getattr(user, 'is_verified', False),
                'user_role': getattr(user, 'user_role', 'Member'),
            })

        return Response(follower_data)

    @action(detail=False, methods=['get'])
    def following(self, request):
        """Get users that this user follows"""
        user_id = request.query_params.get('user_id')
        if not user_id:
            return Response({'error': 'user_id required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            target_user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

        following = UserFollow.objects.filter(follower=target_user).select_related('following')
        following_data = []

        for follow in following:
            user = follow.following
            following_data.append({
                'id': user.id,
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'full_name': f"{user.first_name} {user.last_name}".strip(),
                'avatar': user.userprofile.profile_image.url if hasattr(user, 'userprofile') and user.userprofile.profile_image else None,
                'is_verified': getattr(user, 'is_verified', False),
                'user_role': getattr(user, 'user_role', 'Member'),
            })

        return Response(following_data)


class CommunityStatsViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for community statistics
    Allows read-only access for everyone including anonymous users
    """
    serializer_class = CommunityStatsSerializer
    permission_classes = [CommunityStatsReadOnly]
    
    def get_queryset(self):
        return CommunityStats.objects.all()
    
    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get current community stats with caching"""
        # request parameter required by Django REST framework but not used
        # Try to get from cache first
        cache_key = CommunityCache.get_stats_cache_key()
        cached_stats = CommunityCache.get(cache_key)

        if cached_stats is not None:
            return Response(cached_stats)

        # Get or create stats from database
        stats, created = CommunityStats.objects.get_or_create(id=1)

        # Update stats if older than 1 hour or if created
        if created or stats.updated_at < timezone.now() - timedelta(hours=1):
            # Use more efficient queries
            stats.total_posts = CommunityPost.objects.count()
            stats.total_users = User.objects.filter(is_active=True).count()

            # More efficient likes counting using aggregation
            from django.db.models import Sum
            posts_with_likes = CommunityPost.objects.annotate(
                like_count=models.Count('likes')
            ).aggregate(
                total_likes=Sum('like_count')
            )
            stats.total_likes = posts_with_likes['total_likes'] or 0

            stats.total_comments = CommunityComment.objects.count()

            # Today's stats
            today = timezone.now().date()
            stats.posts_today = CommunityPost.objects.filter(
                created_at__date=today
            ).count()
            stats.active_users_today = UserActivity.objects.filter(
                created_at__date=today
            ).values('user').distinct().count()

            stats.save()

            # Invalidate cache after update
            CommunityCache.invalidate_stats_cache()

        # Serialize and cache the result
        serializer = self.get_serializer(stats)
        serialized_data = serializer.data

        # Cache for 1 hour
        CommunityCache.set_with_timeout(cache_key, serialized_data, 'community_stats')

        return Response(serialized_data)

    @action(detail=False, methods=['get'])
    def ping(self, request):
        """Health check endpoint for community service"""
        # request parameter required by Django REST framework but not used
        return Response({'status': 'ok'})


class HashtagViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for hashtag-related endpoints
    """
    permission_classes = [permissions.AllowAny]  # Allow anonymous access for trending topics

    @action(detail=False, methods=['get'], url_path='trending')
    def trending(self, request):
        """
        Get trending hashtags based on recent usage in community posts with caching
        """
        # request parameter required by Django REST framework but not used
        # Try to get from cache first
        cache_key = CommunityCache.get_hashtags_cache_key()
        cached_hashtags = CommunityCache.get(cache_key)

        if cached_hashtags is not None:
            return Response(cached_hashtags)

        # Get hashtags used in posts from the last 7 days
        seven_days_ago = timezone.now() - timedelta(days=7)

        # Count hashtag usage in recent posts
        trending_tags = Tag.objects.filter(
            community_posts__created_at__gte=seven_days_ago
        ).annotate(
            count=Count('community_posts'),
            recent_count=Count('community_posts', filter=Q(community_posts__created_at__gte=seven_days_ago))
        ).filter(
            count__gt=0
        ).order_by('-recent_count', '-count')[:10]

        # Format response to match frontend expectations
        trending_data = []
        for tag in trending_tags:
            trending_data.append({
                'tag': tag.name,
                'count': tag.recent_count,
                'trending': tag.recent_count > 0
            })

        # Cache the result for 30 minutes
        CommunityCache.set_with_timeout(cache_key, trending_data, 'trending_hashtags')

        return Response(trending_data)


class UserRecommendationViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for user recommendation endpoints
    """
    permission_classes = [permissions.AllowAny]  # Allow anonymous access

    @action(detail=False, methods=['get'], url_path='recommendations')
    def recommendations(self, request):
        """
        Get user recommendations based on activity and filters with caching
        """
        # Handle anonymous users
        if not request.user.is_authenticated:
            # Return empty recommendations for anonymous users
            return Response([])

        # Get query parameters for cache key
        params = {
            'role': request.query_params.get('role'),
            'location': request.query_params.get('location'),
            'interests': request.query_params.getlist('interests'),
            'activity_level': request.query_params.get('activity_level', 'all'),
            'verification_status': request.query_params.get('verification_status', 'all'),
            'user_id': request.user.id
        }

        # Try to get from cache first
        cache_key = CommunityCache.get_recommendations_cache_key(**params)
        cached_recommendations = CommunityCache.get(cache_key)

        if cached_recommendations is not None:
            return Response(cached_recommendations)

        # Extract parameters for processing
        role = params['role']
        location = params['location']
        interests = params['interests']
        activity_level = params['activity_level']
        verification_status = params['verification_status']

        # Start with all users except current user
        users = User.objects.exclude(id=request.user.id).select_related('profile')

        # Filter by role (if user has a profile with role information)
        if role:
            users = users.filter(profile__user_role__icontains=role)

        # Filter by location (if user has a profile with location information)
        if location:
            users = users.filter(profile__location__icontains=location)

        # Filter by interests (if user has posts with matching tags)
        if interests:
            users = users.filter(
                community_posts__tags__name__in=interests
            ).distinct()

        # Filter by verification status
        if verification_status == 'verified':
            users = users.filter(is_staff=True)  # Use staff status as verification
        elif verification_status == 'unverified':
            users = users.filter(is_staff=False)

        # Filter by activity level
        if activity_level == 'high':
            # Users with recent activity (last 7 days)
            seven_days_ago = timezone.now() - timedelta(days=7)
            active_users = UserActivity.objects.filter(
                created_at__gte=seven_days_ago
            ).values_list('user_id', flat=True).distinct()
            users = users.filter(id__in=active_users)
        elif activity_level == 'medium':
            # Users with activity in last 30 days
            thirty_days_ago = timezone.now() - timedelta(days=30)
            active_users = UserActivity.objects.filter(
                created_at__gte=thirty_days_ago
            ).values_list('user_id', flat=True).distinct()
            users = users.filter(id__in=active_users)

        # Limit results
        users = users[:20]

        # Format response to match frontend expectations
        recommendations = [self._format_user_recommendation(user) for user in users]

        # Cache the recommendations for 2 hours
        CommunityCache.set_with_timeout(cache_key, recommendations, 'user_recommendations')

        return Response(recommendations)

    def _format_user_recommendation(self, user):
        """Helper method to format user data for recommendations"""
        try:
            profile = user.profile
            return {
                'id': user.id,
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'full_name': f"{user.first_name} {user.last_name}".strip() or user.username,
                'avatar': profile.profile_image.url if profile.profile_image else None,
                'user_role': getattr(profile, 'job_title', None) or 'Member',
                'bio': getattr(profile, 'bio', ''),
                'location': getattr(profile, 'location', ''),
            }
        except AttributeError:
            # Handle users without profiles or missing profile attributes
            return {
                'id': user.id,
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'full_name': f"{user.first_name} {user.last_name}".strip() or user.username,
                'avatar': None,
                'user_role': 'Member',
                'bio': '',
                'location': '',
            }

        return Response(recommendations)


# Test endpoint to verify server is working
from rest_framework.decorators import api_view, permission_classes

@api_view(['GET', 'POST'])
@permission_classes([AllowAny])
def test_endpoint(request):
    """Simple test endpoint to verify server is working"""
    print(f"🎯 TEST ENDPOINT: {request.method} request received")
    print(f"🎯 TEST ENDPOINT: Data: {request.data}")

    return Response({
        'status': 'working',
        'method': request.method,
        'data': request.data,
        'message': 'Server is responding correctly!'
    })
