import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Send, MessageCircle, Heart, MoreHorizontal, Reply } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { CommunityAuthGuard } from './CommunityAuthGuard';
import { FacebookAvatar } from './FacebookAvatar';
import { Button } from '../ui/StandardizedComponents';
import { getButtonClasses, getInputClasses, designTokens } from '../../styles/designSystem';
import { useUserNavigation } from '../../hooks/useUserNavigation';

interface Comment {
  id: string;
  content: string;
  author_name: string;
  time_ago: string;
  created_at: string;
  parent?: string | null;
  like_count: number;
  reply_count: number;
  is_liked: boolean;
  replies: Comment[];
}

interface SimpleCommentSectionProps {
  postId: string;
  comments: Comment[];
  onAddComment: (content: string, parentId?: string) => Promise<void>;
  onLikeComment?: (commentId: string) => Promise<void>;
  isLoading?: boolean;
}

/**
 * Simple comment section - no nesting, no complex features
 * Just a clean list of comments with a simple input form
 */
export const SimpleCommentSection: React.FC<SimpleCommentSectionProps> = ({
  postId,
  comments,
  onAddComment,
  onLikeComment,
  isLoading = false
}) => {
  const { t } = useTranslation();
  const { isAuthenticated } = useAuth();
  const { handleUserClick } = useUserNavigation();
  const [newComment, setNewComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newComment.trim() || isSubmitting) return;

    try {
      setIsSubmitting(true);
      await onAddComment(newComment.trim());
      setNewComment(''); // Clear input on success
    } catch (error) {
      console.error('Failed to add comment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReply = async (parentId: string) => {
    if (!replyContent.trim() || isSubmitting) return;

    try {
      setIsSubmitting(true);
      await onAddComment(replyContent.trim(), parentId);
      setReplyContent('');
      setReplyingTo(null);
    } catch (error) {
      console.error('Failed to add reply:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLike = async (commentId: string) => {
    if (!onLikeComment) return;

    try {
      await onLikeComment(commentId);
    } catch (error) {
      console.error('Failed to like comment:', error);
    }
  };

  // Render individual comment with Facebook-style design
  const renderComment = (comment: Comment, isReply = false) => (
    <div key={comment.id} className={`flex gap-3 group ${isReply ? 'ml-8 mt-2' : ''}`}>
      {/* Vertical line for replies */}
      {isReply && (
        <div className="absolute left-6 top-0 w-0.5 h-full bg-glass-border opacity-30"></div>
      )}

      {/* User Avatar */}
      <FacebookAvatar
        username={comment.author_name}
        userId={comment.id} // You might want to pass actual user ID here
        size="md"
        className="flex-shrink-0"
        onClick={handleUserClick}
        clickable={true}
      />

      {/* Comment Content */}
      <div className="flex-1 min-w-0">
        {/* Comment Bubble */}
        <div
          className="inline-block max-w-full rounded-2xl px-3 py-2"
          style={{
            backgroundColor: designTokens.colors.glass.bg,
            border: `1px solid ${designTokens.colors.glass.border}`,
            backdropFilter: 'blur(10px)'
          }}
        >
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleUserClick(comment.author_name);
            }}
            className="font-medium text-white text-sm mb-1 hover:underline cursor-pointer bg-transparent border-none p-0"
          >
            {comment.author_name}
          </button>
          <p className="text-gray-200 text-sm leading-relaxed break-words">
            {comment.content}
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-4 mt-1 ml-3 text-xs">
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleLike(comment.id);
            }}
            className={`hover:text-blue-400 transition-colors font-medium cursor-pointer ${
              comment.is_liked ? 'text-blue-400' : 'text-glass-muted'
            }`}
            disabled={!onLikeComment}
          >
            <Heart className={`w-3 h-3 inline mr-1 ${comment.is_liked ? 'fill-current' : ''}`} />
            Like {comment.like_count > 0 && `(${comment.like_count})`}
          </button>

          {!isReply && (
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setReplyingTo(replyingTo === comment.id ? null : comment.id);
              }}
              className="hover:text-blue-400 transition-colors font-medium text-glass-muted cursor-pointer"
            >
              <Reply className="w-3 h-3 inline mr-1" />
              Reply
            </button>
          )}

          <span className="text-glass-muted">
            {comment.time_ago}
          </span>

          <button
            className="opacity-0 group-hover:opacity-100 hover:text-glass-secondary transition-all cursor-pointer"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
          >
            <MoreHorizontal className="w-3 h-3" />
          </button>
        </div>

        {/* Reply Input */}
        {replyingTo === comment.id && (
          <div className="flex items-start gap-2 mt-3 ml-3">
            <FacebookAvatar
              username={isAuthenticated ? 'User' : 'Guest'}
              size="sm"
              clickable={false}
            />
            <div className="flex-1">
              <textarea
                value={replyContent}
                onChange={(e) => setReplyContent(e.target.value)}
                placeholder={`Reply to ${comment.author_name}...`}
                className={getInputClasses('glass', 'sm', 'resize-none rounded-2xl text-sm')}
                rows={1}
                maxLength={500}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleReply(comment.id);
                  }
                }}
              />
              <div className="flex items-center gap-2 mt-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleReply(comment.id)}
                  disabled={!replyContent.trim() || isSubmitting}
                >
                  Reply
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setReplyingTo(null);
                    setReplyContent('');
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Replies */}
        {comment.replies && comment.replies.length > 0 && (
          <div className="relative mt-2">
            {comment.replies.map(reply => renderComment(reply, true))}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="space-y-3">
      {/* Comments Header */}
      <div className="flex items-center justify-between border-t border-glass-border pt-3">
        <span className="text-glass-secondary text-sm font-medium">
          {comments.length} {comments.length === 1 ? 'comment' : 'comments'}
        </span>
        <div className="flex items-center gap-4 text-xs text-glass-muted">
          <span>Most relevant</span>
          <MoreHorizontal className="w-4 h-4" />
        </div>
      </div>

      {/* Add Comment Form */}
      <CommunityAuthGuard
        action="add comments"
        showLoginPrompt={false}
        fallback={
          <div
            className="flex items-center gap-3 p-3 rounded-full"
            style={{
              backgroundColor: designTokens.colors.glass.light,
              border: `1px solid ${designTokens.colors.glass.border}`
            }}
          >
            <FacebookAvatar username="Guest" size="md" />
            <div className="flex-1 text-glass-muted text-sm">
              {t('community.comments.loginToComment', 'Log in to write a comment')}
            </div>
          </div>
        }
      >
        <div className="flex items-start gap-3">
          <FacebookAvatar
            username={isAuthenticated ? 'User' : 'Guest'}
            size="md"
            className="flex-shrink-0"
            clickable={false}
          />

          <form onSubmit={handleSubmit} className="flex-1">
            <div className="relative">
              <textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder={t('community.comments.placeholder', 'Write a comment...')}
                className={getInputClasses('glass', 'md', 'resize-none rounded-2xl text-sm')}
                rows={1}
                maxLength={500}
                disabled={isSubmitting}
                style={{ minHeight: '40px' }}
                onInput={(e) => {
                  const target = e.target as HTMLTextAreaElement;
                  target.style.height = 'auto';
                  target.style.height = Math.min(target.scrollHeight, 120) + 'px';
                }}
              />

              {newComment.trim() && (
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={getButtonClasses('primary', 'sm', 'absolute right-2 bottom-2 w-6 h-6 rounded-full p-0')}
                >
                  <Send className="w-3 h-3" />
                </button>
              )}
            </div>
          </form>
        </div>
      </CommunityAuthGuard>

      {/* Comments List */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse flex gap-3">
                <div className="w-8 h-8 bg-glass-bg rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-glass-bg rounded w-1/4"></div>
                  <div className="h-12 bg-glass-bg rounded-2xl w-3/4"></div>
                </div>
              </div>
            ))}
          </div>
        ) : comments.length === 0 ? (
          <div className="text-center py-6 text-glass-muted">
            <MessageCircle className="w-6 h-6 mx-auto mb-2 opacity-50" />
            <p className="text-sm">
              {t('community.comments.noComments', 'No comments yet')}
            </p>
          </div>
        ) : (
          comments.filter(comment => !comment.parent).map((comment) => renderComment(comment))
        )}
      </div>
    </div>
  );
};

export default SimpleCommentSection;
