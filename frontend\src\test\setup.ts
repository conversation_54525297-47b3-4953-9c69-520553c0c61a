/**
 * Test Setup Configuration
 * Global test setup for community testing environment
 */

import { expect, afterEach, vi } from 'vitest';
import { cleanup } from '@testing-library/react';
import * as matchers from '@testing-library/jest-dom/matchers';

// Extend Vitest's expect with jest-dom matchers
expect.extend(matchers);

// Cleanup after each test case
afterEach(() => {
  cleanup();
});

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: vi.fn(),
});

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

// Mock fetch
global.fetch = vi.fn();

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

console.error = (...args: any[]) => {
  // Suppress specific React warnings in tests
  if (
    typeof args[0] === 'string' &&
    (args[0].includes('Warning: ReactDOM.render is no longer supported') ||
     args[0].includes('Warning: An invalid form control') ||
     args[0].includes('Warning: Failed prop type'))
  ) {
    return;
  }
  originalConsoleError.apply(console, args);
};

console.warn = (...args: any[]) => {
  // Suppress specific warnings in tests
  if (
    typeof args[0] === 'string' &&
    (args[0].includes('componentWillReceiveProps') ||
     args[0].includes('componentWillUpdate'))
  ) {
    return;
  }
  originalConsoleWarn.apply(console, args);
};

// Mock environment variables
vi.mock('../../config/environment', () => ({
  API_BASE_URL: 'http://localhost:8000/api',
  APP_ENV: 'test',
  IS_DEVELOPMENT: false,
  IS_PRODUCTION: false,
  IS_TEST: true,
}));

// Mock translation system
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, fallback?: string) => fallback || key,
    i18n: {
      language: 'en',
      changeLanguage: vi.fn(),
    },
  }),
  Trans: ({ children }: { children: React.ReactNode }) => children,
  initReactI18next: {
    type: '3rdParty',
    init: vi.fn(),
  },
}));

// Mock router
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => vi.fn(),
    useLocation: () => ({
      pathname: '/community',
      search: '',
      hash: '',
      state: null,
    }),
    useParams: () => ({}),
    BrowserRouter: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock authentication context
vi.mock('../../hooks/useAuth', () => ({
  useAuth: () => ({
    user: {
      id: 1,
      name: 'Test User',
      email: '<EMAIL>',
      role: 'user',
    },
    isAuthenticated: true,
    login: vi.fn(),
    logout: vi.fn(),
    loading: false,
  }),
}));

// Mock language context
vi.mock('../../hooks/useLanguage', () => ({
  useLanguage: () => ({
    language: 'en',
    isRTL: false,
    changeLanguage: vi.fn(),
  }),
}));

// Mock toast notifications
vi.mock('../../hooks/useToast', () => ({
  useToast: () => ({
    showSuccess: vi.fn(),
    showError: vi.fn(),
    showWarning: vi.fn(),
    showInfo: vi.fn(),
  }),
}));

// Mock API utilities
vi.mock('../../utils/authUtils', () => ({
  apiRequest: vi.fn(),
  getAuthHeaders: vi.fn(() => ({
    'Authorization': 'Bearer test-token',
    'Content-Type': 'application/json',
  })),
}));

// Mock request deduplication
vi.mock('../../utils/requestDeduplication', () => ({
  deduplicateRequest: vi.fn((key, fn) => fn()),
}));

// Global test utilities
export const createMockPost = (overrides = {}) => ({
  id: '1',
  title: 'Test Post',
  content: 'Test content',
  author: {
    id: 1,
    name: 'Test Author',
    avatar: null,
  },
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  likes_count: 0,
  comments_count: 0,
  shares_count: 0,
  is_liked: false,
  is_saved: false,
  hashtags: [],
  ...overrides,
});

export const createMockStats = (overrides = {}) => ({
  total_posts: 10,
  total_users: 5,
  posts_today: 2,
  active_users: 3,
  trending_hashtags: [
    { tag: 'test', count: 5 },
    { tag: 'community', count: 3 },
  ],
  ...overrides,
});

export const createMockUser = (overrides = {}) => ({
  id: 1,
  name: 'Test User',
  email: '<EMAIL>',
  avatar: null,
  role: 'user',
  followers_count: 0,
  following_count: 0,
  posts_count: 0,
  ...overrides,
});

// Test wrapper for React components
import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';

const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <BrowserRouter>
      {children}
    </BrowserRouter>
  );
};

const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react';
export { customRender as render };

// Performance testing utilities
export const measurePerformance = async (fn: () => Promise<void> | void) => {
  const start = performance.now();
  await fn();
  const end = performance.now();
  return end - start;
};

// Async testing utilities
export const waitForNextTick = () => new Promise(resolve => setTimeout(resolve, 0));

export const flushPromises = () => new Promise(resolve => setImmediate(resolve));

// Mock data generators
export const generateMockPosts = (count: number) => {
  return Array.from({ length: count }, (_, index) => createMockPost({
    id: `${index + 1}`,
    title: `Test Post ${index + 1}`,
    content: `Content for test post ${index + 1}`,
    likes_count: Math.floor(Math.random() * 100),
    comments_count: Math.floor(Math.random() * 20),
  }));
};

// Error simulation utilities
export const simulateNetworkError = () => {
  return Promise.reject(new Error('Network Error'));
};

export const simulateServerError = () => {
  return Promise.reject(new Error('Server Error: 500'));
};

export const simulateAuthError = () => {
  return Promise.reject(new Error('Authentication Error: 401'));
};
