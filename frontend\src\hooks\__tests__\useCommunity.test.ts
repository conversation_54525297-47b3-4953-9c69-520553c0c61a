/**
 * Unit Tests for useCommunity Hook
 * Tests the unified community hook functionality including caching, error handling, and performance optimizations
 */

// Note: This test file requires the following dependencies to be installed:
// npm install --save-dev vitest @testing-library/react @testing-library/jest-dom jsdom

import { renderHook, act, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { useCommunity } from '../useCommunity';
import * as communityApi from '../../services/communityApi';
import { CommunityErrorHandler } from '../../utils/communityErrorHandler';

// Mock dependencies
vi.mock('../../services/communityApi');
vi.mock('../../utils/communityErrorHandler');
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, fallback?: string) => fallback || key,
  }),
}));
vi.mock('../../hooks/useLanguage', () => ({
  useLanguage: () => ({
    isRTL: false,
  }),
}));
vi.mock('../../hooks/useAuth', () => ({
  useAuth: () => ({
    user: { id: 1, name: 'Test User' },
    isAuthenticated: true,
  }),
}));
vi.mock('../../hooks/useToast', () => ({
  useToast: () => ({
    showSuccess: vi.fn(),
    showError: vi.fn(),
    showWarning: vi.fn(),
  }),
}));

const mockCommunityApi = communityApi as any;
const mockErrorHandler = CommunityErrorHandler as any;

describe('useCommunity Hook', () => {
  const mockPosts = [
    {
      id: '1',
      title: 'Test Post 1',
      content: 'Test content 1',
      author: { id: 1, name: 'Author 1' },
      created_at: '2024-01-01T00:00:00Z',
      likes_count: 5,
      comments_count: 2,
      is_liked: false,
      is_saved: false,
    },
    {
      id: '2',
      title: 'Test Post 2',
      content: 'Test content 2',
      author: { id: 2, name: 'Author 2' },
      created_at: '2024-01-02T00:00:00Z',
      likes_count: 3,
      comments_count: 1,
      is_liked: true,
      is_saved: false,
    },
  ];

  const mockStats = {
    total_posts: 10,
    total_users: 5,
    posts_today: 2,
    active_users: 3,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mock implementations
    mockCommunityApi.getPosts.mockResolvedValue(mockPosts);
    mockCommunityApi.getStats.mockResolvedValue(mockStats);
    mockCommunityApi.searchPosts.mockResolvedValue([]);
    mockCommunityApi.ping.mockResolvedValue({ status: 'ok' });
    
    mockErrorHandler.parseError.mockImplementation((error: any) => error);
    mockErrorHandler.getLocalizedMessage.mockImplementation((error: any) => error.message || 'Error');
    mockErrorHandler.withRetry.mockImplementation(async (operation: any) => await operation());

    // Mock timers
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('Initial State', () => {
    it('should initialize with correct default values', () => {
      const { result } = renderHook(() => useCommunity());

      expect(result.current.posts).toEqual([]);
      expect(result.current.searchResults).toEqual([]);
      expect(result.current.displayPosts).toEqual([]);
      expect(result.current.activeView).toBe('feed');
      expect(result.current.searchQuery).toBe('');
      expect(result.current.isLoading).toBe(true);
      expect(result.current.isSearching).toBe(false);
      expect(result.current.connectionStatus).toBe('connecting');
      expect(result.current.stats).toBeNull();
    });
  });

  describe('Data Loading', () => {
    it('should load posts on mount', async () => {
      const { result } = renderHook(() => useCommunity());

      await waitFor(() => {
        expect(mockCommunityApi.getPosts).toHaveBeenCalledTimes(1);
      });

      await waitFor(() => {
        expect(result.current.posts).toEqual(mockPosts);
        expect(result.current.isLoading).toBe(false);
      });
    });

    it('should load stats on mount', async () => {
      const { result } = renderHook(() => useCommunity());

      await waitFor(() => {
        expect(mockCommunityApi.getStats).toHaveBeenCalledTimes(1);
      });

      await waitFor(() => {
        expect(result.current.stats).toEqual(mockStats);
        expect(result.current.connectionStatus).toBe('connected');
      });
    });

    it('should handle loading errors gracefully', async () => {
      const error = new Error('Network error');
      mockCommunityApi.getPosts.mockRejectedValue(error);

      const { result } = renderHook(() => useCommunity());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
        expect(result.current.posts).toEqual([]);
      });
    });
  });

  describe('Caching Mechanism', () => {
    it('should not reload posts if cache is valid', async () => {
      const { result } = renderHook(() => useCommunity());

      // Wait for initial load
      await waitFor(() => {
        expect(result.current.posts).toEqual(mockPosts);
      });

      // Clear the mock call count
      mockCommunityApi.getPosts.mockClear();

      // Call loadPosts again without force refresh
      await act(async () => {
        await result.current.loadPosts();
      });

      // Should not make another API call due to caching
      expect(mockCommunityApi.getPosts).not.toHaveBeenCalled();
    });

    it('should reload posts when force refresh is true', async () => {
      const { result } = renderHook(() => useCommunity());

      // Wait for initial load
      await waitFor(() => {
        expect(result.current.posts).toEqual(mockPosts);
      });

      // Clear the mock call count
      mockCommunityApi.getPosts.mockClear();

      // Call loadPosts with force refresh
      await act(async () => {
        await result.current.loadPosts(true);
      });

      // Should make another API call
      expect(mockCommunityApi.getPosts).toHaveBeenCalledTimes(1);
    });

    it('should cache stats and avoid duplicate calls', async () => {
      const { result } = renderHook(() => useCommunity());

      // Wait for initial load
      await waitFor(() => {
        expect(result.current.stats).toEqual(mockStats);
      });

      // Clear the mock call count
      mockCommunityApi.getStats.mockClear();

      // Call loadStats again without force refresh
      await act(async () => {
        await result.current.loadStats();
      });

      // Should not make another API call due to caching
      expect(mockCommunityApi.getStats).not.toHaveBeenCalled();
    });
  });

  describe('Search Functionality', () => {
    it('should perform search when query is provided', async () => {
      const searchResults = [mockPosts[0]];
      mockCommunityApi.searchPosts.mockResolvedValue(searchResults);

      const { result } = renderHook(() => useCommunity());

      await act(async () => {
        await result.current.handleSearch('test query');
      });

      expect(mockCommunityApi.searchPosts).toHaveBeenCalledWith('test query');
      expect(result.current.searchResults).toEqual(searchResults);
      expect(result.current.searchQuery).toBe('test query');
    });

    it('should clear search results when query is empty', async () => {
      const { result } = renderHook(() => useCommunity());

      // First set a search query
      await act(async () => {
        await result.current.handleSearch('test');
      });

      // Then clear it
      await act(async () => {
        await result.current.handleSearch('');
      });

      expect(result.current.searchResults).toEqual([]);
      expect(result.current.searchQuery).toBe('');
    });

    it('should handle search errors', async () => {
      const error = new Error('Search failed');
      mockCommunityApi.searchPosts.mockRejectedValue(error);

      const { result } = renderHook(() => useCommunity());

      await act(async () => {
        await result.current.handleSearch('test query');
      });

      expect(result.current.isSearching).toBe(false);
      expect(mockErrorHandler.parseError).toHaveBeenCalledWith(error, 'search_posts');
    });
  });

  describe('Post Interactions', () => {
    it('should handle liking a post', async () => {
      mockCommunityApi.likePost.mockResolvedValue({});

      const { result } = renderHook(() => useCommunity());

      // Wait for posts to load
      await waitFor(() => {
        expect(result.current.posts).toEqual(mockPosts);
      });

      await act(async () => {
        await result.current.handleLikePost('1');
      });

      expect(mockCommunityApi.likePost).toHaveBeenCalledWith('1');
      
      // Check that the post's like status was updated
      const updatedPost = result.current.posts.find(p => p.id === '1');
      expect(updatedPost?.is_liked).toBe(true);
      expect(updatedPost?.likes_count).toBe(6); // Original 5 + 1
    });

    it('should handle creating a post', async () => {
      const newPost = {
        id: '3',
        title: 'New Post',
        content: 'New content',
        author: { id: 1, name: 'Test User' },
        created_at: '2024-01-03T00:00:00Z',
        likes_count: 0,
        comments_count: 0,
        is_liked: false,
        is_saved: false,
      };

      mockCommunityApi.createPost.mockResolvedValue(newPost);

      const { result } = renderHook(() => useCommunity());

      // Wait for initial posts to load
      await waitFor(() => {
        expect(result.current.posts).toEqual(mockPosts);
      });

      const postData = {
        title: 'New Post',
        content: 'New content',
        hashtags: ['test'],
      };

      await act(async () => {
        await result.current.handleCreatePost(postData);
      });

      expect(mockCommunityApi.createPost).toHaveBeenCalledWith({
        title: 'New Post',
        content: 'New content',
        hashtags: ['test'],
      });

      // Check that the new post was added to the beginning of the list
      expect(result.current.posts[0]).toEqual(newPost);
      expect(result.current.posts).toHaveLength(3);
    });
  });

  describe('Connection Status', () => {
    it('should set connection status to connected when stats load successfully', async () => {
      const { result } = renderHook(() => useCommunity());

      await waitFor(() => {
        expect(result.current.connectionStatus).toBe('connected');
      });
    });

    it('should set connection status to disconnected when stats fail to load', async () => {
      mockCommunityApi.getStats.mockRejectedValue(new Error('Network error'));

      const { result } = renderHook(() => useCommunity());

      await waitFor(() => {
        expect(result.current.connectionStatus).toBe('disconnected');
      });
    });

    it('should check connection when disconnected', async () => {
      mockCommunityApi.getStats.mockRejectedValue(new Error('Network error'));
      mockCommunityApi.ping.mockResolvedValue({ status: 'ok' });

      const { result } = renderHook(() => useCommunity());

      // Wait for initial failure
      await waitFor(() => {
        expect(result.current.connectionStatus).toBe('disconnected');
      });

      // Manually trigger connection check
      await act(async () => {
        await result.current.checkConnection();
      });

      expect(mockCommunityApi.ping).toHaveBeenCalled();
      expect(result.current.connectionStatus).toBe('connected');
    });
  });

  describe('Performance Optimizations', () => {
    it('should implement intelligent polling with visibility changes', async () => {
      const { result } = renderHook(() => useCommunity());

      // Wait for initial load
      await waitFor(() => {
        expect(result.current.posts).toEqual(mockPosts);
      });

      // Simulate page becoming hidden
      Object.defineProperty(document, 'hidden', {
        writable: true,
        value: true,
      });

      // Trigger visibility change event
      act(() => {
        document.dispatchEvent(new Event('visibilitychange'));
      });

      // Clear mock calls
      mockCommunityApi.getStats.mockClear();

      // Fast forward time to trigger polling
      act(() => {
        vi.advanceTimersByTime(300000); // 5 minutes
      });

      // Should not have called getStats because page is hidden
      expect(mockCommunityApi.getStats).not.toHaveBeenCalled();
    });

    it('should use exponential backoff for connection retries', async () => {
      mockCommunityApi.ping.mockRejectedValue(new Error('Connection failed'));

      const { result } = renderHook(() => useCommunity());

      // Set connection status to disconnected
      await act(async () => {
        result.current.connectionStatus = 'disconnected';
      });

      // Fast forward through multiple retry attempts
      act(() => {
        vi.advanceTimersByTime(30000); // First retry after 30s
      });

      act(() => {
        vi.advanceTimersByTime(60000); // Second retry after 60s
      });

      act(() => {
        vi.advanceTimersByTime(120000); // Third retry after 120s
      });

      // Should have attempted multiple retries with exponential backoff
      expect(mockCommunityApi.ping).toHaveBeenCalledTimes(3);
    });
  });

  describe('Error Handling', () => {
    it('should use retry mechanism for failed operations', async () => {
      const error = new Error('Temporary failure');
      mockCommunityApi.getPosts
        .mockRejectedValueOnce(error)
        .mockRejectedValueOnce(error)
        .mockResolvedValue(mockPosts);

      mockErrorHandler.withRetry.mockImplementation(async (operation, options, context) => {
        // Simulate retry logic
        let attempts = 0;
        const maxRetries = options?.maxRetries || 2;
        
        while (attempts <= maxRetries) {
          try {
            return await operation();
          } catch (err) {
            attempts++;
            if (attempts > maxRetries) throw err;
          }
        }
      });

      const { result } = renderHook(() => useCommunity());

      await waitFor(() => {
        expect(result.current.posts).toEqual(mockPosts);
      });

      // Should have retried the operation
      expect(mockCommunityApi.getPosts).toHaveBeenCalledTimes(3);
    });

    it('should handle different error types appropriately', async () => {
      const networkError = new Error('Network error');
      mockCommunityApi.getPosts.mockRejectedValue(networkError);

      const { result } = renderHook(() => useCommunity());

      await waitFor(() => {
        expect(mockErrorHandler.parseError).toHaveBeenCalledWith(networkError, 'load_posts');
      });
    });
  });
});
