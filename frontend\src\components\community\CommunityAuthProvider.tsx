import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useToast } from '../../hooks/useToast';
import { useTranslation } from 'react-i18next';

// Community-specific auth context
interface CommunityAuthContextType {
  isAuthenticated: boolean;
  user: any;
  canCreatePost: boolean;
  canComment: boolean;
  canLike: boolean;
  canFollow: boolean;
  showAuthPrompt: (action: string) => void;
}

const CommunityAuthContext = createContext<CommunityAuthContextType | undefined>(undefined);

interface CommunityAuthProviderProps {
  children: ReactNode;
}

export const CommunityAuthProvider: React.FC<CommunityAuthProviderProps> = ({ children }) => {
  const { isAuthenticated, user } = useAuth();
  const { showInfo } = useToast();
  const { t } = useTranslation();

  // Community-specific permission checks
  const canCreatePost = isAuthenticated;
  const canComment = isAuthenticated;
  const canLike = isAuthenticated;
  const canFollow = isAuthenticated;

  // Show authentication prompt for specific actions
  const showAuthPrompt = (action: string) => {
    const messages = {
      post: t('community.auth.loginToPost', 'Please log in to create posts'),
      comment: t('community.auth.loginToComment', 'Please log in to comment'),
      like: t('community.auth.loginToLike', 'Please log in to like posts'),
      follow: t('community.auth.loginToFollow', 'Please log in to follow users'),
      save: t('community.auth.loginToSave', 'Please log in to save posts'),
    };

    showInfo(messages[action as keyof typeof messages] || t('community.auth.loginRequired', 'Please log in to continue'));
  };

  // Log authentication state changes for debugging
  useEffect(() => {
    console.log('🔐 Community Auth State:', { 
      isAuthenticated, 
      userId: user?.id, 
      username: user?.username 
    });
  }, [isAuthenticated, user]);

  const contextValue: CommunityAuthContextType = {
    isAuthenticated,
    user,
    canCreatePost,
    canComment,
    canLike,
    canFollow,
    showAuthPrompt,
  };

  return (
    <CommunityAuthContext.Provider value={contextValue}>
      {children}
    </CommunityAuthContext.Provider>
  );
};

// Hook to use community auth context
export const useCommunityAuth = (): CommunityAuthContextType => {
  const context = useContext(CommunityAuthContext);
  if (context === undefined) {
    throw new Error('useCommunityAuth must be used within a CommunityAuthProvider');
  }
  return context;
};

export default CommunityAuthProvider;
