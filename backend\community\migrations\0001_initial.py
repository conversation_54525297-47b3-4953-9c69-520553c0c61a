# Generated by Django 5.2.1 on 2025-07-29 09:09

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("api", "0008_post_allow_comments_post_category_post_excerpt_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="CommunityStats",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("total_posts", models.PositiveIntegerField(default=0)),
                ("total_users", models.PositiveIntegerField(default=0)),
                ("total_likes", models.PositiveIntegerField(default=0)),
                ("total_comments", models.PositiveIntegerField(default=0)),
                ("posts_today", models.PositiveIntegerField(default=0)),
                ("active_users_today", models.PositiveIntegerField(default=0)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Community Statistics",
                "verbose_name_plural": "Community Statistics",
            },
        ),
        migrations.CreateModel(
            name="CommunityPost",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(blank=True, max_length=200)),
                ("content", models.TextField(max_length=2000)),
                (
                    "media",
                    models.JSONField(
                        blank=True, default=list, help_text="List of media URLs/paths"
                    ),
                ),
                ("shares_count", models.PositiveIntegerField(default=0)),
                ("comments_count", models.PositiveIntegerField(default=0)),
                (
                    "visibility",
                    models.CharField(
                        choices=[
                            ("public", "Public"),
                            ("followers", "Followers Only"),
                            ("private", "Private"),
                        ],
                        default="public",
                        max_length=20,
                    ),
                ),
                ("allow_comments", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "author",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="community_posts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "likes",
                    models.ManyToManyField(
                        blank=True,
                        related_name="liked_community_posts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "tags",
                    models.ManyToManyField(
                        blank=True, related_name="community_posts", to="api.tag"
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="CommunityComment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("content", models.TextField(max_length=1000)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "author",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="community_comments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "likes",
                    models.ManyToManyField(
                        blank=True,
                        related_name="liked_community_comments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="replies",
                        to="community.communitycomment",
                    ),
                ),
                (
                    "post",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="comments",
                        to="community.communitypost",
                    ),
                ),
            ],
            options={
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="OnlineUser",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("last_seen", models.DateTimeField(auto_now=True)),
                ("is_online", models.BooleanField(default=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="online_status",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-last_seen"],
            },
        ),
        migrations.CreateModel(
            name="PostSave",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("saved_at", models.DateTimeField(auto_now_add=True)),
                (
                    "post",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="community.communitypost",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-saved_at"],
                "unique_together": {("user", "post")},
            },
        ),
        migrations.AddField(
            model_name="communitypost",
            name="saves",
            field=models.ManyToManyField(
                blank=True,
                related_name="saved_community_posts",
                through="community.PostSave",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.CreateModel(
            name="PostShare",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("shared_at", models.DateTimeField(auto_now_add=True)),
                ("platform", models.CharField(blank=True, max_length=50)),
                (
                    "post",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="community.communitypost",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-shared_at"],
            },
        ),
        migrations.CreateModel(
            name="UserActivity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "activity_type",
                    models.CharField(
                        choices=[
                            ("post_created", "Post Created"),
                            ("post_liked", "Post Liked"),
                            ("post_shared", "Post Shared"),
                            ("post_commented", "Post Commented"),
                            ("user_followed", "User Followed"),
                        ],
                        max_length=20,
                    ),
                ),
                ("content_type", models.CharField(max_length=50)),
                ("object_id", models.PositiveIntegerField()),
                ("data", models.JSONField(blank=True, default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="activities",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="UserFollow",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("followed_at", models.DateTimeField(auto_now_add=True)),
                (
                    "follower",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="following",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "following",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="followers",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-followed_at"],
            },
        ),
        migrations.AddIndex(
            model_name="communitypost",
            index=models.Index(
                fields=["-created_at"], name="community_c_created_515528_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="communitypost",
            index=models.Index(
                fields=["author", "-created_at"], name="community_c_author__8994e4_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="communitypost",
            index=models.Index(
                fields=["visibility", "-created_at"],
                name="community_c_visibil_f7b0a5_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="useractivity",
            index=models.Index(
                fields=["user", "-created_at"], name="community_u_user_id_3e9b7b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="useractivity",
            index=models.Index(
                fields=["activity_type", "-created_at"],
                name="community_u_activit_942290_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="userfollow",
            unique_together={("follower", "following")},
        ),
    ]
