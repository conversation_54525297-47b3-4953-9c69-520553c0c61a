"""
🎯 CONSOLIDATED AI MANAGEMENT COMMAND
Combines functionality from:
- init_ai_config.py
- setup_ai.py  
- update_api_key.py
- check_ai_status.py

Single command for all AI configuration and management tasks.
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import User
from core.models import AIConfiguration
from core.ai_config import get_ai_config, get_gemini_config
from core.ai_config_helper import AIConfigurationHelper
from core.services.consolidated_ai_service import ConsolidatedAIService

# Create compatibility functions
def get_ai_service():
    return ConsolidatedAIService()

def ai_get_status():
    return ConsolidatedAIService().get_status()

def ai_chat(message: str, language: str = 'en', user_id: int = None, context: dict = None, chat_type: str = 'general'):
    return ConsolidatedAIService().chat(message, language, user_id, context, chat_type)
from core.langgraph_ai_service import LangGraphAIService
import os


class Command(BaseCommand):
    help = '🎯 Consolidated AI Management - setup, configure, test, and manage AI services'

    def add_arguments(self, parser):
        # Main actions
        parser.add_argument(
            '--init',
            action='store_true',
            help='Initialize AI configuration in database'
        )
        parser.add_argument(
            '--setup',
            type=str,
            metavar='API_KEY',
            help='Set up AI configuration with provided API key'
        )
        parser.add_argument(
            '--update-key',
            type=str,
            metavar='API_KEY',
            help='Update Gemini API key'
        )
        parser.add_argument(
            '--check',
            action='store_true',
            help='Check AI service status and configuration'
        )
        parser.add_argument(
            '--test',
            action='store_true',
            help='Test AI service with sample chat'
        )
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset AI service configuration and reload'
        )
        parser.add_argument(
            '--validate',
            action='store_true',
            help='Validate current AI configuration'
        )
        parser.add_argument(
            '--instructions',
            action='store_true',
            help='Show setup instructions'
        )
        
        # Options
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force update existing configuration'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🎯 AI MANAGEMENT CONSOLE'))
        self.stdout.write('=' * 60)
        
        # Execute based on provided options
        if options['instructions']:
            self.show_instructions()
        elif options['init']:
            self.init_ai_config(options.get('force', False))
        elif options['setup']:
            self.setup_ai_config(options['setup'])
        elif options['update_key']:
            self.update_api_key(options['update_key'])
        elif options['check']:
            self.check_ai_status()
        elif options['test']:
            self.test_ai_service()
        elif options['reset']:
            self.reset_ai_service()
        elif options['validate']:
            self.validate_configuration()
        else:
            self.show_help_menu()

    def show_help_menu(self):
        """Show available commands"""
        self.stdout.write(self.style.WARNING('Available AI Management Commands:'))
        self.stdout.write('')
        self.stdout.write('  --init              Initialize AI configuration')
        self.stdout.write('  --setup API_KEY     Set up with new API key')
        self.stdout.write('  --update-key KEY    Update existing API key')
        self.stdout.write('  --check             Check service status')
        self.stdout.write('  --test              Test AI with sample chat')
        self.stdout.write('  --reset             Reset configuration')
        self.stdout.write('  --validate          Validate configuration')
        self.stdout.write('  --instructions      Show setup instructions')
        self.stdout.write('')
        self.stdout.write('Options:')
        self.stdout.write('  --force             Force update existing config')

    def init_ai_config(self, force=False):
        """Initialize AI configuration (from init_ai_config.py)"""
        self.stdout.write('🚀 Initializing AI Configuration...')

        # Get or create admin user
        try:
            admin_user = User.objects.filter(is_superuser=True).first()
            if not admin_user:
                admin_user = User.objects.create_superuser(
                    username='admin',
                    email='<EMAIL>',
                    password='admin123'
                )
                self.stdout.write(self.style.SUCCESS('✅ Created admin user'))
        except Exception as e:
            self.stdout.write(self.style.WARNING(f'⚠️ Admin user setup: {e}'))
            admin_user = None

        # Initialize AI configuration
        api_key = os.getenv('GEMINI_API_KEY', 'AIzaSyBLcSmyOVNJyKq_X6T3MOjio6XmZLliX5s')
        
        try:
            config, created = AIConfiguration.objects.get_or_create(
                provider='gemini',
                key='api_key',
                defaults={
                    'value': api_key,
                    'config_type': 'api_key',
                    'is_sensitive': True,
                    'is_active': True,
                    'description': 'Google Gemini API Key for AI services',
                    'created_by': admin_user,
                    'updated_by': admin_user
                }
            )

            if created:
                self.stdout.write(self.style.SUCCESS('✅ Created AI configuration'))
            elif force:
                config.value = api_key
                config.is_active = True
                config.save()
                self.stdout.write(self.style.SUCCESS('✅ Updated AI configuration'))
            else:
                self.stdout.write(self.style.WARNING('⚠️ Configuration exists (use --force to update)'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Configuration failed: {e}'))
            return

        # Test the configuration
        self.stdout.write('🧪 Testing AI configuration...')
        try:
            status = ai_get_status()
            if status.get('available'):
                self.stdout.write(self.style.SUCCESS('✅ AI service is available'))
            else:
                self.stdout.write(self.style.ERROR('❌ AI service not available'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ AI test failed: {e}'))

        self.stdout.write(self.style.SUCCESS('🎉 AI Configuration initialization complete!'))

    def setup_ai_config(self, api_key):
        """Set up AI configuration with provided key (from setup_ai.py)"""
        self.stdout.write(f'🔧 Setting up AI with key: {api_key[:10]}...{api_key[-4:]}')
        
        try:
            helper = AIConfigurationHelper()
            result = helper.setup_configuration(api_key)
            
            if result['success']:
                self.stdout.write(self.style.SUCCESS('✅ AI configuration setup successful'))
                self.check_ai_status()
            else:
                self.stdout.write(self.style.ERROR(f'❌ Setup failed: {result.get("message", "Unknown error")}'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Setup error: {e}'))

    def update_api_key(self, api_key):
        """Update Gemini API key (from update_api_key.py)"""
        self.stdout.write(f'🔄 Updating API key: {api_key[:10]}...{api_key[-4:]}')
        
        try:
            config, created = AIConfiguration.objects.get_or_create(
                provider='gemini',
                key='api_key',
                defaults={
                    'value': api_key.strip(),
                    'config_type': 'api_key',
                    'is_sensitive': True,
                    'is_active': True,
                    'description': 'Google Gemini API Key for AI services'
                }
            )

            if not created:
                old_value = config.value
                config.value = api_key.strip()
                config.is_active = True
                config.save()
                self.stdout.write(self.style.SUCCESS('✅ Updated existing API key'))
                self.stdout.write(f'   Old: {old_value[:10]}...{old_value[-4:] if len(old_value) > 4 else old_value}')
                self.stdout.write(f'   New: {api_key[:10]}...{api_key[-4:]}')
            else:
                self.stdout.write(self.style.SUCCESS('✅ Created new API key configuration'))

            # Test the new key
            self.stdout.write('🧪 Testing new API key...')
            self.check_ai_status()
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Update failed: {e}'))

    def check_ai_status(self):
        """Check AI service status (from check_ai_status.py)"""
        self.stdout.write("🔍 CHECKING AI SERVICE STATUS")
        self.stdout.write("=" * 50)

        try:
            # Check AI config
            config = get_ai_config()
            self.stdout.write(f"✅ AI Config loaded: {bool(config)}")

            # Check LangGraph service
            self.stdout.write("\n🔍 LANGGRAPH SERVICE:")
            langgraph_service = LangGraphAIService()
            is_available = langgraph_service.is_available()
            self.stdout.write(f"   Available: {is_available}")
            self.stdout.write(f"   LLM initialized: {langgraph_service.llm is not None}")
            self.stdout.write(f"   Workflow initialized: {langgraph_service.workflow is not None}")

            # Check main AI service
            self.stdout.write("\n🔍 MAIN AI SERVICE:")
            ai_service = get_ai_service()
            status = ai_service.get_status()

            self.stdout.write(f"   Available: {status.get('available', False)}")
            self.stdout.write(f"   Service: {status.get('service', 'unknown')}")
            self.stdout.write(f"   Features: {list(status.get('features', {}).keys())}")

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Status check failed: {e}"))

    def test_ai_service(self):
        """Test AI service with sample chat"""
        self.stdout.write("🧪 TESTING AI SERVICE")
        self.stdout.write("=" * 50)

        try:
            ai_service = get_ai_service()

            # Test simple chat
            self.stdout.write("Testing simple chat...")
            response = ai_service.chat(
                message="مرحبا",
                language='ar',
                user_id=999,
                context={'user_name': 'تست', 'user_region': 'damascus_dialect'},
                chat_type='general'
            )

            self.stdout.write(f"   Success: {response.get('success', False)}")
            self.stdout.write(f"   Service: {response.get('service', 'unknown')}")
            self.stdout.write(f"   Error: {response.get('error', 'none')}")

            if response.get('response'):
                preview = response['response'][:100] + "..." if len(response['response']) > 100 else response['response']
                self.stdout.write(f"   Response preview: {preview}")

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Test failed: {e}"))

    def reset_ai_service(self):
        """Reset AI service configuration"""
        self.stdout.write('🔄 Resetting AI service configuration...')

        try:
            # Reset global instances
            import core.ai_config
            import core.ai_service

            core.ai_config._gemini_config = None
            core.ai_service._ai_service_instance = None

            self.stdout.write(self.style.SUCCESS('✅ AI service configuration reset'))
            self.stdout.write('🔄 Testing new configuration...')

            # Test the new configuration
            status = ai_get_status()
            self.stdout.write(f"📊 AI Status: {status.get('available', False)}")

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Reset failed: {e}'))

    def validate_configuration(self):
        """Validate current AI configuration"""
        self.stdout.write('🔍 Validating AI Configuration...')

        try:
            helper = AIConfigurationHelper()
            status = helper.get_configuration_status()

            self.stdout.write(f"Gemini Configured: {status['gemini_configured']}")
            self.stdout.write(f"API Key Source: {status['api_key_source']}")
            self.stdout.write(f"Database Config: {status['database_config_exists']}")
            self.stdout.write(f"Environment Config: {status['environment_config_exists']}")

            if status['recommendations']:
                self.stdout.write('\nRecommendations:')
                for rec in status['recommendations']:
                    style = self.style.ERROR if rec['type'] == 'error' else self.style.WARNING
                    self.stdout.write(style(f"  {rec['message']}"))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Validation failed: {e}'))

    def show_instructions(self):
        """Show setup instructions"""
        self.stdout.write(self.style.SUCCESS('🎯 AI SETUP INSTRUCTIONS'))
        self.stdout.write('=' * 60)
        self.stdout.write('')
        self.stdout.write('1. Get a Gemini API key from Google AI Studio:')
        self.stdout.write('   https://makersuite.google.com/app/apikey')
        self.stdout.write('')
        self.stdout.write('2. Set up the API key using one of these methods:')
        self.stdout.write('')
        self.stdout.write('   Method A - Environment Variable:')
        self.stdout.write('   export GEMINI_API_KEY="your-api-key-here"')
        self.stdout.write('')
        self.stdout.write('   Method B - Management Command:')
        self.stdout.write('   python manage.py ai_manager --setup "your-api-key-here"')
        self.stdout.write('')
        self.stdout.write('3. Test the configuration:')
        self.stdout.write('   python manage.py ai_manager --check')
        self.stdout.write('')
        self.stdout.write('4. Run a test chat:')
        self.stdout.write('   python manage.py ai_manager --test')
        self.stdout.write('')
        self.stdout.write(self.style.SUCCESS('🎉 You\'re ready to use Yasmeen AI!'))
