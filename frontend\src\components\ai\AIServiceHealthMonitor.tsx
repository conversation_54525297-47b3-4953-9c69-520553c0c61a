/**
 * 🎯 AI SERVICE HEALTH MONITOR
 * Real-time monitoring and diagnostics for AI service reliability
 */

import React, { useState, useEffect, useCallback } from 'react';
import { unifiedAIService } from '../../services/unifiedAIService';
import { useUnifiedRoles } from '../../hooks/useUnifiedRoles';
import { StandardCard, StandardButton, StandardBadge } from '../ui/StandardizedComponents';
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  ArrowPathIcon,
  ChartBarIcon,
  ClockIcon,
  CpuChipIcon,
  SignalIcon
} from '@heroicons/react/24/outline';

interface AIServiceHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  activeRequests: number;
  rateLimitInfo: {
    remaining: number;
    limit: number;
    resetTime: number;
  };
  errorRate: number;
  lastCheck: string;
  uptime: number;
  details: {
    apiEndpoint: boolean;
    authentication: boolean;
    rateLimit: boolean;
    errorHandling: boolean;
  };
}

interface AIMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  peakResponseTime: number;
  requestsPerMinute: number;
  errorTypes: Record<string, number>;
}

export const AIServiceHealthMonitor: React.FC = () => {
  const [health, setHealth] = useState<AIServiceHealth | null>(null);
  const [metrics, setMetrics] = useState<AIMetrics | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30000); // 30 seconds

  const { canAccess } = useUnifiedRoles();

  // Check if user can access AI monitoring
  const canAccessMonitoring = canAccess('canAccessSystemSettings');

  useEffect(() => {
    if (canAccessMonitoring) {
      checkAIHealth();
    }
  }, [canAccessMonitoring]);

  useEffect(() => {
    if (autoRefresh && canAccessMonitoring) {
      const interval = setInterval(checkAIHealth, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, canAccessMonitoring]);

  const checkAIHealth = useCallback(async () => {
    if (!canAccessMonitoring) return;

    setIsChecking(true);
    try {
      // Check AI service health
      const healthResult = await unifiedAIService.healthCheck();
      
      // Get rate limit info (mock for now - would come from service)
      const rateLimitInfo = unifiedAIService.getRateLimitInfo('current_user', 'admin');
      
      // Get active requests count
      const activeRequests = unifiedAIService.getActiveRequestsCount();

      // Calculate error rate (mock calculation)
      const errorRate = Math.random() * 5; // 0-5% error rate

      const healthData: AIServiceHealth = {
        status: healthResult.status,
        responseTime: healthResult.details.responseTime || 0,
        activeRequests,
        rateLimitInfo: {
          remaining: rateLimitInfo.remaining,
          limit: rateLimitInfo.limit,
          resetTime: rateLimitInfo.resetTime
        },
        errorRate,
        lastCheck: new Date().toISOString(),
        uptime: 99.5, // Mock uptime percentage
        details: {
          apiEndpoint: healthResult.status !== 'unhealthy',
          authentication: true,
          rateLimit: rateLimitInfo.remaining > 0,
          errorHandling: true
        }
      };

      setHealth(healthData);

      // Update metrics (mock data - would come from analytics service)
      const metricsData: AIMetrics = {
        totalRequests: 1250,
        successfulRequests: 1187,
        failedRequests: 63,
        averageResponseTime: healthData.responseTime,
        peakResponseTime: Math.max(healthData.responseTime * 2, 5000),
        requestsPerMinute: 12,
        errorTypes: {
          'rate_limit': 25,
          'timeout': 18,
          'invalid_request': 12,
          'service_unavailable': 8
        }
      };

      setMetrics(metricsData);

    } catch (error) {
      console.error('AI health check failed:', error);
      
      const errorHealth: AIServiceHealth = {
        status: 'unhealthy',
        responseTime: 0,
        activeRequests: 0,
        rateLimitInfo: { remaining: 0, limit: 0, resetTime: 0 },
        errorRate: 100,
        lastCheck: new Date().toISOString(),
        uptime: 0,
        details: {
          apiEndpoint: false,
          authentication: false,
          rateLimit: false,
          errorHandling: false
        }
      };

      setHealth(errorHealth);
    } finally {
      setIsChecking(false);
    }
  }, [canAccessMonitoring]);

  const getStatusIcon = (status: AIServiceHealth['status']) => {
    switch (status) {
      case 'healthy':
        return <CheckCircleIcon className="w-6 h-6 text-green-500" />;
      case 'degraded':
        return <ExclamationTriangleIcon className="w-6 h-6 text-yellow-500" />;
      case 'unhealthy':
        return <XCircleIcon className="w-6 h-6 text-red-500" />;
      default:
        return <ClockIcon className="w-6 h-6 text-gray-500" />;
    }
  };

  const getStatusColor = (status: AIServiceHealth['status']) => {
    switch (status) {
      case 'healthy': return 'text-green-400';
      case 'degraded': return 'text-yellow-400';
      case 'unhealthy': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const formatUptime = (uptime: number) => {
    return `${uptime.toFixed(2)}%`;
  };

  const formatResponseTime = (time: number) => {
    return `${time}ms`;
  };

  if (!canAccessMonitoring) {
    return (
      <StandardCard variant="glass" padding="lg">
        <div className="text-center">
          <XCircleIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">Access Denied</h3>
          <p className="text-white/70">You don't have permission to access AI service monitoring.</p>
        </div>
      </StandardCard>
    );
  }

  if (!health) {
    return (
      <StandardCard variant="glass" padding="lg">
        <div className="text-center">
          <ArrowPathIcon className="w-8 h-8 text-blue-400 mx-auto mb-4 animate-spin" />
          <p className="text-white/70">Loading AI service health...</p>
        </div>
      </StandardCard>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">AI Service Health Monitor</h2>
          <p className="text-white/70 mt-1">Real-time monitoring and diagnostics</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <StandardButton
            variant="ghost"
            size="sm"
            onClick={checkAIHealth}
            isLoading={isChecking}
            leftIcon={<ArrowPathIcon className="w-4 h-4" />}
          >
            Refresh
          </StandardButton>
          
          <StandardButton
            variant={autoRefresh ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            Auto Refresh
          </StandardButton>
        </div>
      </div>

      {/* Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StandardCard variant="glass" padding="lg">
          <div className="flex items-center space-x-3">
            {getStatusIcon(health.status)}
            <div>
              <p className="text-white/70 text-sm">Service Status</p>
              <p className={`font-semibold capitalize ${getStatusColor(health.status)}`}>
                {health.status}
              </p>
            </div>
          </div>
        </StandardCard>

        <StandardCard variant="glass" padding="lg">
          <div className="flex items-center space-x-3">
            <ClockIcon className="w-6 h-6 text-blue-400" />
            <div>
              <p className="text-white/70 text-sm">Response Time</p>
              <p className="font-semibold text-white">
                {formatResponseTime(health.responseTime)}
              </p>
            </div>
          </div>
        </StandardCard>

        <StandardCard variant="glass" padding="lg">
          <div className="flex items-center space-x-3">
            <CpuChipIcon className="w-6 h-6 text-purple-400" />
            <div>
              <p className="text-white/70 text-sm">Active Requests</p>
              <p className="font-semibold text-white">{health.activeRequests}</p>
            </div>
          </div>
        </StandardCard>

        <StandardCard variant="glass" padding="lg">
          <div className="flex items-center space-x-3">
            <SignalIcon className="w-6 h-6 text-green-400" />
            <div>
              <p className="text-white/70 text-sm">Uptime</p>
              <p className="font-semibold text-white">{formatUptime(health.uptime)}</p>
            </div>
          </div>
        </StandardCard>
      </div>

      {/* Detailed Health Status */}
      <StandardCard variant="glass" padding="lg">
        <h3 className="text-lg font-semibold text-white mb-4">Service Components</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(health.details).map(([component, status]) => (
            <div key={component} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
              <span className="text-white capitalize">
                {component.replace(/([A-Z])/g, ' $1').trim()}
              </span>
              <StandardBadge variant={status ? 'success' : 'error'}>
                {status ? 'Healthy' : 'Failed'}
              </StandardBadge>
            </div>
          ))}
        </div>
      </StandardCard>

      {/* Rate Limiting Info */}
      <StandardCard variant="glass" padding="lg">
        <h3 className="text-lg font-semibold text-white mb-4">Rate Limiting</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-white/70">Remaining Requests</span>
            <span className="text-white font-medium">
              {health.rateLimitInfo.remaining} / {health.rateLimitInfo.limit}
            </span>
          </div>
          
          <div className="w-full bg-white/10 rounded-full h-2">
            <div 
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ 
                width: `${(health.rateLimitInfo.remaining / health.rateLimitInfo.limit) * 100}%` 
              }}
            />
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <span className="text-white/70">Reset Time</span>
            <span className="text-white/70">
              {new Date(health.rateLimitInfo.resetTime).toLocaleTimeString()}
            </span>
          </div>
        </div>
      </StandardCard>

      {/* Metrics */}
      {metrics && (
        <StandardCard variant="glass" padding="lg">
          <h3 className="text-lg font-semibold text-white mb-4">Performance Metrics</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <p className="text-white/70 text-sm mb-2">Request Statistics</p>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-white/70">Total</span>
                  <span className="text-white">{metrics.totalRequests}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/70">Successful</span>
                  <span className="text-green-400">{metrics.successfulRequests}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/70">Failed</span>
                  <span className="text-red-400">{metrics.failedRequests}</span>
                </div>
              </div>
            </div>
            
            <div>
              <p className="text-white/70 text-sm mb-2">Response Times</p>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-white/70">Average</span>
                  <span className="text-white">{formatResponseTime(metrics.averageResponseTime)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/70">Peak</span>
                  <span className="text-white">{formatResponseTime(metrics.peakResponseTime)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/70">Requests/min</span>
                  <span className="text-white">{metrics.requestsPerMinute}</span>
                </div>
              </div>
            </div>
            
            <div>
              <p className="text-white/70 text-sm mb-2">Error Types</p>
              <div className="space-y-2">
                {Object.entries(metrics.errorTypes).map(([type, count]) => (
                  <div key={type} className="flex justify-between">
                    <span className="text-white/70 capitalize">
                      {type.replace(/_/g, ' ')}
                    </span>
                    <span className="text-red-400">{count}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </StandardCard>
      )}

      {/* Last Updated */}
      <div className="text-center text-white/50 text-sm">
        Last updated: {new Date(health.lastCheck).toLocaleString()}
      </div>
    </div>
  );
};

export default AIServiceHealthMonitor;
