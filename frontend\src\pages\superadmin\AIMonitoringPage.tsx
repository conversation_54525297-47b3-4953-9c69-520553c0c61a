/**
 * AI Monitoring Page - Super Admin Only
 * Comprehensive monitoring and management of AI systems
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import {
  Brain,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Zap,
  Database,
  Server,
  Eye,
  Settings,
  RefreshCw,
  BarChart3,
  Users,
  MessageSquare,
  Shield
} from 'lucide-react';

interface AISystemStatus {
  id: string;
  name: string;
  status: 'healthy' | 'warning' | 'error' | 'maintenance';
  uptime: string;
  responseTime: number;
  requestCount: number;
  errorRate: number;
  lastUpdated: string;
}

interface AIMetrics {
  totalRequests: number;
  successRate: number;
  averageResponseTime: number;
  activeUsers: number;
  systemLoad: number;
  memoryUsage: number;
}

const AIMonitoringPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [aiSystems, setAiSystems] = useState<AISystemStatus[]>([]);
  const [metrics, setMetrics] = useState<AIMetrics | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState('24h');

  useEffect(() => {
    loadAISystemData();
  }, [selectedTimeRange]);

  const loadAISystemData = async () => {
    setLoading(true);
    try {
      // Simulate API call - replace with real API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setAiSystems([
        {
          id: 'gemini-ai',
          name: 'Gemini AI Service',
          status: 'healthy',
          uptime: '99.9%',
          responseTime: 245,
          requestCount: 15420,
          errorRate: 0.1,
          lastUpdated: new Date().toISOString()
        },
        {
          id: 'business-analyzer',
          name: 'Business Plan Analyzer',
          status: 'healthy',
          uptime: '99.7%',
          responseTime: 1200,
          requestCount: 8930,
          errorRate: 0.3,
          lastUpdated: new Date().toISOString()
        },
        {
          id: 'market-predictor',
          name: 'Market Prediction Engine',
          status: 'warning',
          uptime: '98.5%',
          responseTime: 3400,
          requestCount: 5670,
          errorRate: 1.5,
          lastUpdated: new Date().toISOString()
        },
        {
          id: 'chat-assistant',
          name: 'AI Chat Assistant',
          status: 'healthy',
          uptime: '99.8%',
          responseTime: 180,
          requestCount: 23450,
          errorRate: 0.2,
          lastUpdated: new Date().toISOString()
        }
      ]);

      setMetrics({
        totalRequests: 53470,
        successRate: 98.9,
        averageResponseTime: 756,
        activeUsers: 1247,
        systemLoad: 67,
        memoryUsage: 78
      });
    } catch (error) {
      console.error('Error loading AI system data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadAISystemData();
    setRefreshing(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'error':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      case 'maintenance':
        return <Settings className="w-5 h-5 text-blue-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'maintenance':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 text-white p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
          <span className="ml-4 text-lg">{t('Loading AI monitoring data...')}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className={`flex items-center justify-between mb-8 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="w-12 h-12 bg-purple-600/30 rounded-lg flex items-center justify-center">
              <Brain className="w-6 h-6 text-purple-400" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">{t('AI System Monitoring')}</h1>
              <p className="text-purple-200">{t('Real-time monitoring of AI services and performance')}</p>
            </div>
          </div>
          
          <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <select
              value={selectedTimeRange}
              onChange={(e) => setSelectedTimeRange(e.target.value)}
              className="bg-indigo-900/50 border border-indigo-700 rounded-lg px-4 py-2 text-white"
            >
              <option value="1h">{t('Last Hour')}</option>
              <option value="24h">{t('Last 24 Hours')}</option>
              <option value="7d">{t('Last 7 Days')}</option>
              <option value="30d">{t('Last 30 Days')}</option>
            </select>
            
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
              {t('Refresh')}
            </button>
          </div>
        </div>

        {/* Metrics Overview */}
        {metrics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-purple-200 text-sm">{t('Total Requests')}</p>
                  <p className="text-2xl font-bold">{metrics.totalRequests.toLocaleString()}</p>
                </div>
                <MessageSquare className="w-8 h-8 text-purple-400" />
              </div>
            </div>

            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-purple-200 text-sm">{t('Success Rate')}</p>
                  <p className="text-2xl font-bold text-green-400">{metrics.successRate}%</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-400" />
              </div>
            </div>

            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-purple-200 text-sm">{t('Avg Response Time')}</p>
                  <p className="text-2xl font-bold">{metrics.averageResponseTime}ms</p>
                </div>
                <Clock className="w-8 h-8 text-blue-400" />
              </div>
            </div>

            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-purple-200 text-sm">{t('Active Users')}</p>
                  <p className="text-2xl font-bold">{metrics.activeUsers.toLocaleString()}</p>
                </div>
                <Users className="w-8 h-8 text-yellow-400" />
              </div>
            </div>

            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-purple-200 text-sm">{t('System Load')}</p>
                  <p className="text-2xl font-bold">{metrics.systemLoad}%</p>
                </div>
                <Server className="w-8 h-8 text-orange-400" />
              </div>
            </div>

            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-purple-200 text-sm">{t('Memory Usage')}</p>
                  <p className="text-2xl font-bold">{metrics.memoryUsage}%</p>
                </div>
                <Database className="w-8 h-8 text-red-400" />
              </div>
            </div>
          </div>
        )}

        {/* AI Systems Status */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg border border-indigo-800/50 overflow-hidden">
          <div className="p-6 border-b border-indigo-800/50">
            <h2 className="text-xl font-semibold flex items-center gap-2">
              <Shield className="w-5 h-5 text-purple-400" />
              {t('AI Systems Status')}
            </h2>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-indigo-900/50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-purple-200 uppercase tracking-wider">
                    {t('System')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-purple-200 uppercase tracking-wider">
                    {t('Status')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-purple-200 uppercase tracking-wider">
                    {t('Uptime')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-purple-200 uppercase tracking-wider">
                    {t('Response Time')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-purple-200 uppercase tracking-wider">
                    {t('Requests')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-purple-200 uppercase tracking-wider">
                    {t('Error Rate')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-purple-200 uppercase tracking-wider">
                    {t('Actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-indigo-800/50">
                {aiSystems.map((system) => (
                  <tr key={system.id} className="hover:bg-indigo-900/20">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-3">
                        <Brain className="w-5 h-5 text-purple-400" />
                        <span className="font-medium">{system.name}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(system.status)}`}>
                        {getStatusIcon(system.status)}
                        {t(system.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {system.uptime}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span className={system.responseTime > 2000 ? 'text-red-400' : system.responseTime > 1000 ? 'text-yellow-400' : 'text-green-400'}>
                        {system.responseTime}ms
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {system.requestCount.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span className={system.errorRate > 1 ? 'text-red-400' : system.errorRate > 0.5 ? 'text-yellow-400' : 'text-green-400'}>
                        {system.errorRate}%
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <div className="flex items-center gap-2">
                        <button className="text-purple-400 hover:text-purple-300">
                          <Eye className="w-4 h-4" />
                        </button>
                        <button className="text-blue-400 hover:text-blue-300">
                          <Settings className="w-4 h-4" />
                        </button>
                        <button className="text-green-400 hover:text-green-300">
                          <BarChart3 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIMonitoringPage;
