"""
Community URLs
API endpoints for community features
"""

from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from .views import (
    CommunityPostViewSet, SimpleCommunityCommentViewSet,
    UserFollowViewSet, CommunityStatsViewSet,
    HashtagViewSet, UserRecommendationViewSet, test_endpoint
)

@api_view(['GET'])
@permission_classes([AllowAny])
def ping_view(request):
    """Health check endpoint for community service"""
    return Response({'status': 'ok'})

# Create router for community endpoints
router = DefaultRouter()
router.register(r'posts', CommunityPostViewSet, basename='community-posts')
router.register(r'comments', SimpleCommunityCommentViewSet, basename='community-comments')
router.register(r'users', UserRecommendationViewSet, basename='community-users')
router.register(r'stats', CommunityStatsViewSet, basename='community-stats')
router.register(r'hashtags', HashtagViewSet, basename='community-hashtags')
router.register(r'follow', UserFollowViewSet, basename='community-follow')

urlpatterns = [
    path('ping/', ping_view, name='community-ping'),
    path('test/', test_endpoint, name='test-endpoint'),
    path('', include(router.urls)),
]
