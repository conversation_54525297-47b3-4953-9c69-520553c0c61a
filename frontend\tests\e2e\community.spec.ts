/**
 * End-to-End Tests for Community Features
 * Tests critical user flows and interactions in the community page
 */

import { test, expect, Page } from '@playwright/test';

// Test configuration
const BASE_URL = 'http://localhost:5173';
const COMMUNITY_URL = `${BASE_URL}/community`;

// Helper functions
async function waitForCommunityPageLoad(page: Page) {
  await page.waitForSelector('[data-testid="community-header"]', { timeout: 10000 });
  await page.waitForSelector('[data-testid="posts-feed"]', { timeout: 10000 });
  await page.waitForSelector('[data-testid="community-sidebar"]', { timeout: 10000 });
}

async function waitForLoadingToComplete(page: Page) {
  // Wait for loading spinners to disappear
  await page.waitForSelector('.animate-spin', { state: 'detached', timeout: 10000 });
  
  // Wait for skeleton loaders to disappear
  await page.waitForSelector('.animate-pulse', { state: 'detached', timeout: 10000 });
}

async function loginUser(page: Page, username: string = 'testuser', password: string = 'testpass') {
  await page.goto(`${BASE_URL}/login`);
  await page.fill('[data-testid="username-input"]', username);
  await page.fill('[data-testid="password-input"]', password);
  await page.click('[data-testid="login-button"]');
  await page.waitForURL(`${BASE_URL}/dashboard`, { timeout: 10000 });
}

test.describe('Community Page - Guest User Experience', () => {
  test('should load community page for guest users', async ({ page }) => {
    await page.goto(COMMUNITY_URL);
    
    // Wait for page to load
    await waitForCommunityPageLoad(page);
    
    // Check that guest banner is visible
    await expect(page.locator('[data-testid="guest-banner"]')).toBeVisible();
    
    // Check that login button is present
    await expect(page.locator('text=تسجيل الدخول')).toBeVisible();
    
    // Check that posts are visible (read-only access)
    await expect(page.locator('[data-testid="posts-feed"]')).toBeVisible();
    
    // Check that community stats are visible
    await expect(page.locator('[data-testid="community-stats"]')).toBeVisible();
  });

  test('should display connection status', async ({ page }) => {
    await page.goto(COMMUNITY_URL);
    await waitForCommunityPageLoad(page);
    
    // Check connection status indicator
    const connectionStatus = page.locator('[data-testid="connection-status"]');
    await expect(connectionStatus).toBeVisible();
    
    // Should show connected status (green indicator)
    await expect(connectionStatus.locator('text=متصل')).toBeVisible();
  });

  test('should show posts without interaction buttons for guests', async ({ page }) => {
    await page.goto(COMMUNITY_URL);
    await waitForCommunityPageLoad(page);
    await waitForLoadingToComplete(page);
    
    // Check that posts are visible
    const posts = page.locator('[data-testid="post-item"]');
    await expect(posts.first()).toBeVisible();
    
    // Check that interaction buttons are disabled for guests
    const likeButton = posts.first().locator('[data-testid="like-button"]');
    await expect(likeButton).toBeDisabled();
    
    const commentButton = posts.first().locator('[data-testid="comment-button"]');
    await expect(commentButton).toBeDisabled();
  });

  test('should allow search functionality for guests', async ({ page }) => {
    await page.goto(COMMUNITY_URL);
    await waitForCommunityPageLoad(page);
    
    // Use search functionality
    const searchInput = page.locator('[data-testid="search-input"]');
    await expect(searchInput).toBeVisible();
    
    await searchInput.fill('test');
    await page.keyboard.press('Enter');
    
    // Wait for search results
    await page.waitForSelector('[data-testid="search-results"]', { timeout: 5000 });
    
    // Check that search was performed
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible();
  });
});

test.describe('Community Page - Authenticated User Experience', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await loginUser(page);
  });

  test('should load community page for authenticated users', async ({ page }) => {
    await page.goto(COMMUNITY_URL);
    await waitForCommunityPageLoad(page);
    
    // Check that guest banner is not visible
    await expect(page.locator('[data-testid="guest-banner"]')).not.toBeVisible();
    
    // Check that user profile is visible in header
    await expect(page.locator('[data-testid="user-profile"]')).toBeVisible();
    
    // Check that create post button is enabled
    await expect(page.locator('[data-testid="create-post-button"]')).toBeEnabled();
  });

  test('should allow post creation', async ({ page }) => {
    await page.goto(COMMUNITY_URL);
    await waitForCommunityPageLoad(page);
    
    // Click create post button
    await page.click('[data-testid="create-post-button"]');
    
    // Wait for modal to open
    await expect(page.locator('[data-testid="create-post-modal"]')).toBeVisible();
    
    // Fill in post details
    await page.fill('[data-testid="post-title-input"]', 'Test Post Title');
    await page.fill('[data-testid="post-content-input"]', 'This is a test post content');
    await page.fill('[data-testid="post-hashtags-input"]', '#test #automation');
    
    // Submit post
    await page.click('[data-testid="submit-post-button"]');
    
    // Wait for modal to close and post to appear
    await expect(page.locator('[data-testid="create-post-modal"]')).not.toBeVisible();
    
    // Check that success message appears
    await expect(page.locator('text=تم إنشاء المنشور بنجاح')).toBeVisible();
    
    // Check that new post appears in feed
    await expect(page.locator('text=Test Post Title')).toBeVisible();
  });

  test('should allow post interactions', async ({ page }) => {
    await page.goto(COMMUNITY_URL);
    await waitForCommunityPageLoad(page);
    await waitForLoadingToComplete(page);
    
    const firstPost = page.locator('[data-testid="post-item"]').first();
    await expect(firstPost).toBeVisible();
    
    // Test like functionality
    const likeButton = firstPost.locator('[data-testid="like-button"]');
    await expect(likeButton).toBeEnabled();
    
    const initialLikeCount = await likeButton.locator('[data-testid="like-count"]').textContent();
    await likeButton.click();
    
    // Wait for like to be processed
    await page.waitForTimeout(1000);
    
    // Check that like count increased
    const newLikeCount = await likeButton.locator('[data-testid="like-count"]').textContent();
    expect(parseInt(newLikeCount || '0')).toBeGreaterThan(parseInt(initialLikeCount || '0'));
    
    // Test comment functionality
    const commentButton = firstPost.locator('[data-testid="comment-button"]');
    await commentButton.click();
    
    // Check that comment input appears
    await expect(firstPost.locator('[data-testid="comment-input"]')).toBeVisible();
    
    // Add a comment
    await firstPost.locator('[data-testid="comment-input"]').fill('This is a test comment');
    await firstPost.locator('[data-testid="submit-comment-button"]').click();
    
    // Wait for comment to be added
    await page.waitForTimeout(1000);
    
    // Check that comment appears
    await expect(firstPost.locator('text=This is a test comment')).toBeVisible();
  });

  test('should handle view switching', async ({ page }) => {
    await page.goto(COMMUNITY_URL);
    await waitForCommunityPageLoad(page);
    
    // Test different view tabs
    const feedTab = page.locator('[data-testid="feed-tab"]');
    const trendingTab = page.locator('[data-testid="trending-tab"]');
    const followingTab = page.locator('[data-testid="following-tab"]');
    const savedTab = page.locator('[data-testid="saved-tab"]');
    
    // Check that feed tab is active by default
    await expect(feedTab).toHaveClass(/active/);
    
    // Switch to trending
    await trendingTab.click();
    await expect(trendingTab).toHaveClass(/active/);
    await expect(feedTab).not.toHaveClass(/active/);
    
    // Switch to following
    await followingTab.click();
    await expect(followingTab).toHaveClass(/active/);
    
    // Switch to saved
    await savedTab.click();
    await expect(savedTab).toHaveClass(/active/);
  });
});

test.describe('Community Page - Performance and Accessibility', () => {
  test('should load within acceptable time limits', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto(COMMUNITY_URL);
    await waitForCommunityPageLoad(page);
    
    const loadTime = Date.now() - startTime;
    
    // Should load within 5 seconds
    expect(loadTime).toBeLessThan(5000);
  });

  test('should be accessible with keyboard navigation', async ({ page }) => {
    await page.goto(COMMUNITY_URL);
    await waitForCommunityPageLoad(page);
    
    // Test skip links
    await page.keyboard.press('Tab');
    const skipToMain = page.locator('text=انتقل إلى المحتوى الرئيسي');
    await expect(skipToMain).toBeFocused();
    
    await page.keyboard.press('Enter');
    
    // Should focus on main content
    const mainContent = page.locator('[data-testid="main-content"]');
    await expect(mainContent).toBeFocused();
  });

  test('should handle RTL layout correctly', async ({ page }) => {
    await page.goto(COMMUNITY_URL);
    await waitForCommunityPageLoad(page);
    
    // Check that page has RTL direction
    const body = page.locator('body');
    await expect(body).toHaveAttribute('dir', 'rtl');
    
    // Check that text alignment is correct for RTL
    const header = page.locator('[data-testid="community-header"]');
    const headerStyles = await header.evaluate(el => getComputedStyle(el));
    expect(headerStyles.direction).toBe('rtl');
  });

  test('should handle responsive design', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto(COMMUNITY_URL);
    await waitForCommunityPageLoad(page);
    
    // Check that mobile layout is applied
    const sidebar = page.locator('[data-testid="community-sidebar"]');
    const sidebarStyles = await sidebar.evaluate(el => getComputedStyle(el));
    
    // Sidebar should be hidden or collapsed on mobile
    expect(['none', 'hidden'].some(value => 
      sidebarStyles.display === value || sidebarStyles.visibility === value
    )).toBeTruthy();
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.reload();
    await waitForCommunityPageLoad(page);
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.reload();
    await waitForCommunityPageLoad(page);
    
    // Sidebar should be visible on desktop
    await expect(sidebar).toBeVisible();
  });
});

test.describe('Community Page - Error Handling', () => {
  test('should handle network errors gracefully', async ({ page }) => {
    // Intercept API calls and simulate network error
    await page.route('**/api/community/posts/', route => {
      route.abort('failed');
    });
    
    await page.goto(COMMUNITY_URL);
    
    // Should show error message
    await expect(page.locator('text=فشل في تحميل المنشورات')).toBeVisible();
    
    // Should show retry button
    const retryButton = page.locator('[data-testid="retry-button"]');
    await expect(retryButton).toBeVisible();
    
    // Test retry functionality
    await page.unroute('**/api/community/posts/');
    await retryButton.click();
    
    // Should load successfully after retry
    await waitForCommunityPageLoad(page);
  });

  test('should handle server errors gracefully', async ({ page }) => {
    // Intercept API calls and simulate server error
    await page.route('**/api/community/posts/', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' })
      });
    });
    
    await page.goto(COMMUNITY_URL);
    
    // Should show error message
    await expect(page.locator('text=حدث خطأ في الخادم')).toBeVisible();
    
    // Should provide error recovery options
    await expect(page.locator('[data-testid="error-recovery"]')).toBeVisible();
  });

  test('should handle authentication errors', async ({ page }) => {
    // Login first
    await loginUser(page);
    await page.goto(COMMUNITY_URL);
    await waitForCommunityPageLoad(page);
    
    // Intercept like API call and simulate auth error
    await page.route('**/api/community/posts/*/like/', route => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Authentication required' })
      });
    });
    
    // Try to like a post
    const likeButton = page.locator('[data-testid="like-button"]').first();
    await likeButton.click();
    
    // Should show authentication error
    await expect(page.locator('text=يرجى تسجيل الدخول مرة أخرى')).toBeVisible();
  });
});

test.describe('Community Page - Data Persistence', () => {
  test('should persist search query across page reloads', async ({ page }) => {
    await page.goto(COMMUNITY_URL);
    await waitForCommunityPageLoad(page);
    
    // Perform search
    const searchInput = page.locator('[data-testid="search-input"]');
    await searchInput.fill('test query');
    await page.keyboard.press('Enter');
    
    // Reload page
    await page.reload();
    await waitForCommunityPageLoad(page);
    
    // Search query should be preserved
    await expect(searchInput).toHaveValue('test query');
  });

  test('should maintain scroll position on navigation', async ({ page }) => {
    await page.goto(COMMUNITY_URL);
    await waitForCommunityPageLoad(page);
    await waitForLoadingToComplete(page);
    
    // Scroll down
    await page.evaluate(() => window.scrollTo(0, 500));
    
    // Navigate away and back
    await page.goto(`${BASE_URL}/dashboard`);
    await page.goBack();
    
    // Should maintain scroll position
    const scrollY = await page.evaluate(() => window.scrollY);
    expect(scrollY).toBeGreaterThan(400);
  });
});
