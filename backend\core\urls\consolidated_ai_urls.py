"""
🎯 CONSOLIDATED AI URLS
Single source of truth for all AI API endpoints

This file consolidates and replaces:
- core/ai_urls.py (main AI endpoints)
- core/ai/urls.py (unified AI URLs)
- ai_recommendations/urls.py (recommendation endpoints)

Features:
- Clean, consistent URL structure
- Single endpoint per functionality
- Backward compatibility where needed
- Clear naming conventions
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

# Import consolidated views
from core.views.consolidated_ai_views import (
    ConsolidatedChatView,
    ConsolidatedBusinessAnalysisView,
    ConsolidatedRecommendationsView,
    ConsolidatedMentorshipView,
    ConsolidatedIdeaBuilderView,
    ConsolidatedAIStatusView,
    ConsolidatedTextAnalysisView,
    ConsolidatedContentGenerationView,
    consolidated_ai_test
)

# Create router for any ViewSets (none currently, but ready for future)
router = DefaultRouter()

# Consolidated AI URL patterns
urlpatterns = [
    # Include router URLs (empty for now)
    path('', include(router.urls)),
    
    # ========================================
    # CORE AI ENDPOINTS
    # ========================================
    
    # Status and Health Check
    path('status/', ConsolidatedAIStatusView.as_view(), name='consolidated-ai-status'),
    path('test/', consolidated_ai_test, name='consolidated-ai-test'),
    
    # Chat and Communication
    path('chat/', ConsolidatedChatView.as_view(), name='consolidated-ai-chat'),
    
    # Business Analysis
    path('analyze-business/', ConsolidatedBusinessAnalysisView.as_view(), name='consolidated-ai-analyze-business'),
    
    # Text Analysis
    path('analyze-text/', ConsolidatedTextAnalysisView.as_view(), name='consolidated-ai-analyze-text'),
    
    # Content Generation
    path('generate-content/', ConsolidatedContentGenerationView.as_view(), name='consolidated-ai-generate-content'),
    
    # ========================================
    # BUSINESS INTELLIGENCE ENDPOINTS
    # ========================================
    
    # Business Recommendations
    path('recommendations/', ConsolidatedRecommendationsView.as_view(), name='consolidated-ai-recommendations'),
    
    # Mentorship Matching
    path('mentorship/', ConsolidatedMentorshipView.as_view(), name='consolidated-ai-mentorship'),
    
    # Idea Building and Enhancement
    path('idea-builder/', ConsolidatedIdeaBuilderView.as_view(), name='consolidated-ai-idea-builder'),
    
    # ========================================
    # BACKWARD COMPATIBILITY ALIASES
    # ========================================
    
    # Legacy chat endpoints
    path('universal-chat/', ConsolidatedChatView.as_view(), name='universal-chat-legacy'),
    path('ai-chat/', ConsolidatedChatView.as_view(), name='ai-chat-legacy'),
    
    # Legacy analysis endpoints
    path('business-analysis/', ConsolidatedBusinessAnalysisView.as_view(), name='business-analysis-legacy'),
    path('analyze/', ConsolidatedBusinessAnalysisView.as_view(), name='analyze-legacy'),
    
    # Legacy recommendation endpoints
    path('generate-recommendations/', ConsolidatedRecommendationsView.as_view(), name='generate-recommendations-legacy'),
    path('business-recommendations/', ConsolidatedRecommendationsView.as_view(), name='business-recommendations-legacy'),
]

# URL patterns for app inclusion
app_name = 'consolidated_ai'
