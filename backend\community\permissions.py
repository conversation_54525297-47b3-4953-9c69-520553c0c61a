"""
Community-specific permissions
Allows read-only access for anonymous users to public content
"""

from rest_framework import permissions


class CommunityReadOnlyOrAuthenticated(permissions.BasePermission):
    """
    Custom permission to allow:
    - Read-only access for anonymous users (GET requests only)
    - Full access for authenticated users
    """
    
    def has_permission(self, request, view):
        # Read permissions for anonymous users (GET, HEAD, OPTIONS)
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Write permissions only for authenticated users
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # Read permissions for anonymous users
        if request.method in permissions.SAFE_METHODS:
            # For anonymous users, only allow access to public posts
            if not request.user.is_authenticated:
                return getattr(obj, 'visibility', 'public') == 'public'
            return True
        
        # Write permissions only for authenticated users
        if not request.user.is_authenticated:
            return False
            
        # For authenticated users, check if they own the object or are admin
        if hasattr(obj, 'author'):
            return obj.author == request.user or request.user.is_staff
        
        return request.user.is_authenticated


class CommunityStatsReadOnly(permissions.BasePermission):
    """
    Allow read-only access to community stats for everyone
    """
    
    def has_permission(self, request, view):
        # Only allow GET requests
        return request.method in permissions.SAFE_METHODS


class AuthenticatedWriteOnly(permissions.BasePermission):
    """
    Only allow authenticated users to perform write operations
    """
    
    def has_permission(self, request, view):
        # Read operations allowed for everyone
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Write operations only for authenticated users
        return request.user and request.user.is_authenticated
