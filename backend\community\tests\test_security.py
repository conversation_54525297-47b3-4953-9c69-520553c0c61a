"""
Test Security Features for Community App
Tests content sanitization, validation, and security measures
"""

import pytest
from django.test import TestCase, RequestFactory
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from unittest.mock import patch, MagicMock

from ..security import (
    ContentSanitizer, content_sanitizer, sanitize_community_content,
    validate_community_content, SecurityEventLogger, security_logger
)
from ..models import CommunityPost, CommunityComment
from ..serializers import CommunityPostSerializer, CommunityCommentSerializer


class ContentSanitizerTest(TestCase):
    """Test content sanitization functionality"""
    
    def setUp(self):
        self.sanitizer = ContentSanitizer()
    
    def test_sanitize_html_removes_dangerous_scripts(self):
        """Test that dangerous script tags are removed"""
        dangerous_content = '<script>alert("xss")</script>Hello World'
        sanitized = self.sanitizer.sanitize_html(dangerous_content)
        
        self.assertNotIn('<script>', sanitized)
        self.assertNotIn('alert', sanitized)
        self.assertIn('Hello World', sanitized)
    
    def test_sanitize_html_removes_javascript_urls(self):
        """Test that javascript: URLs are removed"""
        dangerous_content = '<a href="javascript:alert(\'xss\')">Click me</a>'
        sanitized = self.sanitizer.sanitize_html(dangerous_content)
        
        self.assertNotIn('javascript:', sanitized)
        self.assertNotIn('alert', sanitized)
    
    def test_sanitize_html_removes_event_handlers(self):
        """Test that event handlers are removed"""
        dangerous_content = '<div onclick="alert(\'xss\')">Click me</div>'
        sanitized = self.sanitizer.sanitize_html(dangerous_content)
        
        self.assertNotIn('onclick', sanitized)
        self.assertNotIn('alert', sanitized)
    
    def test_sanitize_html_allows_safe_content(self):
        """Test that safe content is preserved"""
        safe_content = 'Hello <strong>World</strong>!\nNew line here.'
        sanitized = self.sanitizer.sanitize_html(safe_content)
        
        self.assertIn('Hello', sanitized)
        self.assertIn('World', sanitized)
        # Line breaks should be converted to <br>
        self.assertIn('<br>', sanitized)
    
    def test_validate_content_length_post(self):
        """Test post content length validation"""
        # Valid content
        valid_content = "This is a valid post content."
        self.sanitizer.validate_content_length(valid_content, 'post_content')
        
        # Too short
        with self.assertRaises(ValidationError):
            self.sanitizer.validate_content_length("", 'post_content')
        
        # Too long
        long_content = "x" * 5001
        with self.assertRaises(ValidationError):
            self.sanitizer.validate_content_length(long_content, 'post_content')
    
    def test_validate_content_length_comment(self):
        """Test comment content length validation"""
        # Valid content
        valid_content = "This is a valid comment."
        self.sanitizer.validate_content_length(valid_content, 'comment_content')
        
        # Too long
        long_content = "x" * 1001
        with self.assertRaises(ValidationError):
            self.sanitizer.validate_content_length(long_content, 'comment_content')
    
    def test_check_inappropriate_content(self):
        """Test inappropriate content detection"""
        # Clean content
        clean_content = "This is a normal post about technology."
        issues = self.sanitizer.check_inappropriate_content(clean_content)
        self.assertEqual(len(issues), 0)
        
        # Spam-like content
        spam_content = "SPAM SPAM SPAM BUY NOW!!!"
        issues = self.sanitizer.check_inappropriate_content(spam_content)
        self.assertGreater(len(issues), 0)
        
        # Repeated characters
        repeated_content = "Hellooooooooooooooooooo"
        issues = self.sanitizer.check_inappropriate_content(repeated_content)
        self.assertGreater(len(issues), 0)
    
    def test_validate_urls(self):
        """Test URL validation"""
        # Safe URLs
        safe_content = "Check out https://example.com for more info"
        issues = self.sanitizer.validate_urls(safe_content)
        self.assertEqual(len(issues), 0)
        
        # Suspicious domains
        suspicious_content = "Visit http://malicious.tk for free stuff"
        issues = self.sanitizer.validate_urls(suspicious_content)
        self.assertGreater(len(issues), 0)
        
        # IP addresses
        ip_content = "Visit http://*********** for admin panel"
        issues = self.sanitizer.validate_urls(ip_content)
        self.assertGreater(len(issues), 0)
    
    def test_sanitize_and_validate_complete(self):
        """Test complete sanitization and validation process"""
        # Valid content
        valid_content = "This is a <strong>valid</strong> post about technology."
        result = self.sanitizer.sanitize_and_validate(valid_content, 'post_content')
        
        self.assertTrue(result['is_valid'])
        self.assertIn('valid', result['sanitized_content'])
        self.assertEqual(len(result['issues']), 0)
        
        # Invalid content
        invalid_content = '<script>alert("xss")</script>' + 'x' * 5001
        result = self.sanitizer.sanitize_and_validate(invalid_content, 'post_content')
        
        self.assertFalse(result['is_valid'])
        self.assertGreater(len(result['issues']), 0)


class SecurityEventLoggerTest(TestCase):
    """Test security event logging"""
    
    def setUp(self):
        self.logger = SecurityEventLogger()
    
    @patch('community.security.logger')
    def test_log_suspicious_activity(self, mock_logger):
        """Test suspicious activity logging"""
        self.logger.log_suspicious_activity(
            user_id=123,
            activity_type='test_activity',
            details={'test': 'data'}
        )
        
        mock_logger.warning.assert_called_once()
        call_args = mock_logger.warning.call_args[0][0]
        self.assertIn('SECURITY EVENT', call_args)
        self.assertIn('User: 123', call_args)
        self.assertIn('test_activity', call_args)
    
    @patch('community.security.logger')
    def test_log_authentication_failure(self, mock_logger):
        """Test authentication failure logging"""
        self.logger.log_authentication_failure(
            user_id=123,
            reason='invalid_token',
            request_info={'path': '/api/test'}
        )
        
        mock_logger.error.assert_called_once()
        call_args = mock_logger.error.call_args[0][0]
        self.assertIn('AUTH FAILURE', call_args)
        self.assertIn('invalid_token', call_args)
    
    @patch('community.security.logger')
    def test_log_rate_limit_exceeded(self, mock_logger):
        """Test rate limit logging"""
        self.logger.log_rate_limit_exceeded(
            user_id=123,
            endpoint='/api/posts/',
            ip_address='***********'
        )
        
        mock_logger.warning.assert_called_once()
        call_args = mock_logger.warning.call_args[0][0]
        self.assertIn('RATE LIMIT EXCEEDED', call_args)
        self.assertIn('/api/posts/', call_args)


class CommunitySecurityIntegrationTest(APITestCase):
    """Integration tests for community security features"""
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
    
    def test_post_creation_with_malicious_content(self):
        """Test post creation with malicious content is sanitized"""
        malicious_data = {
            'title': '<script>alert("xss")</script>Malicious Title',
            'content': '<script>alert("xss")</script>Malicious content with <iframe src="evil.com"></iframe>',
            'visibility': 'public',
            'allow_comments': True
        }
        
        response = self.client.post('/api/community/posts/', malicious_data)
        
        # Should succeed but content should be sanitized
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check that malicious content was removed
        post = CommunityPost.objects.get(id=response.data['id'])
        self.assertNotIn('<script>', post.title)
        self.assertNotIn('<script>', post.content)
        self.assertNotIn('<iframe>', post.content)
        self.assertNotIn('alert', post.title)
        self.assertNotIn('alert', post.content)
    
    def test_comment_creation_with_malicious_content(self):
        """Test comment creation with malicious content is sanitized"""
        # Create a post first
        post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.user,
            visibility='public',
            allow_comments=True
        )
        
        malicious_data = {
            'content': '<script>alert("xss")</script>Malicious comment',
            'post': post.id
        }
        
        response = self.client.post(f'/api/community/posts/{post.id}/add_comment/', malicious_data)
        
        # Should succeed but content should be sanitized
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check that malicious content was removed
        comment = CommunityComment.objects.get(id=response.data['id'])
        self.assertNotIn('<script>', comment.content)
        self.assertNotIn('alert', comment.content)
    
    def test_post_creation_with_invalid_length(self):
        """Test post creation with invalid content length"""
        invalid_data = {
            'title': 'x' * 201,  # Too long
            'content': 'Valid content',
            'visibility': 'public',
            'allow_comments': True
        }
        
        response = self.client.post('/api/community/posts/', invalid_data)
        
        # Should fail validation
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('title', response.data)
    
    def test_unauthenticated_post_creation(self):
        """Test that unauthenticated users cannot create posts"""
        self.client.force_authenticate(user=None)
        
        data = {
            'title': 'Test Title',
            'content': 'Test content',
            'visibility': 'public',
            'allow_comments': True
        }
        
        response = self.client.post('/api/community/posts/', data)
        
        # Should require authentication
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_inactive_user_post_creation(self):
        """Test that inactive users cannot create posts"""
        self.user.is_active = False
        self.user.save()
        
        data = {
            'title': 'Test Title',
            'content': 'Test content',
            'visibility': 'public',
            'allow_comments': True
        }
        
        response = self.client.post('/api/community/posts/', data)
        
        # Should be forbidden
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Account is inactive', str(response.data))
    
    @patch('community.security.security_logger.log_suspicious_activity')
    def test_security_logging_on_post_creation(self, mock_log):
        """Test that security events are logged on post creation"""
        data = {
            'title': 'Test Title',
            'content': 'Test content',
            'visibility': 'public',
            'allow_comments': True
        }
        
        response = self.client.post('/api/community/posts/', data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check that security logging was called
        mock_log.assert_called_once()
        call_args = mock_log.call_args
        self.assertEqual(call_args[1]['user_id'], self.user.id)
        self.assertEqual(call_args[1]['activity_type'], 'post_created')


class CommunitySerializerSecurityTest(TestCase):
    """Test security features in community serializers"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.factory = RequestFactory()
    
    def test_post_serializer_content_validation(self):
        """Test post serializer content validation"""
        request = self.factory.post('/api/community/posts/')
        request.user = self.user
        
        # Valid data
        valid_data = {
            'title': 'Valid Title',
            'content': 'Valid content for testing',
            'visibility': 'public',
            'allow_comments': True
        }
        
        serializer = CommunityPostSerializer(data=valid_data, context={'request': request})
        self.assertTrue(serializer.is_valid())
        
        # Invalid data - malicious content
        malicious_data = {
            'title': '<script>alert("xss")</script>Malicious',
            'content': '<script>alert("xss")</script>Malicious content',
            'visibility': 'public',
            'allow_comments': True
        }
        
        serializer = CommunityPostSerializer(data=malicious_data, context={'request': request})
        self.assertTrue(serializer.is_valid())  # Should be valid but sanitized
        
        # Check sanitized data
        validated_data = serializer.validated_data
        self.assertNotIn('<script>', validated_data['title'])
        self.assertNotIn('<script>', validated_data['content'])
    
    def test_comment_serializer_validation(self):
        """Test comment serializer validation"""
        # Create a post
        post = CommunityPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.user,
            visibility='public',
            allow_comments=True
        )
        
        request = self.factory.post('/api/community/comments/')
        request.user = self.user
        
        # Valid comment
        valid_data = {
            'content': 'Valid comment content',
            'post': post.id
        }
        
        serializer = CommunityCommentSerializer(data=valid_data, context={'request': request})
        self.assertTrue(serializer.is_valid())
        
        # Invalid comment - too long
        invalid_data = {
            'content': 'x' * 1001,  # Too long for comment
            'post': post.id
        }
        
        serializer = CommunityCommentSerializer(data=invalid_data, context={'request': request})
        self.assertFalse(serializer.is_valid())
        self.assertIn('content', serializer.errors)
    
    def test_tag_validation(self):
        """Test tag validation in post serializer"""
        request = self.factory.post('/api/community/posts/')
        request.user = self.user
        
        # Valid tags
        valid_data = {
            'title': 'Test Post',
            'content': 'Test content',
            'tag_names': ['tech', 'programming', 'django'],
            'visibility': 'public',
            'allow_comments': True
        }
        
        serializer = CommunityPostSerializer(data=valid_data, context={'request': request})
        self.assertTrue(serializer.is_valid())
        
        # Too many tags
        invalid_data = {
            'title': 'Test Post',
            'content': 'Test content',
            'tag_names': [f'tag{i}' for i in range(11)],  # 11 tags, max is 10
            'visibility': 'public',
            'allow_comments': True
        }
        
        serializer = CommunityPostSerializer(data=invalid_data, context={'request': request})
        self.assertFalse(serializer.is_valid())
        self.assertIn('tag_names', serializer.errors)
        
        # Invalid tag format
        invalid_format_data = {
            'title': 'Test Post',
            'content': 'Test content',
            'tag_names': ['valid-tag', 'invalid tag with spaces!@#'],
            'visibility': 'public',
            'allow_comments': True
        }
        
        serializer = CommunityPostSerializer(data=invalid_format_data, context={'request': request})
        self.assertFalse(serializer.is_valid())
        self.assertIn('tag_names', serializer.errors)
