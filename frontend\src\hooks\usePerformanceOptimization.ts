/**
 * Performance Optimization Hook
 * 
 * Provides utilities for monitoring and optimizing component performance
 * including render tracking, memory usage, and performance metrics.
 */

import { useEffect, useRef, useCallback, useMemo } from 'react';

export interface PerformanceMetrics {
  renderCount: number;
  lastRenderTime: number;
  averageRenderTime: number;
  memoryUsage?: number;
  componentName: string;
}

export interface UsePerformanceOptimizationReturn {
  // Performance tracking
  trackRender: (componentName: string) => void;
  getMetrics: (componentName: string) => PerformanceMetrics | null;
  
  // Memory optimization
  clearCache: () => void;
  
  // Debounced functions
  createDebouncedCallback: <T extends (...args: any[]) => any>(
    callback: T,
    delay: number
  ) => T;
  
  // Throttled functions
  createThrottledCallback: <T extends (...args: any[]) => any>(
    callback: T,
    delay: number
  ) => T;
  
  // Virtual scrolling helpers
  getVisibleItems: <T>(
    items: T[],
    containerHeight: number,
    itemHeight: number,
    scrollTop: number
  ) => { visibleItems: T[]; startIndex: number; endIndex: number };
}

class PerformanceTracker {
  private metrics: Map<string, PerformanceMetrics> = new Map();
  private renderTimes: Map<string, number[]> = new Map();

  trackRender(componentName: string): void {
    const now = performance.now();
    const existing = this.metrics.get(componentName);
    
    if (existing) {
      const renderTimes = this.renderTimes.get(componentName) || [];
      const timeSinceLastRender = now - existing.lastRenderTime;
      renderTimes.push(timeSinceLastRender);
      
      // Keep only last 10 render times for average calculation
      if (renderTimes.length > 10) {
        renderTimes.shift();
      }
      
      this.renderTimes.set(componentName, renderTimes);
      
      const averageRenderTime = renderTimes.reduce((sum, time) => sum + time, 0) / renderTimes.length;
      
      this.metrics.set(componentName, {
        ...existing,
        renderCount: existing.renderCount + 1,
        lastRenderTime: now,
        averageRenderTime,
        memoryUsage: this.getMemoryUsage()
      });
    } else {
      this.metrics.set(componentName, {
        renderCount: 1,
        lastRenderTime: now,
        averageRenderTime: 0,
        memoryUsage: this.getMemoryUsage(),
        componentName
      });
      this.renderTimes.set(componentName, []);
    }
  }

  getMetrics(componentName: string): PerformanceMetrics | null {
    return this.metrics.get(componentName) || null;
  }

  private getMemoryUsage(): number | undefined {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return undefined;
  }

  clearCache(): void {
    this.metrics.clear();
    this.renderTimes.clear();
  }

  getAllMetrics(): PerformanceMetrics[] {
    return Array.from(this.metrics.values());
  }
}

// Global performance tracker instance
const performanceTracker = new PerformanceTracker();

export const usePerformanceOptimization = (): UsePerformanceOptimizationReturn => {
  const debounceTimers = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const throttleTimers = useRef<Map<string, { lastCall: number; timeout?: NodeJS.Timeout }>>(new Map());

  // Track render performance
  const trackRender = useCallback((componentName: string) => {
    performanceTracker.trackRender(componentName);
  }, []);

  // Get performance metrics
  const getMetrics = useCallback((componentName: string) => {
    return performanceTracker.getMetrics(componentName);
  }, []);

  // Clear performance cache
  const clearCache = useCallback(() => {
    performanceTracker.clearCache();
  }, []);

  // Create debounced callback
  const createDebouncedCallback = useCallback(<T extends (...args: any[]) => any>(
    callback: T,
    delay: number
  ): T => {
    const callbackKey = callback.toString();
    
    return ((...args: any[]) => {
      const existingTimer = debounceTimers.current.get(callbackKey);
      if (existingTimer) {
        clearTimeout(existingTimer);
      }
      
      const timer = setTimeout(() => {
        callback(...args);
        debounceTimers.current.delete(callbackKey);
      }, delay);
      
      debounceTimers.current.set(callbackKey, timer);
    }) as T;
  }, []);

  // Create throttled callback
  const createThrottledCallback = useCallback(<T extends (...args: any[]) => any>(
    callback: T,
    delay: number
  ): T => {
    const callbackKey = callback.toString();
    
    return ((...args: any[]) => {
      const now = Date.now();
      const throttleData = throttleTimers.current.get(callbackKey);
      
      if (!throttleData || now - throttleData.lastCall >= delay) {
        callback(...args);
        throttleTimers.current.set(callbackKey, { lastCall: now });
      } else if (!throttleData.timeout) {
        const remainingTime = delay - (now - throttleData.lastCall);
        const timeout = setTimeout(() => {
          callback(...args);
          const data = throttleTimers.current.get(callbackKey);
          if (data) {
            data.lastCall = Date.now();
            data.timeout = undefined;
          }
        }, remainingTime);
        
        throttleData.timeout = timeout;
      }
    }) as T;
  }, []);

  // Virtual scrolling helper
  const getVisibleItems = useCallback(<T>(
    items: T[],
    containerHeight: number,
    itemHeight: number,
    scrollTop: number
  ) => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length - 1
    );
    
    const visibleItems = items.slice(startIndex, endIndex + 1);
    
    return {
      visibleItems,
      startIndex,
      endIndex
    };
  }, []);

  // Cleanup timers on unmount
  useEffect(() => {
    return () => {
      debounceTimers.current.forEach(timer => clearTimeout(timer));
      throttleTimers.current.forEach(data => {
        if (data.timeout) clearTimeout(data.timeout);
      });
      debounceTimers.current.clear();
      throttleTimers.current.clear();
    };
  }, []);

  return {
    trackRender,
    getMetrics,
    clearCache,
    createDebouncedCallback,
    createThrottledCallback,
    getVisibleItems
  };
};

/**
 * Hook for tracking component render performance
 */
export const useRenderTracking = (componentName: string) => {
  const { trackRender, getMetrics } = usePerformanceOptimization();
  
  useEffect(() => {
    trackRender(componentName);
  });
  
  const metrics = useMemo(() => getMetrics(componentName), [getMetrics, componentName]);
  
  return metrics;
};

/**
 * Hook for optimized event handlers
 */
export const useOptimizedHandlers = () => {
  const { createDebouncedCallback, createThrottledCallback } = usePerformanceOptimization();
  
  return {
    debounce: createDebouncedCallback,
    throttle: createThrottledCallback
  };
};

/**
 * Hook for virtual scrolling
 */
export const useVirtualScrolling = <T>(
  items: T[],
  containerHeight: number,
  itemHeight: number
) => {
  const { getVisibleItems } = usePerformanceOptimization();
  
  const getVisible = useCallback((scrollTop: number) => {
    return getVisibleItems(items, containerHeight, itemHeight, scrollTop);
  }, [items, containerHeight, itemHeight, getVisibleItems]);
  
  return { getVisible };
};

/**
 * Performance monitoring utilities
 */
export const performanceUtils = {
  // Log performance metrics to console
  logMetrics: () => {
    const metrics = performanceTracker.getAllMetrics();
    console.group('🚀 Performance Metrics');
    metrics.forEach(metric => {
      console.log(`${metric.componentName}:`, {
        renders: metric.renderCount,
        avgRenderTime: `${metric.averageRenderTime.toFixed(2)}ms`,
        memory: metric.memoryUsage ? `${(metric.memoryUsage / 1024 / 1024).toFixed(2)}MB` : 'N/A'
      });
    });
    console.groupEnd();
  },
  
  // Get performance summary
  getSummary: () => {
    const metrics = performanceTracker.getAllMetrics();
    const totalRenders = metrics.reduce((sum, m) => sum + m.renderCount, 0);
    const avgRenderTime = metrics.reduce((sum, m) => sum + m.averageRenderTime, 0) / metrics.length;
    
    return {
      totalComponents: metrics.length,
      totalRenders,
      averageRenderTime: avgRenderTime || 0,
      componentsWithHighRenderCount: metrics.filter(m => m.renderCount > 10).length
    };
  }
};
