"""
Community Caching Utilities
Comprehensive caching strategies for community features
"""

import hashlib
import json
from typing import Any, Optional, Dict, List
from functools import wraps
from django.core.cache import cache, caches
from django.conf import settings
from django.utils import timezone
from django.db.models import QuerySet
from django.contrib.auth.models import User
import logging

logger = logging.getLogger(__name__)

# Get community-specific cache
community_cache = caches['community'] if 'community' in settings.CACHES else cache


class CommunityCache:
    """Community-specific caching utilities"""
    
    # Cache key prefixes
    POSTS_PREFIX = 'community:posts'
    STATS_PREFIX = 'community:stats'
    HASHTAGS_PREFIX = 'community:hashtags'
    USER_PREFIX = 'community:user'
    FEED_PREFIX = 'community:feed'
    RECOMMENDATIONS_PREFIX = 'community:recommendations'
    
    @classmethod
    def _generate_cache_key(cls, prefix: str, *args, **kwargs) -> str:
        """Generate a consistent cache key"""
        key_parts = [prefix]
        
        # Add positional arguments
        for arg in args:
            if isinstance(arg, (int, str)):
                key_parts.append(str(arg))
            elif isinstance(arg, User):
                key_parts.append(f"user_{arg.id}")
            else:
                key_parts.append(str(hash(str(arg))))
        
        # Add keyword arguments
        if kwargs:
            sorted_kwargs = sorted(kwargs.items())
            kwargs_str = json.dumps(sorted_kwargs, sort_keys=True)
            kwargs_hash = hashlib.md5(kwargs_str.encode()).hexdigest()[:8]
            key_parts.append(kwargs_hash)
        
        return ':'.join(key_parts)
    
    @classmethod
    def get_posts_cache_key(cls, user_id: Optional[int] = None, **filters) -> str:
        """Generate cache key for posts"""
        return cls._generate_cache_key(cls.POSTS_PREFIX, user_id or 'anonymous', **filters)
    
    @classmethod
    def get_stats_cache_key(cls) -> str:
        """Generate cache key for community stats"""
        return cls._generate_cache_key(cls.STATS_PREFIX, 'global')
    
    @classmethod
    def get_hashtags_cache_key(cls) -> str:
        """Generate cache key for trending hashtags"""
        return cls._generate_cache_key(cls.HASHTAGS_PREFIX, 'trending')
    
    @classmethod
    def get_user_cache_key(cls, user_id: int, data_type: str = 'profile') -> str:
        """Generate cache key for user data"""
        return cls._generate_cache_key(cls.USER_PREFIX, user_id, data_type)
    
    @classmethod
    def get_feed_cache_key(cls, user_id: int, **params) -> str:
        """Generate cache key for user feed"""
        return cls._generate_cache_key(cls.FEED_PREFIX, user_id, **params)
    
    @classmethod
    def get_recommendations_cache_key(cls, **params) -> str:
        """Generate cache key for user recommendations"""
        return cls._generate_cache_key(cls.RECOMMENDATIONS_PREFIX, **params)
    
    @classmethod
    def set_with_timeout(cls, key: str, value: Any, timeout_key: str) -> bool:
        """Set cache value with configured timeout"""
        timeout = getattr(settings, 'CACHE_TIMEOUTS', {}).get(timeout_key, 300)
        try:
            community_cache.set(key, value, timeout)
            logger.debug(f"Cache set: {key} (timeout: {timeout}s)")
            return True
        except Exception as e:
            logger.error(f"Cache set failed for {key}: {e}")
            return False
    
    @classmethod
    def get(cls, key: str) -> Any:
        """Get value from cache"""
        try:
            value = community_cache.get(key)
            if value is not None:
                logger.debug(f"Cache hit: {key}")
            else:
                logger.debug(f"Cache miss: {key}")
            return value
        except Exception as e:
            logger.error(f"Cache get failed for {key}: {e}")
            return None
    
    @classmethod
    def delete(cls, key: str) -> bool:
        """Delete value from cache"""
        try:
            community_cache.delete(key)
            logger.debug(f"Cache deleted: {key}")
            return True
        except Exception as e:
            logger.error(f"Cache delete failed for {key}: {e}")
            return False
    
    @classmethod
    def delete_pattern(cls, pattern: str) -> bool:
        """Delete all keys matching pattern"""
        try:
            if hasattr(community_cache, 'delete_pattern'):
                community_cache.delete_pattern(pattern)
            else:
                # Fallback for non-Redis backends
                keys = community_cache._cache.keys() if hasattr(community_cache, '_cache') else []
                for key in keys:
                    if pattern in key:
                        community_cache.delete(key)
            logger.debug(f"Cache pattern deleted: {pattern}")
            return True
        except Exception as e:
            logger.error(f"Cache pattern delete failed for {pattern}: {e}")
            return False
    
    @classmethod
    def invalidate_user_cache(cls, user_id: int):
        """Invalidate all cache entries for a specific user"""
        patterns = [
            f"{cls.USER_PREFIX}:{user_id}:*",
            f"{cls.FEED_PREFIX}:{user_id}:*",
            f"{cls.POSTS_PREFIX}:*",  # Posts might be affected by user actions
        ]
        
        for pattern in patterns:
            cls.delete_pattern(pattern)
    
    @classmethod
    def invalidate_posts_cache(cls):
        """Invalidate posts-related cache entries"""
        patterns = [
            f"{cls.POSTS_PREFIX}:*",
            f"{cls.FEED_PREFIX}:*",
            f"{cls.STATS_PREFIX}:*",
        ]
        
        for pattern in patterns:
            cls.delete_pattern(pattern)
    
    @classmethod
    def invalidate_stats_cache(cls):
        """Invalidate community stats cache"""
        cls.delete_pattern(f"{cls.STATS_PREFIX}:*")
    
    @classmethod
    def invalidate_hashtags_cache(cls):
        """Invalidate hashtags cache"""
        cls.delete_pattern(f"{cls.HASHTAGS_PREFIX}:*")


def cache_result(timeout_key: str, key_generator=None):
    """
    Decorator to cache function results
    
    Args:
        timeout_key: Key to lookup timeout in CACHE_TIMEOUTS setting
        key_generator: Function to generate cache key, defaults to function name + args
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            if key_generator:
                cache_key = key_generator(*args, **kwargs)
            else:
                # Default key generation
                key_parts = [func.__name__]
                for arg in args:
                    if hasattr(arg, 'id'):
                        key_parts.append(f"{arg.__class__.__name__}_{arg.id}")
                    else:
                        key_parts.append(str(hash(str(arg))))
                
                if kwargs:
                    kwargs_str = json.dumps(sorted(kwargs.items()), sort_keys=True)
                    kwargs_hash = hashlib.md5(kwargs_str.encode()).hexdigest()[:8]
                    key_parts.append(kwargs_hash)
                
                cache_key = ':'.join(key_parts)
            
            # Try to get from cache
            cached_result = CommunityCache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            CommunityCache.set_with_timeout(cache_key, result, timeout_key)
            
            return result
        
        return wrapper
    return decorator


def cache_queryset(timeout_key: str, key_generator=None):
    """
    Decorator to cache QuerySet results
    Converts QuerySet to list for caching
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            if key_generator:
                cache_key = key_generator(*args, **kwargs)
            else:
                cache_key = f"queryset:{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # Try to get from cache
            cached_result = CommunityCache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function
            result = func(*args, **kwargs)
            
            # Convert QuerySet to list for caching
            if isinstance(result, QuerySet):
                cached_data = list(result)
                CommunityCache.set_with_timeout(cache_key, cached_data, timeout_key)
                return result  # Return original QuerySet
            else:
                CommunityCache.set_with_timeout(cache_key, result, timeout_key)
                return result
        
        return wrapper
    return decorator


class CacheInvalidationMixin:
    """Mixin to handle cache invalidation in models"""
    
    def invalidate_cache(self):
        """Override in models to define cache invalidation logic"""
        pass
    
    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        self.invalidate_cache()
    
    def delete(self, *args, **kwargs):
        self.invalidate_cache()
        super().delete(*args, **kwargs)


# Cache warming utilities
class CacheWarmer:
    """Utilities to warm up cache with frequently accessed data"""
    
    @staticmethod
    def warm_community_stats():
        """Pre-load community stats into cache"""
        from .models import CommunityStats
        from django.contrib.auth.models import User
        from .models import CommunityPost, CommunityComment
        
        try:
            stats, created = CommunityStats.objects.get_or_create(id=1)
            
            # Update stats
            stats.total_posts = CommunityPost.objects.count()
            stats.total_users = User.objects.filter(is_active=True).count()
            stats.total_comments = CommunityComment.objects.count()
            stats.save()
            
            # Cache the stats
            cache_key = CommunityCache.get_stats_cache_key()
            CommunityCache.set_with_timeout(cache_key, stats, 'community_stats')
            
            logger.info("Community stats cache warmed")
            return True
        except Exception as e:
            logger.error(f"Failed to warm community stats cache: {e}")
            return False
    
    @staticmethod
    def warm_trending_hashtags():
        """Pre-load trending hashtags into cache"""
        from api.models import Tag
        from django.db.models import Count, Q
        from django.utils import timezone
        from datetime import timedelta
        
        try:
            seven_days_ago = timezone.now() - timedelta(days=7)
            
            trending_tags = Tag.objects.filter(
                community_posts__created_at__gte=seven_days_ago
            ).annotate(
                count=Count('community_posts'),
                recent_count=Count('community_posts', filter=Q(community_posts__created_at__gte=seven_days_ago))
            ).filter(
                count__gt=0
            ).order_by('-recent_count', '-count')[:10]
            
            # Cache the trending hashtags
            cache_key = CommunityCache.get_hashtags_cache_key()
            CommunityCache.set_with_timeout(cache_key, list(trending_tags), 'trending_hashtags')
            
            logger.info("Trending hashtags cache warmed")
            return True
        except Exception as e:
            logger.error(f"Failed to warm trending hashtags cache: {e}")
            return False
