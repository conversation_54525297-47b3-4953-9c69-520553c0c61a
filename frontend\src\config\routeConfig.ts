/**
 * 🎯 UNIFIED ROUTE CONFIGURATION
 * Centralized route definitions with role-based access control
 */

import { RoleCapabilities } from './roleConfig';

export interface RouteDefinition {
  path: string;
  component: string;
  requiredCapability?: keyof RoleCapabilities;
  requiredCapabilities?: (keyof RoleCapabilities)[];
  requireAll?: boolean;
  allowedRoles?: string[];
  isPublic?: boolean;
  redirectTo?: string;
  layout?: 'default' | 'auth' | 'dashboard' | 'minimal';
  title?: string;
  description?: string;
}

// ========================================
// PUBLIC ROUTES
// ========================================

export const PUBLIC_ROUTES: RouteDefinition[] = [
  {
    path: '/',
    component: 'HomePage',
    isPublic: true,
    layout: 'default',
    title: 'Home - AI Incubator',
    description: 'Welcome to the AI-powered business incubator platform'
  },
  {
    path: '/features',
    component: 'FeaturesPage',
    isPublic: true,
    layout: 'default',
    title: 'Features - AI Incubator'
  },
  {
    path: '/login',
    component: 'LoginPage',
    isPublic: true,
    layout: 'auth',
    title: 'Login - AI Incubator'
  },
  {
    path: '/register',
    component: 'EnhancedRegisterPage',
    isPublic: true,
    layout: 'auth',
    title: 'Register - AI Incubator'
  },
  {
    path: '/registration-success',
    component: 'RegistrationSuccessPage',
    isPublic: true,
    layout: 'auth',
    title: 'Registration Successful'
  }
];

// ========================================
// AUTHENTICATED ROUTES
// ========================================

export const AUTHENTICATED_ROUTES: RouteDefinition[] = [
  // ✅ FIXED: User-specific routes (NO dashboard access - users are NOT business roles)
  {
    path: '/user/home',
    component: 'UserHomePage',
    allowedRoles: ['user'],
    layout: 'minimal',
    title: 'Home'
  },
  {
    path: '/user/profile',
    component: 'UserProfilePage',
    allowedRoles: ['user'],
    layout: 'minimal',
    title: 'Profile'
  },
  {
    path: '/user/ai-chat',
    component: 'AIChatPage',
    allowedRoles: ['user'],
    requiredCapability: 'canAccessAI',
    layout: 'minimal',
    title: 'AI Assistant'
  },

  // ✅ FIXED: Dashboard routes (BUSINESS ROLES ONLY - excludes 'user')
  {
    path: '/:role/dashboard',
    component: 'DashboardPage',
    requiredCapability: 'canAccessDashboard',
    allowedRoles: ['entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'],
    layout: 'dashboard',
    title: 'Dashboard'
  },
  
  // Business plan routes
  {
    path: '/:role/business-plans',
    component: 'BusinessPlansPage',
    requiredCapability: 'canCreateBusinessPlans',
    layout: 'dashboard',
    title: 'Business Plans'
  },
  {
    path: '/:role/business-ideas',
    component: 'BusinessIdeasPage',
    requiredCapability: 'canCreateBusinessPlans',
    layout: 'dashboard',
    title: 'Business Ideas'
  },
  
  // AI routes
  {
    path: '/:role/ai-chat',
    component: 'AIChatPage',
    requiredCapability: 'canAccessAI',
    layout: 'dashboard',
    title: 'AI Assistant'
  },
  
  // Mentorship routes
  {
    path: '/:role/mentorship',
    component: 'MentorshipPage',
    requiredCapability: 'canMentor',
    layout: 'dashboard',
    title: 'Mentorship'
  },
  {
    path: '/:role/mentees',
    component: 'MenteesPage',
    requiredCapability: 'canMentor',
    layout: 'dashboard',
    title: 'My Mentees'
  },
  
  // Investment routes
  {
    path: '/:role/investments',
    component: 'InvestmentsPage',
    requiredCapability: 'canInvest',
    layout: 'dashboard',
    title: 'Investments'
  },
  {
    path: '/:role/portfolio',
    component: 'PortfolioPage',
    requiredCapability: 'canInvest',
    layout: 'dashboard',
    title: 'Portfolio'
  },
  
  // Moderation routes
  {
    path: '/:role/moderation',
    component: 'ModerationPage',
    requiredCapability: 'canModerateContent',
    layout: 'dashboard',
    title: 'Content Moderation'
  },
  {
    path: '/:role/reports',
    component: 'ReportsPage',
    requiredCapability: 'canModerateContent',
    layout: 'dashboard',
    title: 'Reports'
  },
  
  // User management routes
  {
    path: '/:role/users',
    component: 'UsersPage',
    requiredCapability: 'canManageUsers',
    layout: 'dashboard',
    title: 'User Management'
  },
  {
    path: '/:role/approvals',
    component: 'ApprovalsPage',
    requiredCapability: 'canManageUsers',
    layout: 'dashboard',
    title: 'User Approvals'
  },
  
  // Analytics routes
  {
    path: '/:role/analytics',
    component: 'AnalyticsPage',
    requiredCapability: 'canAccessAnalytics',
    layout: 'dashboard',
    title: 'Analytics'
  },
  
  // System settings routes
  {
    path: '/:role/system',
    component: 'SystemPage',
    requiredCapability: 'canAccessSystemSettings',
    layout: 'dashboard',
    title: 'System Management'
  },
  {
    path: '/:role/settings',
    component: 'SettingsPage',
    layout: 'dashboard',
    title: 'Settings'
  },
  
  // Profile routes (all authenticated users)
  {
    path: '/:role/profile',
    component: 'ProfilePage',
    layout: 'dashboard',
    title: 'Profile'
  }
];

// ========================================
// ERROR ROUTES
// ========================================

export const ERROR_ROUTES: RouteDefinition[] = [
  {
    path: '/access-denied',
    component: 'AccessDeniedPage',
    isPublic: true,
    layout: 'minimal',
    title: 'Access Denied'
  },
  {
    path: '/not-found',
    component: 'NotFoundPage',
    isPublic: true,
    layout: 'minimal',
    title: 'Page Not Found'
  },
  {
    path: '*',
    component: 'NotFoundPage',
    isPublic: true,
    layout: 'minimal',
    title: 'Page Not Found'
  }
];

// ========================================
// UTILITY FUNCTIONS
// ========================================

export function getAllRoutes(): RouteDefinition[] {
  return [...PUBLIC_ROUTES, ...AUTHENTICATED_ROUTES, ...ERROR_ROUTES];
}

export function getRoutesByRole(role: string): RouteDefinition[] {
  return AUTHENTICATED_ROUTES.filter(route => {
    // Public routes are always accessible
    if (route.isPublic) return true;
    
    // Check allowed roles
    if (route.allowedRoles && !route.allowedRoles.includes(role)) {
      return false;
    }
    
    // For dynamic role routes, replace :role with actual role
    return true;
  });
}

export function getRouteDefinition(path: string): RouteDefinition | undefined {
  const allRoutes = getAllRoutes();
  
  // First try exact match
  let route = allRoutes.find(r => r.path === path);
  if (route) return route;
  
  // Then try pattern matching for dynamic routes
  route = allRoutes.find(r => {
    if (!r.path.includes(':role')) return false;
    
    const pattern = r.path.replace(':role', '[^/]+');
    const regex = new RegExp(`^${pattern}$`);
    return regex.test(path);
  });
  
  return route;
}

export function isRouteAccessible(path: string, userRole: string, capabilities: any): boolean {
  const route = getRouteDefinition(path);
  if (!route) return false;
  
  // Public routes are always accessible
  if (route.isPublic) return true;
  
  // Check allowed roles
  if (route.allowedRoles && !route.allowedRoles.includes(userRole)) {
    return false;
  }
  
  // Check required capability
  if (route.requiredCapability && !capabilities[route.requiredCapability]) {
    return false;
  }
  
  // Check multiple capabilities
  if (route.requiredCapabilities) {
    if (route.requireAll) {
      // User must have ALL capabilities
      return route.requiredCapabilities.every(cap => capabilities[cap]);
    } else {
      // User must have ANY capability
      return route.requiredCapabilities.some(cap => capabilities[cap]);
    }
  }
  
  return true;
}

export function getPageTitle(path: string, role?: string): string {
  const route = getRouteDefinition(path);
  if (!route) return 'AI Incubator';
  
  let title = route.title || 'AI Incubator';
  
  // Replace role placeholder if present
  if (role && title.includes(':role')) {
    const roleDisplayName = role.charAt(0).toUpperCase() + role.slice(1);
    title = title.replace(':role', roleDisplayName);
  }
  
  return title;
}

export default {
  PUBLIC_ROUTES,
  AUTHENTICATED_ROUTES,
  ERROR_ROUTES,
  getAllRoutes,
  getRoutesByRole,
  getRouteDefinition,
  isRouteAccessible,
  getPageTitle
};
