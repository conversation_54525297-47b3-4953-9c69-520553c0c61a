"""
AI Models Package
Consolidated AI-related Django models

This package contains all AI-related models consolidated from:
- core.models (AIConfiguration, AIUsageLog, AIServiceStatus, AIRateLimit)
- ai_recommendations.models (AIRecommendation, AIRecommendationFeedback)
- ai_core.models (AISession, AIInteraction)
- ai_models.models (AIModelMetadata, PredictionLog)
"""

# Import consolidated models
from .ai_models import (
    # Core AI Configuration Models
    AIConfiguration,
    AIUsageLog,
    AIServiceStatus,
    AIRateLimit,

    # AI Session and Interaction Models
    AISession,
    AIInteraction,

    # AI Recommendation Models
    AIRecommendation,
    AIRecommendationFeedback,

    # AI Model Metadata and Prediction Models
    AIModelMetadata,
    PredictionLog
)

__all__ = [
    # Core AI Configuration Models
    'AIConfiguration',
    'AIUsageLog',
    'AIServiceStatus',
    'AIRateLimit',

    # AI Session and Interaction Models
    'AISession',
    'AIInteraction',

    # AI Recommendation Models
    'AIRecommendation',
    'AIRecommendationFeedback',

    # AI Model Metadata and Prediction Models
    'AIModelMetadata',
    'PredictionLog'
]
