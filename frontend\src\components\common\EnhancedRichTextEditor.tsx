import React, { useState, useRef, useCallback } from 'react';
import { Bold, Italic, Underline, Link, Image, Hash, AtSign, Smile, List, ListOrdered, Quote, Code } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';

interface EnhancedRichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  maxLength?: number;
  onMentionSearch?: (query: string) => void;
  onHashtagSearch?: (query: string) => void;
  mentions?: Array<{ id: string; username: string; avatar?: string }>;
  hashtags?: Array<{ name: string; count: number }>;
  className?: string;
  disabled?: boolean;
}

const EnhancedRichTextEditor: React.FC<EnhancedRichTextEditorProps> = ({
  value,
  onChange,
  placeholder = 'What\'s on your mind?',
  maxLength = 2000,
  onMentionSearch,
  onHashtagSearch,
  mentions = [],
  hashtags = [],
  className = '',
  disabled = false
}) => {
  const { isRTL } = useLanguage();
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [showMentions, setShowMentions] = useState(false);
  const [showHashtags, setShowHashtags] = useState(false);
  const [mentionQuery, setMentionQuery] = useState('');
  const [hashtagQuery, setHashtagQuery] = useState('');
  const [cursorPosition, setCursorPosition] = useState(0);

  // Auto-resize textarea
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
    }
  }, []);

  // Handle text change with mention and hashtag detection
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    const cursorPos = e.target.selectionStart;
    
    if (newValue.length <= maxLength) {
      onChange(newValue);
      setCursorPosition(cursorPos);
      
      // Check for mentions (@)
      const beforeCursor = newValue.substring(0, cursorPos);
      const mentionMatch = beforeCursor.match(/@(\w*)$/);
      if (mentionMatch) {
        setMentionQuery(mentionMatch[1]);
        setShowMentions(true);
        setShowHashtags(false);
        onMentionSearch?.(mentionMatch[1]);
      }
      // Check for hashtags (#)
      else {
        const hashtagMatch = beforeCursor.match(/#(\w*)$/);
        if (hashtagMatch) {
          setHashtagQuery(hashtagMatch[1]);
          setShowHashtags(true);
          setShowMentions(false);
          onHashtagSearch?.(hashtagMatch[1]);
        } else {
          setShowMentions(false);
          setShowHashtags(false);
        }
      }
      
      adjustTextareaHeight();
    }
  };

  // Insert mention
  const insertMention = (mention: { id: string; username: string }) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const beforeCursor = value.substring(0, cursorPosition);
    const afterCursor = value.substring(cursorPosition);
    const beforeMention = beforeCursor.replace(/@\w*$/, '');
    
    const newValue = `${beforeMention}@${mention.username} ${afterCursor}`;
    onChange(newValue);
    setShowMentions(false);
    
    // Focus back to textarea
    setTimeout(() => {
      textarea.focus();
      const newCursorPos = beforeMention.length + mention.username.length + 2;
      textarea.setSelectionRange(newCursorPos, newCursorPos);
    }, 0);
  };

  // Insert hashtag
  const insertHashtag = (hashtag: { name: string }) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const beforeCursor = value.substring(0, cursorPosition);
    const afterCursor = value.substring(cursorPosition);
    const beforeHashtag = beforeCursor.replace(/#\w*$/, '');
    
    const newValue = `${beforeHashtag}#${hashtag.name} ${afterCursor}`;
    onChange(newValue);
    setShowHashtags(false);
    
    // Focus back to textarea
    setTimeout(() => {
      textarea.focus();
      const newCursorPos = beforeHashtag.length + hashtag.name.length + 2;
      textarea.setSelectionRange(newCursorPos, newCursorPos);
    }, 0);
  };

  // Format text (bold, italic, etc.)
  const formatText = (format: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);
    
    let formattedText = '';
    switch (format) {
      case 'bold':
        formattedText = `**${selectedText}**`;
        break;
      case 'italic':
        formattedText = `*${selectedText}*`;
        break;
      case 'underline':
        formattedText = `__${selectedText}__`;
        break;
      case 'code':
        formattedText = `\`${selectedText}\``;
        break;
      case 'quote':
        formattedText = `> ${selectedText}`;
        break;
      default:
        formattedText = selectedText;
    }

    const newValue = value.substring(0, start) + formattedText + value.substring(end);
    onChange(newValue);
    
    // Restore focus and selection
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + formattedText.length, start + formattedText.length);
    }, 0);
  };

  const remainingChars = maxLength - value.length;
  const isNearLimit = remainingChars < 100;

  return (
    <div className={`relative ${className}`}>
      {/* Formatting Toolbar */}
      <div className="flex items-center gap-1 p-2 border-b border-white/20 bg-white/5 backdrop-blur-sm rounded-t-lg">
        <button
          type="button"
          onClick={() => formatText('bold')}
          className="p-2 hover:bg-white/10 rounded transition-colors text-white/80 hover:text-white"
          title="Bold"
        >
          <Bold className="w-4 h-4" />
        </button>
        <button
          type="button"
          onClick={() => formatText('italic')}
          className="p-2 hover:bg-white/10 rounded transition-colors text-white/80 hover:text-white"
          title="Italic"
        >
          <Italic className="w-4 h-4" />
        </button>
        <button
          type="button"
          onClick={() => formatText('underline')}
          className="p-2 hover:bg-white/10 rounded transition-colors text-white/80 hover:text-white"
          title="Underline"
        >
          <Underline className="w-4 h-4" />
        </button>
        <button
          type="button"
          onClick={() => formatText('code')}
          className="p-2 hover:bg-white/10 rounded transition-colors text-white/80 hover:text-white"
          title="Code"
        >
          <Code className="w-4 h-4" />
        </button>
        <button
          type="button"
          onClick={() => formatText('quote')}
          className="p-2 hover:bg-white/10 rounded transition-colors text-white/80 hover:text-white"
          title="Quote"
        >
          <Quote className="w-4 h-4" />
        </button>

        <div className="w-px h-6 bg-white/20 mx-2" />

        <button
          type="button"
          className="p-2 hover:bg-white/10 rounded transition-colors text-white/80 hover:text-white"
          title="Add Link"
        >
          <Link className="w-4 h-4" />
        </button>
        <button
          type="button"
          className="p-2 hover:bg-white/10 rounded transition-colors text-white/80 hover:text-white"
          title="Add Image"
        >
          <Image className="w-4 h-4" />
        </button>
        <button
          type="button"
          className="p-2 hover:bg-white/10 rounded transition-colors text-white/80 hover:text-white"
          title="Add Emoji"
        >
          <Smile className="w-4 h-4" />
        </button>
      </div>

      {/* Text Area */}
      <div className="relative">
        <textarea
          ref={textareaRef}
          value={value}
          onChange={handleTextChange}
          placeholder={placeholder}
          disabled={disabled}
          className={`w-full p-4 border-0 resize-none focus:outline-none focus:ring-2 focus:ring-purple-500/50 rounded-b-lg min-h-[120px] text-white placeholder-white/50 ${
            isRTL ? 'text-right' : 'text-left'
          } ${disabled ? 'bg-white/5 cursor-not-allowed opacity-50' : 'bg-white/5 backdrop-blur-sm'}`}
          dir={isRTL ? 'rtl' : 'ltr'}
        />

        {/* Character Count */}
        <div className={`absolute bottom-2 ${isRTL ? 'left-2' : 'right-2'} text-sm ${
          isNearLimit ? 'text-red-400' : 'text-white/60'
        }`}>
          {remainingChars}
        </div>
      </div>

      {/* Mentions Dropdown */}
      {showMentions && mentions.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg shadow-lg max-h-48 overflow-y-auto">
          {mentions.map((mention) => (
            <button
              key={mention.id}
              onClick={() => insertMention(mention)}
              className="w-full px-4 py-2 text-left hover:bg-white/10 flex items-center gap-3 text-white transition-colors"
            >
              {mention.avatar && (
                <img src={mention.avatar} alt="" className="w-6 h-6 rounded-full" />
              )}
              <span>@{mention.username}</span>
            </button>
          ))}
        </div>
      )}

      {/* Hashtags Dropdown */}
      {showHashtags && hashtags.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg shadow-lg max-h-48 overflow-y-auto">
          {hashtags.map((hashtag) => (
            <button
              key={hashtag.name}
              onClick={() => insertHashtag(hashtag)}
              className="w-full px-4 py-2 text-left hover:bg-white/10 flex items-center justify-between text-white transition-colors"
            >
              <span>#{hashtag.name}</span>
              <span className="text-sm text-white/60">{hashtag.count} posts</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default EnhancedRichTextEditor;
