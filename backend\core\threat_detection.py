"""
Automated Threat Detection System
"""
import re
import time
import logging
from collections import defaultdict, deque
from datetime import datetime, timedelta
from django.utils import timezone
from django.contrib.auth.models import AnonymousUser
from django.core.cache import cache
import json

logger = logging.getLogger(__name__)


class ThreatDetector:
    """
    Advanced threat detection system for identifying security risks
    """
    
    def __init__(self):
        self.failed_login_attempts = defaultdict(deque)
        self.request_patterns = defaultdict(deque)
        self.suspicious_ips = set()
        
        # Threat patterns
        self.sql_injection_patterns = [
            r"(\bunion\b.*\bselect\b)",
            r"(\bselect\b.*\bfrom\b)",
            r"(\binsert\b.*\binto\b)",
            r"(\bdelete\b.*\bfrom\b)",
            r"(\bdrop\b.*\btable\b)",
            r"(\bor\b.*1\s*=\s*1)",
            r"(\band\b.*1\s*=\s*1)",
            r"(--|\#|\/\*)",
            r"(\bexec\b|\bexecute\b)",
            r"(\bsp_\w+)",
        ]
        
        self.xss_patterns = [
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"on\w+\s*=",
            r"<iframe[^>]*>",
            r"<object[^>]*>",
            r"<embed[^>]*>",
            r"<link[^>]*>",
            r"<meta[^>]*>",
        ]
        
        self.path_traversal_patterns = [
            r"\.\./",
            r"\.\.\\",
            r"%2e%2e%2f",
            r"%2e%2e\\",
            r"..%2f",
            r"..%5c",
        ]
        
        # Suspicious user agents
        self.suspicious_user_agents = [
            'sqlmap',
            'nikto',
            'nmap',
            'masscan',
            'burp',
            'owasp',
            'w3af',
            'acunetix',
            'nessus',
            'openvas',
        ]
    
    def is_suspicious_request(self, request):
        """
        Quick check if request is suspicious (for real-time blocking)
        """
        try:
            ip = self._get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
            path = request.path
            
            # Check for known malicious user agents
            if any(agent in user_agent for agent in self.suspicious_user_agents):
                return True
            
            # Check for SQL injection in path
            if any(re.search(pattern, path, re.IGNORECASE) for pattern in self.sql_injection_patterns):
                return True
            
            # Check for XSS in path
            if any(re.search(pattern, path, re.IGNORECASE) for pattern in self.xss_patterns):
                return True
            
            # Check for path traversal
            if any(re.search(pattern, path, re.IGNORECASE) for pattern in self.path_traversal_patterns):
                return True
            
            # Check request frequency
            if self._is_high_frequency_request(ip):
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error in suspicious request check: {str(e)}")
            return False
    
    def analyze_request(self, request):
        """
        Detailed analysis of suspicious request
        """
        threat_data = {
            'timestamp': timezone.now().isoformat(),
            'ip_address': self._get_client_ip(request),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'path': request.path,
            'method': request.method,
            'threats_detected': [],
            'risk_score': 0,
            'severity': 'low',
            'confidence': 50,
        }
        
        # Analyze different threat types
        self._check_sql_injection(request, threat_data)
        self._check_xss(request, threat_data)
        self._check_path_traversal(request, threat_data)
        self._check_brute_force(request, threat_data)
        self._check_anomalous_behavior(request, threat_data)
        
        # Calculate overall risk score and severity
        self._calculate_risk_score(threat_data)
        
        threat_data['description'] = self._generate_threat_description(threat_data)
        
        return threat_data
    
    def analyze_response(self, request, response):
        """
        Analyze response for security issues
        """
        try:
            # Check for information disclosure
            if response.status_code == 500:
                self._check_information_disclosure(request, response)
            
            # Check for authentication bypass
            if response.status_code == 200 and 'admin' in request.path:
                self._check_authentication_bypass(request, response)
            
            # Monitor failed login attempts
            if 'login' in request.path and response.status_code in [400, 401, 403]:
                self._track_failed_login(request)
            
        except Exception as e:
            logger.error(f"Error in response analysis: {str(e)}")
    
    def _get_client_ip(self, request):
        """Extract client IP from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def _check_sql_injection(self, request, threat_data):
        """Check for SQL injection patterns"""
        content_to_check = [
            request.path,
            str(request.GET),
            str(getattr(request, 'data', {})),
        ]
        
        for content in content_to_check:
            for pattern in self.sql_injection_patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    threat_data['threats_detected'].append('sql_injection')
                    threat_data['risk_score'] += 30
                    threat_data['confidence'] += 20
                    break
    
    def _check_xss(self, request, threat_data):
        """Check for XSS patterns"""
        content_to_check = [
            request.path,
            str(request.GET),
            str(getattr(request, 'data', {})),
        ]
        
        for content in content_to_check:
            for pattern in self.xss_patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    threat_data['threats_detected'].append('xss')
                    threat_data['risk_score'] += 25
                    threat_data['confidence'] += 15
                    break
    
    def _check_path_traversal(self, request, threat_data):
        """Check for path traversal patterns"""
        path = request.path
        
        for pattern in self.path_traversal_patterns:
            if re.search(pattern, path, re.IGNORECASE):
                threat_data['threats_detected'].append('path_traversal')
                threat_data['risk_score'] += 35
                threat_data['confidence'] += 25
                break
    
    def _check_brute_force(self, request, threat_data):
        """Check for brute force attack patterns"""
        ip = threat_data['ip_address']
        
        # Check failed login attempts
        if 'login' in request.path:
            failed_attempts = len(self.failed_login_attempts.get(ip, []))
            if failed_attempts > 5:
                threat_data['threats_detected'].append('brute_force')
                threat_data['risk_score'] += 40
                threat_data['confidence'] += 30
    
    def _check_anomalous_behavior(self, request, threat_data):
        """Check for anomalous behavior patterns"""
        ip = threat_data['ip_address']
        user_agent = threat_data['user_agent'].lower()
        
        # Check for suspicious user agents
        if any(agent in user_agent for agent in self.suspicious_user_agents):
            threat_data['threats_detected'].append('malicious_tool')
            threat_data['risk_score'] += 50
            threat_data['confidence'] += 40
        
        # Check request frequency
        if self._is_high_frequency_request(ip):
            threat_data['threats_detected'].append('high_frequency')
            threat_data['risk_score'] += 20
            threat_data['confidence'] += 10
    
    def _is_high_frequency_request(self, ip):
        """Check if IP is making high frequency requests"""
        now = time.time()
        window = 60  # 1 minute window
        max_requests = 50  # Max requests per minute for normal behavior
        
        # Get request history for this IP
        requests = self.request_patterns[ip]
        
        # Remove old requests outside the window
        while requests and now - requests[0] > window:
            requests.popleft()
        
        # Add current request
        requests.append(now)
        
        return len(requests) > max_requests
    
    def _track_failed_login(self, request):
        """Track failed login attempts"""
        ip = self._get_client_ip(request)
        now = time.time()
        
        # Clean old attempts (older than 1 hour)
        attempts = self.failed_login_attempts[ip]
        while attempts and now - attempts[0] > 3600:
            attempts.popleft()
        
        # Add current attempt
        attempts.append(now)
        
        # If too many failed attempts, mark IP as suspicious
        if len(attempts) > 10:
            self.suspicious_ips.add(ip)
    
    def _check_information_disclosure(self, request, response):
        """Check for information disclosure in error responses"""
        if hasattr(response, 'content'):
            content = response.content.decode('utf-8', errors='ignore').lower()
            
            # Check for sensitive information in error messages
            sensitive_patterns = [
                'traceback',
                'stack trace',
                'database error',
                'sql error',
                'file not found',
                'permission denied',
                'access denied',
            ]
            
            for pattern in sensitive_patterns:
                if pattern in content:
                    logger.warning(f"Information disclosure detected: {pattern} in response to {request.path}")
                    break
    
    def _check_authentication_bypass(self, request, response):
        """Check for potential authentication bypass"""
        user = request.user if hasattr(request, 'user') else None
        
        if isinstance(user, AnonymousUser) or not user:
            if 'admin' in request.path and response.status_code == 200:
                logger.critical(f"Potential authentication bypass: Anonymous access to {request.path}")
    
    def _calculate_risk_score(self, threat_data):
        """Calculate overall risk score and severity"""
        risk_score = min(threat_data['risk_score'], 100)
        threat_data['risk_score'] = risk_score
        
        if risk_score >= 80:
            threat_data['severity'] = 'critical'
        elif risk_score >= 60:
            threat_data['severity'] = 'high'
        elif risk_score >= 40:
            threat_data['severity'] = 'medium'
        else:
            threat_data['severity'] = 'low'
    
    def _generate_threat_description(self, threat_data):
        """Generate human-readable threat description"""
        threats = threat_data['threats_detected']
        ip = threat_data['ip_address']
        path = threat_data['path']
        
        if not threats:
            return f"Suspicious activity detected from {ip} accessing {path}"
        
        threat_names = {
            'sql_injection': 'SQL Injection',
            'xss': 'Cross-Site Scripting (XSS)',
            'path_traversal': 'Path Traversal',
            'brute_force': 'Brute Force Attack',
            'malicious_tool': 'Malicious Tool Usage',
            'high_frequency': 'High Frequency Requests',
        }
        
        threat_list = [threat_names.get(threat, threat) for threat in threats]
        
        if len(threat_list) == 1:
            return f"{threat_list[0]} detected from {ip} targeting {path}"
        else:
            return f"Multiple threats detected from {ip}: {', '.join(threat_list)} targeting {path}"
