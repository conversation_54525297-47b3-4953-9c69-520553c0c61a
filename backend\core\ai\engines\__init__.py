"""
AI Engines Package
Consolidated ML and analytics engines
"""

# Lazy imports to avoid loading heavy ML dependencies unless needed
def get_predictive_engine():
    """Get predictive analytics engine instance"""
    try:
        from .predictive_engine import PredictiveAnalyticsEngine
        return PredictiveAnalyticsEngine.get_instance()
    except ImportError as e:
        import logging
        logging.warning(f"Predictive engine not available: {e}")
        return None

def get_computer_vision_engine():
    """Get computer vision engine instance"""
    try:
        from .computer_vision import ComputerVisionEngine
        return ComputerVisionEngine()
    except ImportError as e:
        import logging
        logging.warning(f"Computer vision engine not available: {e}")
        return None

def get_voice_ai_engine():
    """Get voice AI engine instance"""
    try:
        from .voice_ai import VoiceAIEngine
        return VoiceAIEngine()
    except ImportError as e:
        import logging
        logging.warning(f"Voice AI engine not available: {e}")
        return None

__all__ = [
    'get_predictive_engine',
    'get_computer_vision_engine', 
    'get_voice_ai_engine'
]
