import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Search, X, Filter, Bell, Plus } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { useAuth } from '../../hooks/useAuth';
import { useIsMobile } from '../../hooks/useIsMobile';
import { ConnectionStatus } from './CommunityLoadingStates';
import { useCommunityStats } from '../../hooks/useCommunityStats';

interface CommunityHeaderProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onClearSearch: () => void;
  onCreatePost: () => void;
  connectionStatus: 'connected' | 'connecting' | 'disconnected';
  isSearching: boolean;
  showGuestBanner: boolean;
}

const CommunityHeader: React.FC<CommunityHeaderProps> = memo(({
  searchQuery,
  onSearchChange,
  onClearSearch,
  onCreatePost,
  connectionStatus,
  isSearching,
  showGuestBanner
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAuth();
  const { stats } = useCommunityStats();
  const isMobile = useIsMobile();

  // Connection status is now handled by the ConnectionStatus component

  return (
    <>
      {/* Enhanced Guest User Banner */}
      {showGuestBanner && (
        <div
          className="bg-gradient-to-r from-purple-600/20 via-blue-600/20 to-indigo-600/20 border-b border-white/10 backdrop-blur-sm"
          role="banner"
          aria-label={t('community.navigation.guestBanner', 'Guest welcome banner')}
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 py-4">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center animate-pulse">
                  <span className="text-xl">👥</span>
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-white mb-1">
                    {t('community.page.guestWelcomeTitle', 'Welcome to Yasmeen AI Community!')}
                  </h3>
                  <p className="text-sm text-gray-300 leading-relaxed">
                    {t('community.page.guestWelcomeMessage', 'Join our vibrant community of Syrian data scientists and AI enthusiasts. Connect, share knowledge, and grow together.')}
                  </p>
                  <div className="flex items-center gap-4 mt-2 text-xs text-gray-400">
                    <span className="flex items-center gap-1">
                      <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
                      {t('community.page.membersOnline', '{{count}} members online', {
                        count: stats?.online_users_count || stats?.active_users_today || 0
                      })}
                    </span>
                    <span>•</span>
                    <span>{t('community.page.postsToday', '{{count}} posts today', {
                      count: stats?.posts_today || 0
                    })}</span>
                  </div>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                <button
                  onClick={() => window.location.href = '/register'}
                  className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 hover:scale-105 hover:shadow-lg flex items-center justify-center gap-2"
                >
                  <span>✨</span>
                  {t('community.page.joinButton', 'Join Community')}
                </button>
                <button
                  onClick={() => window.location.href = '/login'}
                  className="bg-white/10 hover:bg-white/20 border border-white/20 hover:border-white/30 px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 hover:scale-105 backdrop-blur-sm"
                >
                  {t('community.page.loginButton', 'Sign In')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <header
        className="bg-black/20 backdrop-blur-xl border-b border-white/10 sticky top-0 z-40"
        role="banner"
        aria-label={t('community.navigation.mainHeader', 'Community main header')}
        data-testid="community-header"
      >
        <div className={`max-w-7xl mx-auto px-4 sm:px-6 py-4 ${isMobile ? 'px-2 py-2' : ''}`}>
          <div className={`flex ${isMobile ? 'flex-col gap-2' : 'flex-col sm:flex-row items-start sm:items-center justify-between gap-4'}`}>
            {/* Title and Connection Status */}
            <div className="flex items-center gap-4">
              <ConnectionStatus status={connectionStatus} />
              <div className="hidden sm:block w-px h-6 bg-white/20"></div>
              <div>
                <h1 className="text-lg sm:text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent truncate">
                  {t('community.page.title')}
                </h1>
              </div>
            </div>

            {/* Search and Actions */}
            <div className={`flex items-center gap-3 ${isMobile ? 'w-full' : 'w-full sm:w-auto'}`}>
              {/* Search */}
              <div className={`relative ${isMobile ? 'flex-1' : 'flex-1 sm:flex-initial sm:w-80'}`}>
                <Search className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400`} />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => onSearchChange(e.target.value)}
                  placeholder={t('community.navigation.search')}
                  id="search"
                  name="search"
                  role="searchbox"
                  aria-label={t('community.navigation.searchLabel', 'Search community posts')}
                  aria-describedby="search-help"
                  aria-expanded={searchQuery.length > 0}
                  autoComplete="off"
                  className={`w-full bg-white/10 border border-white/20 rounded-xl ${isRTL ? 'pr-10 pl-20' : 'pl-10 pr-20'} py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 focus:bg-white/15 focus:ring-2 focus:ring-purple-400/50 transition-all duration-300`}
                />
                <div id="search-help" className="sr-only">
                  {t('community.navigation.searchHelp', 'Search for posts, users, or topics in the community')}
                </div>

                {/* Search Actions */}
                {searchQuery && (
                  <div className={`absolute ${isRTL ? 'left-12' : 'right-12'} top-1/2 transform -translate-y-1/2`}>
                    {isSearching ? (
                      <div className="animate-spin w-4 h-4 border-2 border-purple-400 border-t-transparent rounded-full"></div>
                    ) : (
                      <button
                        onClick={onClearSearch}
                        className={`absolute ${isRTL ? 'left-12' : 'right-12'} top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-white transition-colors`}
                        aria-label="Clear search"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                )}
                
                <button
                  className={`absolute ${isRTL ? 'left-3' : 'right-3'} top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-purple-400 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-400 rounded`}
                  aria-label={t('community.navigation.searchFilters', 'Open search filters')}
                  aria-expanded="false"
                  aria-haspopup="menu"
                >
                  <Filter className="w-4 h-4" />
                </button>
              </div>

              {/* Create Post Button */}
              <button
                onClick={onCreatePost}
                className={`bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 ${isMobile ? 'px-4 py-3 min-h-[48px]' : 'px-3 py-2 sm:px-4 sm:py-2'} rounded-lg font-medium transition-all duration-200 flex items-center gap-2 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 focus:ring-offset-transparent`}
                aria-label={t('community.actions.createPost', 'Create a new post')}
                type="button"
              >
                <Plus className="w-4 h-4" aria-hidden="true" />
                <span className={isMobile ? 'text-sm' : 'hidden sm:inline'}>{t('community.actions.newPost', 'New Post')}</span>
                {!isMobile && <span className="sm:hidden">{t('community.actions.createPost', 'Create')}</span>}
              </button>

              {/* User Profile - Only show for authenticated users */}
              {!showGuestBanner && user && (
                <div className={`flex items-center gap-3 ${isRTL ? 'mr-4' : 'ml-4'}`}>
                  <div className="relative">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-white font-medium text-sm">
                      {user?.first_name?.[0] || user?.username?.[0] || 'U'}
                    </div>
                    <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-gray-900 rounded-full"></div>
                  </div>
                  <div className="hidden sm:block">
                    <div className="text-sm font-medium text-white">
                      {user?.first_name || user?.username || 'User'}
                    </div>
                    <div className="text-xs text-gray-400">
                      {user?.role || 'Member'}
                    </div>
                  </div>
                  <button className={`${isRTL ? 'mr-auto' : 'ml-auto'} text-gray-400 hover:text-white`}>
                    <Bell className="w-4 h-4" />
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>
    </>
  );
});

CommunityHeader.displayName = 'CommunityHeader';

export default CommunityHeader;
