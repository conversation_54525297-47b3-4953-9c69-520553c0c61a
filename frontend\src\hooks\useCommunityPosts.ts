import { useState, useEffect, useCallback, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { RootState } from '../store';
import { setActiveView, setSearchQuery } from '../store/forumSlice';
import { communityApi, type CommunityPost } from '../services/communityApi';
import { debounce } from '../utils/communityUtils';
import { useToast } from './useToast';
import { useAuth } from './useAuth';
import { useRateLimit, getRateLimitMessage } from '../utils/rateLimiter';
import { sanitizePostContent, sanitizeCommentContent, sanitizeSearchQuery } from '../utils/inputSanitizer';
import { useLanguage } from './useLanguage';

export interface UseCommunityPostsReturn {
  // Data
  posts: CommunityPost[];
  searchResults: CommunityPost[];
  displayPosts: CommunityPost[];
  activeView: string;
  searchQuery: string;
  
  // Loading states
  isLoading: boolean;
  isSearching: boolean;
  
  // Computed stats
  computedStats: {
    trending_posts_count: number;
    verified_authors_count: number;
    saved_posts_count: number;
  };
  
  // Actions
  handleViewChange: (view: string) => void;
  handleSearchChange: (query: string) => void;
  handleClearSearch: () => void;
  refreshPosts: () => Promise<void>;
  
  // Post operations
  handleLikePost: (postId: string) => Promise<void>;
  handleCreatePost: (postData: any) => Promise<void>;
  handleCommentPost: (postId: string, content: string, parentId?: string) => Promise<void>;
  handleSharePost: (postId: string) => Promise<void>;
  handleSavePost: (postId: string) => Promise<void>;
  handleEditPost: (postId: string, updatedPost: CommunityPost) => Promise<void>;
  handleDeletePost: (postId: string) => Promise<void>;
  handleReportPost: (postId: string) => Promise<void>;

  // Comment operations
  handleLikeComment: (commentId: string) => Promise<void>;
  handleEditComment: (commentId: string, content: string) => Promise<void>;
  handleDeleteComment: (commentId: string) => Promise<void>;
  handleReportComment: (commentId: string) => Promise<void>;
}

export const useCommunityPosts = (): UseCommunityPostsReturn => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { isAuthenticated, user } = useAuth();
  const dispatch = useDispatch();
  const { showSuccess, showError, showInfo } = useToast();
  const { checkRateLimit } = useRateLimit();
  
  // Redux state
  const { activeView, searchQuery } = useSelector((state: RootState) => state.forum);
  
  // Local state
  const [posts, setPosts] = useState<CommunityPost[]>([]);
  const [searchResults, setSearchResults] = useState<CommunityPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSearching, setIsSearching] = useState(false);

  // Computed values
  const displayPosts = useMemo(() => {
    return searchQuery.trim() ? searchResults : posts;
  }, [searchQuery, searchResults, posts]);

  const computedStats = useMemo(() => {
    // Ensure posts is an array before filtering
    const postsArray = Array.isArray(posts) ? posts : [];
    return {
      trending_posts_count: postsArray.filter(p => p.like_count > 10).length,
      verified_authors_count: postsArray.filter(p => p.author?.is_verified).length,
      saved_posts_count: postsArray.filter(p => p.is_saved).length,
    };
  }, [posts]);

  // Debounced search function with rate limiting and sanitization
  const debouncedSearch = useCallback(
    debounce(async (query: string) => {
      if (!query.trim()) {
        setSearchResults([]);
        setIsSearching(false);
        return;
      }

      // Rate limiting check
      if (user) {
        const rateLimitResult = checkRateLimit(user.id.toString(), 'SEARCH_ACTION');
        if (!rateLimitResult.allowed) {
          const message = getRateLimitMessage('SEARCH_ACTION', rateLimitResult.retryAfter || 0, isRTL);
          showError(message);
          setIsSearching(false);
          return;
        }
      }

      // Sanitize search query
      const sanitizedQuery = sanitizeSearchQuery(query);
      if (!sanitizedQuery) {
        showError(t('community.messages.invalidSearchQuery'));
        setIsSearching(false);
        return;
      }

      setIsSearching(true);
      try {
        const results = await communityApi.searchPosts(sanitizedQuery);
        setSearchResults(results);
      } catch (error) {
        console.error('Search error:', error);
        showError(t('community.messages.searchError'));
      } finally {
        setIsSearching(false);
      }
    }, 300),
    [showError, t, user, checkRateLimit, isRTL]
  );

  // Load posts based on active view
  const loadPosts = useCallback(async () => {
    setIsLoading(true);
    try {
      let postsData;

      console.log('🎯 Loading posts for view:', activeView);

      // Load different data based on active view
      switch (activeView) {
        case 'trending':
          try {
            postsData = await communityApi.getTrendingPosts();
          } catch (error) {
            console.log('Trending posts not available, falling back to regular posts');
            postsData = await communityApi.getPosts();
          }
          break;

        case 'following':
          try {
            postsData = await communityApi.getFollowingPosts();
          } catch (error) {
            console.log('Following posts not available, falling back to regular posts');
            postsData = await communityApi.getPosts();
          }
          break;

        case 'saved':
          try {
            postsData = await communityApi.getSavedPosts();
          } catch (error) {
            console.log('Saved posts not available, falling back to regular posts');
            postsData = await communityApi.getPosts();
          }
          break;

        case 'feed':
        default:
          postsData = await communityApi.getPosts();
          break;
      }

      // Handle paginated response - extract results array
      const postsArray = Array.isArray(postsData) ? postsData : (postsData?.results || []);
      setPosts(postsArray);
      console.log('✅ Loaded', postsArray.length, 'posts for', activeView);
    } catch (error) {
      console.error('Failed to load posts:', error);
      showError(t('community.messages.loadError'));
    } finally {
      setIsLoading(false);
    }
  }, [activeView, showError, t]);

  // Refresh posts
  const refreshPosts = useCallback(async () => {
    try {
      const postsData = await communityApi.getPosts();
      // Handle paginated response - extract results array
      const postsArray = Array.isArray(postsData) ? postsData : (postsData?.results || []);
      setPosts(postsArray);
    } catch (error) {
      console.error('Failed to refresh posts:', error);
      showError(t('community.messages.refreshError'));
    }
  }, [showError, t]);

  // Load posts when component mounts or when activeView changes
  useEffect(() => {
    loadPosts();
  }, [loadPosts]); // Reload when activeView changes (via loadPosts dependency)

  // Handler functions
  const handleViewChange = useCallback((view: string) => {
    console.log('🎯 Changing view to:', view);
    dispatch(setActiveView(view as 'feed' | 'trending' | 'following' | 'saved'));
    // Posts will be reloaded automatically via useEffect dependency on loadPosts
  }, [dispatch]);

  const handleSearchChange = useCallback((query: string) => {
    dispatch(setSearchQuery(query));
    debouncedSearch(query);
  }, [dispatch, debouncedSearch]);

  const handleClearSearch = useCallback(() => {
    dispatch(setSearchQuery(''));
    setSearchResults([]);
  }, [dispatch]);

  const handleLikePost = useCallback(async (postId: string) => {
    if (!isAuthenticated || !user) {
      showInfo(t('community.messages.loginRequired'));
      return;
    }

    // Rate limiting check
    const rateLimitResult = checkRateLimit(user.id.toString(), 'LIKE_ACTION');
    if (!rateLimitResult.allowed) {
      const message = getRateLimitMessage('LIKE_ACTION', rateLimitResult.retryAfter || 0, isRTL);
      showError(message);
      return;
    }

    try {
      await communityApi.likePost(postId);
      setPosts(prev => prev.map(post =>
        post.id === postId
          ? {
              ...post,
              is_liked: !post.is_liked,
              like_count: post.is_liked ? post.like_count - 1 : post.like_count + 1
            }
          : post
      ));
    } catch (error) {
      console.error('Like error:', error);
      showError(t('community.messages.likeError'));
    }
  }, [isAuthenticated, user, showInfo, showError, t, checkRateLimit, isRTL]);

  const handleCreatePost = useCallback(async (postData: any) => {
    if (!isAuthenticated || !user) {
      showInfo(t('community.messages.loginToCreate'));
      return;
    }

    // Rate limiting check
    const rateLimitResult = checkRateLimit(user.id.toString(), 'POST_CREATE');
    if (!rateLimitResult.allowed) {
      const message = getRateLimitMessage('POST_CREATE', rateLimitResult.retryAfter || 0, isRTL);
      showError(message);
      return;
    }

    // Sanitize post content
    const sanitizedData = {
      ...postData,
      content: sanitizePostContent(postData.content || ''),
      title: postData.title ? sanitizePostContent(postData.title) : undefined
    };

    // Validate sanitized content
    if (!sanitizedData.content.trim()) {
      showError(t('community.messages.emptyPostContent'));
      return;
    }

    try {
      await communityApi.createPost(sanitizedData);
      showSuccess(t('community.messages.postCreated'));
      await refreshPosts();
    } catch (error) {
      console.error('Create post error:', error);
      showError(t('community.messages.createError'));
    }
  }, [isAuthenticated, user, showInfo, showSuccess, showError, t, refreshPosts, checkRateLimit, isRTL]);

  const handleCommentPost = useCallback(async (postId: string, content: string, parentId?: string) => {
    if (!isAuthenticated || !user) {
      showInfo(t('community.messages.loginToComment'));
      return;
    }

    // Rate limiting check
    const rateLimitResult = checkRateLimit(user.id.toString(), 'COMMENT_CREATE');
    if (!rateLimitResult.allowed) {
      const message = getRateLimitMessage('COMMENT_CREATE', rateLimitResult.retryAfter || 0, isRTL);
      showError(message);
      return;
    }

    // Sanitize comment content
    const sanitizedContent = sanitizeCommentContent(content);
    if (!sanitizedContent.trim()) {
      showError(t('community.messages.emptyCommentContent'));
      return;
    }

    try {
      console.log('🎯 REAL SYSTEM: Creating comment with:', {
        postId,
        content: sanitizedContent,
        parentId
      });

      await communityApi.createComment({
        post: postId,
        content: sanitizedContent,
        parent: parentId
      });

      console.log('✅ REAL SYSTEM: Comment created successfully');
      showSuccess(t('community.messages.commentAdded'));
      await refreshPosts();
    } catch (error) {
      console.error('❌ REAL SYSTEM: Comment error:', error);
      showError(t('community.messages.commentError'));
    }
  }, [isAuthenticated, user, showInfo, showSuccess, showError, t, refreshPosts, checkRateLimit, isRTL]);

  const handleSharePost = useCallback(async (postId: string) => {
    if (!isAuthenticated) {
      showInfo(t('community.messages.loginRequired'));
      return;
    }
    
    try {
      // Copy link to clipboard
      const postUrl = `${window.location.origin}/community/post/${postId}`;
      await navigator.clipboard.writeText(postUrl);
      showSuccess(t('community.messages.linkCopied'));
    } catch (error) {
      console.error('Share error:', error);
      showError(t('community.messages.shareError'));
    }
  }, [isAuthenticated, showInfo, showSuccess, showError, t]);

  const handleSavePost = useCallback(async (postId: string) => {
    if (!isAuthenticated) {
      showInfo(t('community.messages.loginRequired'));
      return;
    }
    
    try {
      await communityApi.savePost(postId);
      setPosts(prev => prev.map(post =>
        post.id === postId
          ? { ...post, is_saved: !post.is_saved }
          : post
      ));
      showSuccess(t('community.messages.postSaved'));
    } catch (error) {
      console.error('Save error:', error);
      showError(t('community.messages.saveError'));
    }
  }, [isAuthenticated, showInfo, showSuccess, showError, t]);

  const handleEditPost = useCallback(async (postId: string, updatedPost: CommunityPost) => {
    if (!isAuthenticated) {
      showInfo(t('community.messages.loginRequired'));
      return;
    }

    try {
      const updated = await communityApi.updatePost(postId, updatedPost);
      setPosts(prev => prev.map(post =>
        post.id === postId ? updated : post
      ));
      showSuccess(t('community.messages.postUpdated', 'Post updated successfully'));
    } catch (error) {
      console.error('Edit error:', error);
      showError(t('community.messages.editError', 'Failed to update post'));
    }
  }, [isAuthenticated, showInfo, showSuccess, showError, t]);

  const handleDeletePost = useCallback(async (postId: string) => {
    if (!isAuthenticated) {
      showInfo(t('community.messages.loginRequired'));
      return;
    }

    try {
      await communityApi.deletePost(postId);
      setPosts(prev => prev.filter(post => post.id !== postId));
      showSuccess(t('community.messages.postDeleted', 'Post deleted successfully'));
    } catch (error) {
      console.error('Delete error:', error);
      showError(t('community.messages.deleteError', 'Failed to delete post'));
    }
  }, [isAuthenticated, showInfo, showSuccess, showError, t]);

  const handleReportPost = useCallback(async (postId: string) => {
    if (!isAuthenticated) {
      showInfo(t('community.messages.loginRequired'));
      return;
    }

    try {
      await communityApi.reportPost(postId);
      showSuccess(t('community.messages.postReported', 'Post reported successfully'));
    } catch (error) {
      console.error('Report error:', error);
      showError(t('community.messages.reportError', 'Failed to report post'));
    }
  }, [isAuthenticated, showInfo, showSuccess, showError, t]);

  const handleLikeComment = useCallback(async (commentId: string) => {
    if (!isAuthenticated) {
      showInfo(t('community.messages.loginRequired'));
      return;
    }

    try {
      await communityApi.likeComment(commentId);
      await refreshPosts(); // Refresh to get updated comment like counts
    } catch (error) {
      console.error('Like comment error:', error);
      showError(t('community.messages.likeError', 'Failed to like comment'));
    }
  }, [isAuthenticated, showInfo, showError, t, refreshPosts]);

  const handleEditComment = useCallback(async (commentId: string, content: string) => {
    if (!isAuthenticated) {
      showInfo(t('community.messages.loginRequired'));
      return;
    }

    // Sanitize comment content
    const sanitizedContent = sanitizeCommentContent(content);
    if (!sanitizedContent.trim()) {
      showError(t('community.messages.emptyCommentContent'));
      return;
    }

    try {
      await communityApi.updateComment(commentId, { content: sanitizedContent });
      showSuccess(t('community.messages.commentUpdated', 'Comment updated successfully'));
      await refreshPosts();
    } catch (error) {
      console.error('Edit comment error:', error);
      showError(t('community.messages.editCommentError', 'Failed to update comment'));
    }
  }, [isAuthenticated, showInfo, showSuccess, showError, t, refreshPosts]);

  const handleDeleteComment = useCallback(async (commentId: string) => {
    if (!isAuthenticated) {
      showInfo(t('community.messages.loginRequired'));
      return;
    }

    try {
      await communityApi.deleteComment(commentId);
      showSuccess(t('community.messages.commentDeleted', 'Comment deleted successfully'));
      await refreshPosts();
    } catch (error) {
      console.error('Delete comment error:', error);
      showError(t('community.messages.deleteCommentError', 'Failed to delete comment'));
    }
  }, [isAuthenticated, showInfo, showSuccess, showError, t, refreshPosts]);

  const handleReportComment = useCallback(async (commentId: string) => {
    if (!isAuthenticated) {
      showInfo(t('community.messages.loginRequired'));
      return;
    }

    try {
      await communityApi.reportComment(commentId);
      showSuccess(t('community.messages.commentReported', 'Comment reported successfully'));
    } catch (error) {
      console.error('Report comment error:', error);
      showError(t('community.messages.reportCommentError', 'Failed to report comment'));
    }
  }, [isAuthenticated, showInfo, showSuccess, showError, t]);

  return {
    // Data
    posts,
    searchResults,
    displayPosts,
    activeView,
    searchQuery,
    
    // Loading states
    isLoading,
    isSearching,
    
    // Computed stats
    computedStats,
    
    // Actions
    handleViewChange,
    handleSearchChange,
    handleClearSearch,
    refreshPosts,
    
    // Post operations
    handleLikePost,
    handleCreatePost,
    handleCommentPost,
    handleSharePost,
    handleSavePost,
    handleEditPost,
    handleDeletePost,
    handleReportPost,

    // Comment operations
    handleLikeComment,
    handleEditComment,
    handleDeleteComment,
    handleReportComment,
  };
};
