/**
 * Rate Limiting Utility
 * 
 * Implements client-side rate limiting to prevent abuse and spam.
 * This works in conjunction with server-side rate limiting for comprehensive protection.
 */

export interface RateLimitConfig {
  maxRequests: number;
  windowMs: number;
  keyPrefix: string;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
}

class RateLimiter {
  private storage: Storage;

  constructor(storage: Storage = localStorage) {
    this.storage = storage;
  }

  /**
   * Check if a request is allowed based on rate limiting rules
   */
  checkLimit(userId: string, action: string, config: RateLimitConfig): RateLimitResult {
    const key = `${config.keyPrefix}_${userId}_${action}`;
    const now = Date.now();
    const windowStart = now - config.windowMs;

    // Get existing requests from storage
    const storedData = this.storage.getItem(key);
    let requests: number[] = [];

    if (storedData) {
      try {
        requests = JSON.parse(storedData);
      } catch (error) {
        console.warn('Failed to parse rate limit data:', error);
        requests = [];
      }
    }

    // Filter out requests outside the current window
    requests = requests.filter(timestamp => timestamp > windowStart);

    // Check if limit is exceeded
    if (requests.length >= config.maxRequests) {
      const oldestRequest = Math.min(...requests);
      const retryAfter = Math.ceil((oldestRequest + config.windowMs - now) / 1000);
      
      return {
        allowed: false,
        remaining: 0,
        resetTime: oldestRequest + config.windowMs,
        retryAfter
      };
    }

    // Add current request
    requests.push(now);
    
    // Store updated requests
    try {
      this.storage.setItem(key, JSON.stringify(requests));
    } catch (error) {
      console.warn('Failed to store rate limit data:', error);
    }

    return {
      allowed: true,
      remaining: config.maxRequests - requests.length,
      resetTime: now + config.windowMs
    };
  }

  /**
   * Clear rate limit data for a specific user and action
   */
  clearLimit(userId: string, action: string, keyPrefix: string): void {
    const key = `${keyPrefix}_${userId}_${action}`;
    this.storage.removeItem(key);
  }

  /**
   * Clear all rate limit data (useful for logout)
   */
  clearAllLimits(): void {
    const keys = Object.keys(this.storage);
    keys.forEach(key => {
      if (key.startsWith('rate_limit_')) {
        this.storage.removeItem(key);
      }
    });
  }
}

// Rate limit configurations for different actions
export const RATE_LIMITS = {
  POST_CREATE: {
    maxRequests: 5,
    windowMs: 60 * 60 * 1000, // 1 hour
    keyPrefix: 'rate_limit'
  },
  COMMENT_CREATE: {
    maxRequests: 30,
    windowMs: 60 * 60 * 1000, // 1 hour
    keyPrefix: 'rate_limit'
  },
  LIKE_ACTION: {
    maxRequests: 100,
    windowMs: 60 * 60 * 1000, // 1 hour
    keyPrefix: 'rate_limit'
  },
  SEARCH: {
    maxRequests: 60,
    windowMs: 60 * 1000, // 1 minute
    keyPrefix: 'rate_limit'
  },
  SEARCH_ACTION: {
    maxRequests: 60,
    windowMs: 60 * 1000, // 1 minute
    keyPrefix: 'rate_limit'
  },
  SHARE_ACTION: {
    maxRequests: 50,
    windowMs: 60 * 60 * 1000, // 1 hour
    keyPrefix: 'rate_limit'
  },
  SAVE_ACTION: {
    maxRequests: 100,
    windowMs: 60 * 60 * 1000, // 1 hour
    keyPrefix: 'rate_limit'
  },
  REPORT_ACTION: {
    maxRequests: 10,
    windowMs: 60 * 60 * 1000, // 1 hour
    keyPrefix: 'rate_limit'
  }
} as const;

// Global rate limiter instance
export const rateLimiter = new RateLimiter();

/**
 * Hook for using rate limiting in React components
 */
export const useRateLimit = () => {
  const checkRateLimit = (userId: string, action: keyof typeof RATE_LIMITS): RateLimitResult => {
    const config = RATE_LIMITS[action];
    return rateLimiter.checkLimit(userId, action, config);
  };

  const clearRateLimit = (userId: string, action: keyof typeof RATE_LIMITS): void => {
    const config = RATE_LIMITS[action];
    rateLimiter.clearLimit(userId, action, config.keyPrefix);
  };

  const clearAllRateLimits = (): void => {
    rateLimiter.clearAllLimits();
  };

  return {
    checkRateLimit,
    clearRateLimit,
    clearAllRateLimits
  };
};

/**
 * Rate limit error class
 */
export class RateLimitError extends Error {
  public retryAfter: number;
  public remaining: number;

  constructor(message: string, retryAfter: number, remaining: number) {
    super(message);
    this.name = 'RateLimitError';
    this.retryAfter = retryAfter;
    this.remaining = remaining;
  }
}

/**
 * Utility function to format retry after time
 */
export const formatRetryAfter = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds} second${seconds !== 1 ? 's' : ''}`;
  }
  
  const minutes = Math.ceil(seconds / 60);
  return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
};

/**
 * Utility function to check if rate limit error should show user-friendly message
 */
export const getRateLimitMessage = (action: keyof typeof RATE_LIMITS, retryAfter: number, isRTL: boolean = false): string => {
  const timeStr = formatRetryAfter(retryAfter);
  
  const messages = {
    POST_CREATE: isRTL
      ? `لقد تجاوزت حد إنشاء المنشورات. حاول مرة أخرى خلال ${timeStr}`
      : `You've exceeded the post creation limit. Try again in ${timeStr}`,
    COMMENT_CREATE: isRTL
      ? `لقد تجاوزت حد إضافة التعليقات. حاول مرة أخرى خلال ${timeStr}`
      : `You've exceeded the comment limit. Try again in ${timeStr}`,
    LIKE_ACTION: isRTL
      ? `لقد تجاوزت حد الإعجابات. حاول مرة أخرى خلال ${timeStr}`
      : `You've exceeded the like limit. Try again in ${timeStr}`,
    SEARCH: isRTL
      ? `لقد تجاوزت حد البحث. حاول مرة أخرى خلال ${timeStr}`
      : `You've exceeded the search limit. Try again in ${timeStr}`,
    SEARCH_ACTION: isRTL
      ? `لقد تجاوزت حد البحث. حاول مرة أخرى خلال ${timeStr}`
      : `You've exceeded the search limit. Try again in ${timeStr}`,
    SHARE_ACTION: isRTL
      ? `لقد تجاوزت حد المشاركة. حاول مرة أخرى خلال ${timeStr}`
      : `You've exceeded the share limit. Try again in ${timeStr}`,
    SAVE_ACTION: isRTL
      ? `لقد تجاوزت حد الحفظ. حاول مرة أخرى خلال ${timeStr}`
      : `You've exceeded the save limit. Try again in ${timeStr}`,
    REPORT_ACTION: isRTL
      ? `لقد تجاوزت حد الإبلاغ. حاول مرة أخرى خلال ${timeStr}`
      : `You've exceeded the report limit. Try again in ${timeStr}`
  };

  return messages[action];
};
