"""
Community Serializers
API serializers for community features
"""

from rest_framework import serializers
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from .models import (
    CommunityPost, PostSave, PostShare, CommunityComment,
    UserFollow, UserActivity, OnlineUser, CommunityStats
)
from users.serializers import UserSerializer
from api.serializers import TagSerializer
from .security import sanitize_community_content, validate_community_content, security_logger


class CommunityAuthorSerializer(serializers.ModelSerializer):
    """Simplified user serializer for community posts"""
    full_name = serializers.SerializerMethodField()
    avatar = serializers.SerializerMethodField()
    is_verified = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'full_name', 'avatar', 'is_verified']
    
    def get_full_name(self, obj):
        return f"{obj.first_name} {obj.last_name}".strip() or obj.username
    
    def get_avatar(self, obj):
        # Try multiple profile model names to handle different setups
        profile = None
        if hasattr(obj, 'userprofile'):
            profile = obj.userprofile
        elif hasattr(obj, 'profile'):
            profile = obj.profile

        if profile and hasattr(profile, 'profile_image') and profile.profile_image:
            return profile.profile_image.url
        return None
    
    def get_is_verified(self, obj):
        return obj.is_staff or obj.is_superuser


class SimpleCommunityCommentSerializer(serializers.ModelSerializer):
    """Facebook-style comment serializer with replies and likes"""
    author = CommunityAuthorSerializer(read_only=True)
    author_name = serializers.CharField(source='author.username', read_only=True)
    time_ago = serializers.SerializerMethodField()
    like_count = serializers.ReadOnlyField()
    reply_count = serializers.ReadOnlyField()
    is_liked = serializers.SerializerMethodField()
    replies = serializers.SerializerMethodField()

    class Meta:
        model = CommunityComment
        fields = [
            'id', 'post', 'content', 'author', 'author_name', 'time_ago', 'created_at',
            'parent', 'like_count', 'reply_count', 'is_liked', 'replies'
        ]
        read_only_fields = ['id', 'author', 'author_name', 'time_ago', 'created_at']

    def get_time_ago(self, obj):
        """Simple time ago display"""
        from django.utils import timezone
        from datetime import timedelta

        now = timezone.now()
        diff = now - obj.created_at

        if diff < timedelta(minutes=1):
            return "just now"
        elif diff < timedelta(hours=1):
            minutes = int(diff.total_seconds() / 60)
            return f"{minutes}m ago"
        elif diff < timedelta(days=1):
            hours = int(diff.total_seconds() / 3600)
            return f"{hours}h ago"
        elif diff < timedelta(days=7):
            days = diff.days
            return f"{days}d ago"
        else:
            return obj.created_at.strftime("%b %d")

    # Removed create method - handling author assignment in ViewSet

    def validate_content(self, value):
        """Simple content validation"""
        content = value.strip()
        if not content:
            raise serializers.ValidationError("Comment cannot be empty.")

        if len(content) < 2:
            raise serializers.ValidationError("Comment must be at least 2 characters.")

        if len(content) > 500:
            raise serializers.ValidationError("Comment is too long (max 500 characters).")

        return content

    def get_is_liked(self, obj):
        """Check if current user has liked this comment"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.is_liked_by(request.user)
        return False

    def get_replies(self, obj):
        """Get replies to this comment (max 2 levels like Facebook)"""
        if obj.parent is None:  # Only show replies for top-level comments
            replies = obj.replies.all()[:5]  # Limit to 5 replies initially
            return SimpleCommunityCommentSerializer(
                replies,
                many=True,
                context=self.context
            ).data
        return []

# Removed FixedCommunityCommentSerializer - using SimpleCommunityCommentSerializer now

class CommunityPostSerializer(serializers.ModelSerializer):
    author = CommunityAuthorSerializer(read_only=True)
    tags = TagSerializer(many=True, read_only=True)
    tag_names = serializers.ListField(
        child=serializers.CharField(max_length=50),
        write_only=True,
        required=False
    )
    
    # Interaction counts and status
    like_count = serializers.ReadOnlyField()
    save_count = serializers.ReadOnlyField()
    is_liked = serializers.SerializerMethodField()
    is_saved = serializers.SerializerMethodField()
    
    # Comments - simple list, no nesting
    comments = SimpleCommunityCommentSerializer(many=True, read_only=True)
    
    class Meta:
        model = CommunityPost
        fields = [
            'id', 'title', 'content', 'author', 'media', 'tags', 'tag_names',
            'visibility', 'allow_comments', 'like_count', 'save_count', 
            'shares_count', 'comments_count', 'is_liked', 'is_saved',
            'comments', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'author', 'shares_count', 'comments_count', 
            'created_at', 'updated_at'
        ]
    
    def get_is_liked(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.likes.filter(id=request.user.id).exists()
        return False
    
    def get_is_saved(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.saves.filter(id=request.user.id).exists()
        return False

    def validate_title(self, value):
        """Validate and sanitize post title"""
        if not value or not value.strip():
            raise serializers.ValidationError("Post title cannot be empty.")

        try:
            # Sanitize and validate title
            sanitized_title = sanitize_community_content(value, 'title')
            return sanitized_title
        except ValidationError as e:
            # Log security event
            request = self.context.get('request')
            user_id = request.user.id if request and request.user.is_authenticated else None
            security_logger.log_suspicious_activity(
                user_id=user_id,
                activity_type='invalid_post_title',
                details={'title_length': len(value), 'errors': str(e)}
            )
            raise serializers.ValidationError(f"Invalid title: {e}")

    def validate_content(self, value):
        """Validate and sanitize post content"""
        if not value or not value.strip():
            raise serializers.ValidationError("Post content cannot be empty.")

        try:
            # Sanitize and validate content
            sanitized_content = sanitize_community_content(value, 'post_content')
            return sanitized_content
        except ValidationError as e:
            # Log security event
            request = self.context.get('request')
            user_id = request.user.id if request and request.user.is_authenticated else None
            security_logger.log_suspicious_activity(
                user_id=user_id,
                activity_type='invalid_post_content',
                details={'content_length': len(value), 'errors': str(e)}
            )
            raise serializers.ValidationError(f"Invalid content: {e}")

    def validate_tag_names(self, value):
        """Validate tag names"""
        if not value:
            return value

        # Limit number of tags
        if len(value) > 10:
            raise serializers.ValidationError("Maximum 10 tags allowed per post.")

        # Validate each tag
        validated_tags = []
        for tag_name in value:
            if not tag_name or not tag_name.strip():
                continue

            # Sanitize tag name
            tag_name = tag_name.strip().lower()

            # Validate tag length
            if len(tag_name) > 50:
                raise serializers.ValidationError("Tag names cannot exceed 50 characters.")

            # Basic validation for tag format
            if not tag_name.replace('_', '').replace('-', '').isalnum():
                raise serializers.ValidationError("Tags can only contain letters, numbers, hyphens, and underscores.")

            validated_tags.append(tag_name)

        return validated_tags

    def validate(self, data):
        """Additional validation for post data"""
        request = self.context.get('request')

        # Ensure user is authenticated for post creation
        if not request or not request.user.is_authenticated:
            raise serializers.ValidationError("Authentication required to create posts.")

        # Validate visibility setting
        visibility = data.get('visibility', 'public')
        if visibility not in ['public', 'followers', 'private']:
            raise serializers.ValidationError("Invalid visibility setting.")

        return data

    def create(self, validated_data):
        tag_names = validated_data.pop('tag_names', [])
        post = CommunityPost.objects.create(**validated_data)
        
        # Handle tags
        if tag_names:
            from api.models import Tag
            for tag_name in tag_names:
                tag, _ = Tag.objects.get_or_create(name=tag_name.lower())
                post.tags.add(tag)
        
        return post


class UserFollowSerializer(serializers.ModelSerializer):
    follower = CommunityAuthorSerializer(read_only=True)
    following = CommunityAuthorSerializer(read_only=True)
    
    class Meta:
        model = UserFollow
        fields = ['id', 'follower', 'following', 'followed_at']
        read_only_fields = ['id', 'followed_at']


class UserActivitySerializer(serializers.ModelSerializer):
    user = CommunityAuthorSerializer(read_only=True)
    
    class Meta:
        model = UserActivity
        fields = ['id', 'user', 'activity_type', 'content_type', 'object_id', 'data', 'created_at']
        read_only_fields = ['id', 'created_at']


class OnlineUserSerializer(serializers.ModelSerializer):
    user = CommunityAuthorSerializer(read_only=True)
    
    class Meta:
        model = OnlineUser
        fields = ['user', 'last_seen', 'is_online']


class CommunityStatsSerializer(serializers.ModelSerializer):
    online_users_count = serializers.SerializerMethodField()
    
    class Meta:
        model = CommunityStats
        fields = [
            'total_posts', 'total_users', 'total_likes', 'total_comments',
            'posts_today', 'active_users_today', 'online_users_count', 'updated_at'
        ]
    
    def get_online_users_count(self, obj):
        # obj parameter is required by DRF but not used in this case
        return OnlineUser.get_online_count()


class CommunityFeedSerializer(serializers.Serializer):
    """Serializer for community feed with mixed content types"""
    posts = CommunityPostSerializer(many=True, read_only=True)
    has_more = serializers.BooleanField(read_only=True)
    next_cursor = serializers.CharField(read_only=True, allow_null=True)
