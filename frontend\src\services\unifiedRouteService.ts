/**
 * 🎯 UNIFIED ROUTE SERVICE
 * Single source of truth for all routing logic in the application
 * 
 * This service eliminates conflicts between:
 * - Multiple route configuration files (routeConfig.ts vs ConsolidatedAppRoutes.tsx)
 * - Different route protection systems (ConsolidatedProtectedRoute vs EnhancedProtectedRoute)
 * - Inconsistent navigation patterns
 * - Mixed role-based routing approaches
 */

import { unifiedRoleService } from './unifiedRoleService';
import { type RouteDefinition, getAllRoutes } from '../config/routeConfig';

export interface UnifiedRouteInfo {
  path: string;
  isAccessible: boolean;
  redirectTo?: string;
  requiresAuth: boolean;
  allowedRoles: string[];
  title?: string;
  layout?: string;
}

export class UnifiedRouteService {
  private static instance: UnifiedRouteService;
  
  public static getInstance(): UnifiedRouteService {
    if (!UnifiedRouteService.instance) {
      UnifiedRouteService.instance = new UnifiedRouteService();
    }
    return UnifiedRouteService.instance;
  }

  /**
   * Check if a user can access a specific route
   */
  public canAccessRoute(userRole: string, routePath: string, isAuthenticated: boolean): boolean {
    // Public routes are always accessible
    if (this.isPublicRoute(routePath)) {
      return true;
    }

    // Authentication required for protected routes
    if (!isAuthenticated) {
      return false;
    }

    // Check role-based access
    return unifiedRoleService.canAccessRoute(userRole, routePath);
  }

  /**
   * Get route information for a specific path
   */
  public getRouteInfo(routePath: string, userRole: string, isAuthenticated: boolean): UnifiedRouteInfo {
    const routeDefinition = this.findRouteDefinition(routePath);
    const isAccessible = this.canAccessRoute(userRole, routePath, isAuthenticated);
    
    return {
      path: routePath,
      isAccessible,
      redirectTo: this.getRedirectPath(routePath, userRole, isAuthenticated),
      requiresAuth: !this.isPublicRoute(routePath),
      allowedRoles: this.getAllowedRoles(routePath),
      title: routeDefinition?.title,
      layout: routeDefinition?.layout
    };
  }

  /**
   * Get the appropriate redirect path for a user
   */
  public getRedirectPath(requestedPath: string, userRole: string, isAuthenticated: boolean): string | undefined {
    // If not authenticated, redirect to login
    if (!isAuthenticated && !this.isPublicRoute(requestedPath)) {
      return '/login';
    }

    // If authenticated but no access, redirect to appropriate home
    if (isAuthenticated && !this.canAccessRoute(userRole, requestedPath, isAuthenticated)) {
      return unifiedRoleService.getHomeRoute(userRole);
    }

    // If trying to access dashboard as user, redirect to user home
    if (userRole === 'user' && requestedPath.includes('/dashboard')) {
      return '/user/home';
    }

    return undefined;
  }

  /**
   * Check if a route is public (no authentication required)
   */
  public isPublicRoute(routePath: string): boolean {
    const publicPaths = [
      '/',
      '/login',
      '/register',
      '/registration-success',
      '/register/success',
      '/signup/success',
      '/logout',
      '/features',
      '/about',
      '/contact',
      '/community',
      '/access-denied',
      '/not-found',
      '/test-auth'
    ];

    return publicPaths.some(path => 
      routePath === path || 
      (path.endsWith('*') && routePath.startsWith(path.slice(0, -1)))
    );
  }

  /**
   * Get allowed roles for a specific route
   */
  public getAllowedRoles(routePath: string): string[] {
    // Public routes allow everyone
    if (this.isPublicRoute(routePath)) {
      return ['anonymous', 'user', 'entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'];
    }

    // User-specific routes
    if (routePath.startsWith('/user/')) {
      return ['user'];
    }

    // Dynamic role routes
    if (routePath.includes('/:role/')) {
      const routeType = this.extractRouteType(routePath);
      return this.getRolesForRouteType(routeType);
    }

    // Default authenticated routes
    return ['entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'];
  }

  /**
   * Extract route type from dynamic route pattern
   */
  private extractRouteType(routePath: string): string {
    const match = routePath.match(/\/:role\/(.+)/);
    return match ? match[1].split('/')[0] : '';
  }

  /**
   * Get allowed roles for a specific route type
   */
  private getRolesForRouteType(routeType: string): string[] {
    const roleMap: Record<string, string[]> = {
      'dashboard': ['entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'],
      'profile': ['entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'],
      'settings': ['entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'],
      'ai-chat': ['entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'],
      'business-plans': ['entrepreneur', 'admin', 'super_admin'],
      'business-ideas': ['entrepreneur', 'admin', 'super_admin'],
      'mentorship': ['mentor', 'admin', 'super_admin'],
      'mentees': ['mentor', 'admin', 'super_admin'],
      'investments': ['investor', 'admin', 'super_admin'],
      'portfolio': ['investor', 'admin', 'super_admin'],
      'moderation': ['moderator', 'admin', 'super_admin'],
      'reports': ['moderator', 'admin', 'super_admin'],
      'users': ['admin', 'super_admin'],
      'user-management': ['admin', 'super_admin'],
      'content': ['admin', 'super_admin'],
      'analytics': ['admin', 'super_admin', 'moderator'],
      'approvals': ['admin', 'super_admin'],
      'system': ['super_admin'],
      'system-health': ['super_admin'],
      'role-applications': ['admin', 'super_admin']
    };

    return roleMap[routeType] || ['entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'];
  }

  /**
   * Find route definition from configuration
   */
  private findRouteDefinition(routePath: string): RouteDefinition | undefined {
    const allRoutes = getAllRoutes();
    return allRoutes.find(route => 
      route.path === routePath || 
      this.matchesDynamicRoute(route.path, routePath)
    );
  }

  /**
   * Check if a path matches a dynamic route pattern
   */
  private matchesDynamicRoute(pattern: string, path: string): boolean {
    if (!pattern.includes(':')) return false;
    
    const patternParts = pattern.split('/');
    const pathParts = path.split('/');
    
    if (patternParts.length !== pathParts.length) return false;
    
    return patternParts.every((part, index) => 
      part.startsWith(':') || part === pathParts[index]
    );
  }

  /**
   * Validate routing system consistency
   */
  public validateRoutingConsistency(): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    // Check for route conflicts
    const allRoutes = getAllRoutes();
    const routePaths = allRoutes.map(route => route.path);
    const duplicates = routePaths.filter((path, index) => routePaths.indexOf(path) !== index);
    
    if (duplicates.length > 0) {
      issues.push(`Duplicate route paths found: ${duplicates.join(', ')}`);
    }

    // Check for user dashboard access
    const userDashboardRoutes = allRoutes.filter(route => 
      route.allowedRoles?.includes('user') && route.path.includes('dashboard')
    );
    
    if (userDashboardRoutes.length > 0) {
      issues.push('User role should not have dashboard access');
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }
}

// Export singleton instance
export const unifiedRouteService = UnifiedRouteService.getInstance();
