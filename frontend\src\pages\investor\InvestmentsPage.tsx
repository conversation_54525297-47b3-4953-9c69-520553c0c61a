/**
 * Investments Page - Investor Dashboard
 * Comprehensive investment tracking and management
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  PieChart,
  BarChart3,
  Target,
  Calendar,
  Eye,
  Plus,
  Filter,
  Search,
  Star,
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  Building,
  Globe,
  Award,
  Zap,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';

interface Investment {
  id: string;
  companyName: string;
  founderName: string;
  industry: string;
  stage: 'seed' | 'series_a' | 'series_b' | 'series_c' | 'ipo';
  investmentAmount: number;
  currentValuation: number;
  initialValuation: number;
  investmentDate: string;
  status: 'active' | 'exited' | 'failed' | 'pending';
  roi: number;
  ownership: number;
  lastUpdate: string;
  description: string;
  logo?: string;
  metrics: {
    revenue: number;
    growth: number;
    employees: number;
    customers: number;
  };
}

interface PortfolioSummary {
  totalInvested: number;
  currentValue: number;
  totalROI: number;
  activeInvestments: number;
  exitedInvestments: number;
  averageROI: number;
}

const InvestmentsPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [portfolioSummary, setPortfolioSummary] = useState<PortfolioSummary | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedSort, setSelectedSort] = useState('investment_date_desc');

  useEffect(() => {
    loadInvestmentData();
  }, [selectedFilter, selectedSort]);

  const loadInvestmentData = async () => {
    setLoading(true);
    try {
      // Simulate API call - replace with real API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setInvestments([
        {
          id: '1',
          companyName: 'TechStart Syria',
          founderName: 'Ahmad Al-Hassan',
          industry: 'Technology',
          stage: 'seed',
          investmentAmount: 50000,
          currentValuation: 2000000,
          initialValuation: 1000000,
          investmentDate: '2023-06-15T10:00:00Z',
          status: 'active',
          roi: 100,
          ownership: 5,
          lastUpdate: '2024-01-15T14:30:00Z',
          description: 'AI-powered business automation platform for SMEs',
          metrics: {
            revenue: 150000,
            growth: 45,
            employees: 12,
            customers: 250
          }
        },
        {
          id: '2',
          companyName: 'GreenEnergy Solutions',
          founderName: 'Fatima Al-Zahra',
          industry: 'Clean Energy',
          stage: 'series_a',
          investmentAmount: 100000,
          currentValuation: 8000000,
          initialValuation: 5000000,
          investmentDate: '2023-03-20T09:00:00Z',
          status: 'active',
          roi: 60,
          ownership: 2,
          lastUpdate: '2024-01-14T11:20:00Z',
          description: 'Solar energy solutions for residential and commercial use',
          metrics: {
            revenue: 500000,
            growth: 80,
            employees: 35,
            customers: 150
          }
        },
        {
          id: '3',
          companyName: 'EduTech Arabia',
          founderName: 'Omar Khalil',
          industry: 'Education',
          stage: 'seed',
          investmentAmount: 75000,
          currentValuation: 1200000,
          initialValuation: 800000,
          investmentDate: '2023-09-10T15:00:00Z',
          status: 'active',
          roi: 50,
          ownership: 9.4,
          lastUpdate: '2024-01-13T16:45:00Z',
          description: 'Online learning platform for Arabic-speaking students',
          metrics: {
            revenue: 80000,
            growth: 120,
            employees: 8,
            customers: 1200
          }
        },
        {
          id: '4',
          companyName: 'HealthTech Innovations',
          founderName: 'Layla Mansour',
          industry: 'Healthcare',
          stage: 'series_a',
          investmentAmount: 150000,
          currentValuation: 3000000,
          initialValuation: 3000000,
          investmentDate: '2022-12-05T12:00:00Z',
          status: 'exited',
          roi: 200,
          ownership: 5,
          lastUpdate: '2024-01-01T10:00:00Z',
          description: 'Telemedicine platform for remote healthcare delivery',
          metrics: {
            revenue: 800000,
            growth: 25,
            employees: 45,
            customers: 5000
          }
        }
      ]);

      setPortfolioSummary({
        totalInvested: 375000,
        currentValue: 675000,
        totalROI: 80,
        activeInvestments: 3,
        exitedInvestments: 1,
        averageROI: 102.5
      });
    } catch (error) {
      console.error('Error loading investment data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'seed':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'series_a':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'series_b':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'series_c':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'ipo':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'exited':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const filteredInvestments = investments.filter(investment => {
    const matchesSearch = searchQuery === '' || 
      investment.companyName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      investment.founderName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      investment.industry.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesFilter = selectedFilter === 'all' || investment.status === selectedFilter;
    
    return matchesSearch && matchesFilter;
  });

  const sortedInvestments = [...filteredInvestments].sort((a, b) => {
    switch (selectedSort) {
      case 'company_asc':
        return a.companyName.localeCompare(b.companyName);
      case 'company_desc':
        return b.companyName.localeCompare(a.companyName);
      case 'investment_date_desc':
        return new Date(b.investmentDate).getTime() - new Date(a.investmentDate).getTime();
      case 'investment_date_asc':
        return new Date(a.investmentDate).getTime() - new Date(b.investmentDate).getTime();
      case 'roi_desc':
        return b.roi - a.roi;
      case 'roi_asc':
        return a.roi - b.roi;
      case 'amount_desc':
        return b.investmentAmount - a.investmentAmount;
      case 'amount_asc':
        return a.investmentAmount - b.investmentAmount;
      default:
        return 0;
    }
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 text-white p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
          <span className="ml-4 text-lg">{t('Loading investment data...')}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className={`flex items-center justify-between mb-8 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="w-12 h-12 bg-purple-600/30 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-purple-400" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">{t('My Investments')}</h1>
              <p className="text-purple-200">{t('Track and manage your investment portfolio')}</p>
            </div>
          </div>
          
          <button
            onClick={() => navigate('/investor/opportunities')}
            className="flex items-center gap-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
          >
            <Plus className="w-4 h-4" />
            {t('Find Opportunities')}
          </button>
        </div>

        {/* Portfolio Summary */}
        {portfolioSummary && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-purple-200 text-sm">{t('Total Invested')}</p>
                  <p className="text-2xl font-bold">{formatCurrency(portfolioSummary.totalInvested)}</p>
                </div>
                <DollarSign className="w-8 h-8 text-purple-400" />
              </div>
            </div>

            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-purple-200 text-sm">{t('Current Value')}</p>
                  <p className="text-2xl font-bold text-green-400">{formatCurrency(portfolioSummary.currentValue)}</p>
                </div>
                <TrendingUp className="w-8 h-8 text-green-400" />
              </div>
            </div>

            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-purple-200 text-sm">{t('Total ROI')}</p>
                  <p className="text-2xl font-bold text-yellow-400">+{portfolioSummary.totalROI}%</p>
                </div>
                <Target className="w-8 h-8 text-yellow-400" />
              </div>
            </div>

            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-purple-200 text-sm">{t('Active Investments')}</p>
                  <p className="text-2xl font-bold">{portfolioSummary.activeInvestments}</p>
                </div>
                <Building className="w-8 h-8 text-blue-400" />
              </div>
            </div>

            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-purple-200 text-sm">{t('Exits')}</p>
                  <p className="text-2xl font-bold">{portfolioSummary.exitedInvestments}</p>
                </div>
                <Award className="w-8 h-8 text-orange-400" />
              </div>
            </div>

            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-purple-200 text-sm">{t('Avg ROI')}</p>
                  <p className="text-2xl font-bold text-green-400">+{portfolioSummary.averageROI}%</p>
                </div>
                <BarChart3 className="w-8 h-8 text-green-400" />
              </div>
            </div>
          </div>
        )}

        {/* Filters and Search */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 mb-6">
          <div className={`flex flex-wrap items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="flex items-center gap-2 flex-1 max-w-md">
              <Search className="w-4 h-4 text-purple-400" />
              <input
                type="text"
                placeholder={t('Search investments...')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 bg-indigo-900/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-purple-300 text-sm"
              />
            </div>

            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-purple-400" />
              <select
                value={selectedFilter}
                onChange={(e) => setSelectedFilter(e.target.value)}
                className="bg-indigo-900/50 border border-indigo-700 rounded-lg px-3 py-2 text-white text-sm"
              >
                <option value="all">{t('All Status')}</option>
                <option value="active">{t('Active')}</option>
                <option value="exited">{t('Exited')}</option>
                <option value="failed">{t('Failed')}</option>
                <option value="pending">{t('Pending')}</option>
              </select>
            </div>

            <div className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4 text-purple-400" />
              <select
                value={selectedSort}
                onChange={(e) => setSelectedSort(e.target.value)}
                className="bg-indigo-900/50 border border-indigo-700 rounded-lg px-3 py-2 text-white text-sm"
              >
                <option value="investment_date_desc">{t('Recent Investments')}</option>
                <option value="roi_desc">{t('Highest ROI')}</option>
                <option value="amount_desc">{t('Largest Investment')}</option>
                <option value="company_asc">{t('Company A-Z')}</option>
              </select>
            </div>
          </div>
        </div>

        {/* Investments Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {sortedInvestments.map((investment) => (
            <div key={investment.id} className="bg-indigo-900/30 backdrop-blur-sm rounded-lg border border-indigo-800/50 overflow-hidden hover:border-purple-500/50 transition-all duration-300 group">
              {/* Header */}
              <div className="p-6 border-b border-indigo-800/50">
                <div className={`flex items-start justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className="flex-1 min-w-0">
                    <div className={`flex items-center gap-3 mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center text-white font-semibold">
                        {investment.companyName.charAt(0)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-semibold text-white truncate">{investment.companyName}</h3>
                        <p className="text-sm text-purple-200">{t('Founded by')} {investment.founderName}</p>
                      </div>
                    </div>
                    <p className="text-sm text-purple-200 line-clamp-2">{investment.description}</p>
                  </div>
                </div>
                
                <div className={`flex items-center gap-2 mt-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(investment.status)}`}>
                    {t(investment.status)}
                  </span>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStageColor(investment.stage)}`}>
                    {t(investment.stage)}
                  </span>
                  <span className="text-xs text-purple-300">{investment.industry}</span>
                </div>
              </div>

              {/* Investment Details */}
              <div className="p-6 border-b border-indigo-800/50">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-purple-200">{t('Investment Amount')}</p>
                    <p className="text-lg font-semibold text-white">{formatCurrency(investment.investmentAmount)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-purple-200">{t('Current Value')}</p>
                    <p className="text-lg font-semibold text-green-400">{formatCurrency(investment.currentValuation * investment.ownership / 100)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-purple-200">{t('ROI')}</p>
                    <div className="flex items-center gap-1">
                      {investment.roi >= 0 ? (
                        <ArrowUpRight className="w-4 h-4 text-green-400" />
                      ) : (
                        <ArrowDownRight className="w-4 h-4 text-red-400" />
                      )}
                      <p className={`text-lg font-semibold ${investment.roi >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {investment.roi >= 0 ? '+' : ''}{investment.roi}%
                      </p>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-purple-200">{t('Ownership')}</p>
                    <p className="text-lg font-semibold text-white">{investment.ownership}%</p>
                  </div>
                </div>
              </div>

              {/* Company Metrics */}
              <div className="p-6 border-b border-indigo-800/50">
                <h4 className="text-sm font-medium text-purple-200 mb-3">{t('Company Metrics')}</h4>
                <div className="grid grid-cols-2 gap-3">
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4 text-green-400" />
                    <div>
                      <p className="text-xs text-purple-300">{t('Revenue')}</p>
                      <p className="text-sm font-medium text-white">{formatCurrency(investment.metrics.revenue)}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <TrendingUp className="w-4 h-4 text-blue-400" />
                    <div>
                      <p className="text-xs text-purple-300">{t('Growth')}</p>
                      <p className="text-sm font-medium text-white">+{investment.metrics.growth}%</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4 text-yellow-400" />
                    <div>
                      <p className="text-xs text-purple-300">{t('Employees')}</p>
                      <p className="text-sm font-medium text-white">{investment.metrics.employees}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Target className="w-4 h-4 text-purple-400" />
                    <div>
                      <p className="text-xs text-purple-300">{t('Customers')}</p>
                      <p className="text-sm font-medium text-white">{investment.metrics.customers.toLocaleString()}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="p-4">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className="text-xs text-purple-300">
                    {t('Invested on')} {new Date(investment.investmentDate).toLocaleDateString()}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => navigate(`/investor/investments/${investment.id}`)}
                      className="text-purple-400 hover:text-purple-300 transition-colors"
                      title={t('View Details')}
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    <button
                      className="text-blue-400 hover:text-blue-300 transition-colors"
                      title={t('View Reports')}
                    >
                      <BarChart3 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {sortedInvestments.length === 0 && (
          <div className="text-center py-12">
            <DollarSign className="w-16 h-16 text-purple-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">{t('No investments found')}</h3>
            <p className="text-purple-200 mb-6">{t('Start investing in promising startups to build your portfolio')}</p>
            <button
              onClick={() => navigate('/investor/opportunities')}
              className="inline-flex items-center gap-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4" />
              {t('Explore Opportunities')}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default InvestmentsPage;
