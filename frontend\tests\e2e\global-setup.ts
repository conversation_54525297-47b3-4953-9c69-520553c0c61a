/**
 * Global Setup for E2E Tests
 * Prepares the testing environment before running tests
 */

import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for E2E tests...');

  // Launch browser for setup
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Wait for the application to be ready
    console.log('⏳ Waiting for application to be ready...');
    await page.goto('http://localhost:5173', { waitUntil: 'networkidle' });
    
    // Check if the application is responding
    await page.waitForSelector('body', { timeout: 30000 });
    console.log('✅ Application is ready');

    // Setup test data if needed
    console.log('📝 Setting up test data...');
    
    // You can add API calls here to set up test data
    // For example, creating test users, posts, etc.
    
    // Create a test user session (if authentication is needed)
    try {
      await page.goto('http://localhost:5173/login');
      
      // Check if login page exists
      const loginExists = await page.locator('[data-testid="login-form"]').isVisible().catch(() => false);
      
      if (loginExists) {
        console.log('🔐 Setting up test authentication...');
        
        // You can add login logic here if needed for tests
        // await page.fill('[data-testid="username-input"]', 'testuser');
        // await page.fill('[data-testid="password-input"]', 'testpass');
        // await page.click('[data-testid="login-button"]');
        
        console.log('✅ Test authentication setup complete');
      }
    } catch (error) {
      console.log('ℹ️ Login page not available or not needed for tests');
    }

    // Verify community page is accessible
    console.log('🏘️ Verifying community page accessibility...');
    await page.goto('http://localhost:5173/community');
    await page.waitForSelector('[data-testid="community-header"], .community-header, h1', { timeout: 10000 });
    console.log('✅ Community page is accessible');

    // Clear any existing test data
    console.log('🧹 Cleaning up any existing test data...');
    
    // You can add cleanup logic here
    // For example, clearing localStorage, sessionStorage, etc.
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });

    console.log('✅ Global setup completed successfully');

  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await context.close();
    await browser.close();
  }
}

export default globalSetup;
