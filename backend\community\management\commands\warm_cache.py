"""
Management command to warm up community cache
"""

from django.core.management.base import BaseCommand
from community.cache import CacheWarmer


class Command(BaseCommand):
    help = 'Warm up community cache with frequently accessed data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--stats',
            action='store_true',
            help='Warm up community stats cache',
        )
        parser.add_argument(
            '--hashtags',
            action='store_true',
            help='Warm up trending hashtags cache',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Warm up all cache types',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output',
        )

    def handle(self, *args, **options):
        verbose = options['verbose']
        success_count = 0
        total_count = 0
        
        if options['all'] or options['stats']:
            total_count += 1
            if verbose:
                self.stdout.write('Warming up community stats cache...')
            
            if CacheWarmer.warm_community_stats():
                success_count += 1
                self.stdout.write(
                    self.style.SUCCESS('✓ Community stats cache warmed successfully')
                )
            else:
                self.stdout.write(
                    self.style.ERROR('✗ Failed to warm community stats cache')
                )

        if options['all'] or options['hashtags']:
            total_count += 1
            if verbose:
                self.stdout.write('Warming up trending hashtags cache...')
            
            if CacheWarmer.warm_trending_hashtags():
                success_count += 1
                self.stdout.write(
                    self.style.SUCCESS('✓ Trending hashtags cache warmed successfully')
                )
            else:
                self.stdout.write(
                    self.style.ERROR('✗ Failed to warm trending hashtags cache')
                )

        if not any([options['all'], options['stats'], options['hashtags']]):
            self.stdout.write(
                self.style.WARNING('No cache type specified. Use --all, --stats, or --hashtags')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'\nCache warming completed: {success_count}/{total_count} successful')
            )
