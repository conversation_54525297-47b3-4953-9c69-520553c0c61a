/**
 * Focus Management Utilities
 * Utilities for managing focus and keyboard navigation in the community page
 */

// Focus trap for modals and overlays
export class FocusTrap {
  private element: HTMLElement;
  private focusableElements: HTMLElement[];
  private firstFocusableElement: HTMLElement | null = null;
  private lastFocusableElement: HTMLElement | null = null;
  private previouslyFocusedElement: HTMLElement | null = null;

  constructor(element: HTMLElement) {
    this.element = element;
    this.focusableElements = [];
    this.updateFocusableElements();
  }

  private updateFocusableElements() {
    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'textarea:not([disabled])',
      'select:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ].join(', ');

    this.focusableElements = Array.from(
      this.element.querySelectorAll(focusableSelectors)
    ) as HTMLElement[];

    this.firstFocusableElement = this.focusableElements[0] || null;
    this.lastFocusableElement = 
      this.focusableElements[this.focusableElements.length - 1] || null;
  }

  activate() {
    this.previouslyFocusedElement = document.activeElement as HTMLElement;
    this.element.addEventListener('keydown', this.handleKeyDown);
    
    // Focus the first focusable element
    if (this.firstFocusableElement) {
      this.firstFocusableElement.focus();
    }
  }

  deactivate() {
    this.element.removeEventListener('keydown', this.handleKeyDown);
    
    // Return focus to previously focused element
    if (this.previouslyFocusedElement) {
      this.previouslyFocusedElement.focus();
    }
  }

  private handleKeyDown = (event: KeyboardEvent) => {
    if (event.key !== 'Tab') return;

    // Update focusable elements in case DOM changed
    this.updateFocusableElements();

    if (this.focusableElements.length === 0) {
      event.preventDefault();
      return;
    }

    if (event.shiftKey) {
      // Shift + Tab (backward)
      if (document.activeElement === this.firstFocusableElement) {
        event.preventDefault();
        this.lastFocusableElement?.focus();
      }
    } else {
      // Tab (forward)
      if (document.activeElement === this.lastFocusableElement) {
        event.preventDefault();
        this.firstFocusableElement?.focus();
      }
    }
  };
}

// Skip link functionality
export const setupSkipLinks = () => {
  const skipLinks = document.querySelectorAll('a[href^="#"]');
  
  skipLinks.forEach(link => {
    link.addEventListener('click', (event) => {
      event.preventDefault();
      const targetId = (link as HTMLAnchorElement).getAttribute('href')?.substring(1);
      
      if (targetId) {
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
          targetElement.focus();
          targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }
    });
  });
};

// Announce content changes to screen readers
export const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.textContent = message;
  
  document.body.appendChild(announcement);
  
  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
};

// Manage focus for dynamic content
export const manageFocusForDynamicContent = (
  container: HTMLElement,
  announcement?: string
) => {
  // Announce content change
  if (announcement) {
    announceToScreenReader(announcement);
  }
  
  // Focus the container or first focusable element
  const firstFocusable = container.querySelector(
    'button, input, textarea, select, a[href], [tabindex]:not([tabindex="-1"])'
  ) as HTMLElement;
  
  if (firstFocusable) {
    firstFocusable.focus();
  } else {
    container.focus();
  }
};

// Keyboard navigation for lists and grids
export const setupArrowKeyNavigation = (
  container: HTMLElement,
  itemSelector: string,
  options: {
    wrap?: boolean;
    orientation?: 'horizontal' | 'vertical' | 'both';
  } = {}
) => {
  const { wrap = true, orientation = 'both' } = options;
  
  container.addEventListener('keydown', (event) => {
    const items = Array.from(container.querySelectorAll(itemSelector)) as HTMLElement[];
    const currentIndex = items.indexOf(document.activeElement as HTMLElement);
    
    if (currentIndex === -1) return;
    
    let nextIndex = currentIndex;
    
    switch (event.key) {
      case 'ArrowDown':
        if (orientation === 'vertical' || orientation === 'both') {
          event.preventDefault();
          nextIndex = currentIndex + 1;
          if (nextIndex >= items.length && wrap) nextIndex = 0;
        }
        break;
        
      case 'ArrowUp':
        if (orientation === 'vertical' || orientation === 'both') {
          event.preventDefault();
          nextIndex = currentIndex - 1;
          if (nextIndex < 0 && wrap) nextIndex = items.length - 1;
        }
        break;
        
      case 'ArrowRight':
        if (orientation === 'horizontal' || orientation === 'both') {
          event.preventDefault();
          nextIndex = currentIndex + 1;
          if (nextIndex >= items.length && wrap) nextIndex = 0;
        }
        break;
        
      case 'ArrowLeft':
        if (orientation === 'horizontal' || orientation === 'both') {
          event.preventDefault();
          nextIndex = currentIndex - 1;
          if (nextIndex < 0 && wrap) nextIndex = items.length - 1;
        }
        break;
        
      case 'Home':
        event.preventDefault();
        nextIndex = 0;
        break;
        
      case 'End':
        event.preventDefault();
        nextIndex = items.length - 1;
        break;
    }
    
    if (nextIndex !== currentIndex && items[nextIndex]) {
      items[nextIndex].focus();
    }
  });
};

// Initialize all accessibility features
export const initializeAccessibility = () => {
  setupSkipLinks();
  
  // Add focus visible polyfill for older browsers
  if (!CSS.supports('selector(:focus-visible)')) {
    document.addEventListener('keydown', () => {
      document.body.classList.add('keyboard-navigation');
    });
    
    document.addEventListener('mousedown', () => {
      document.body.classList.remove('keyboard-navigation');
    });
  }
  
  // Announce page changes for SPA navigation
  let currentPath = window.location.pathname;
  const observer = new MutationObserver(() => {
    if (window.location.pathname !== currentPath) {
      currentPath = window.location.pathname;
      announceToScreenReader(`Navigated to ${document.title}`, 'assertive');
    }
  });
  
  observer.observe(document.body, { childList: true, subtree: true });
};
