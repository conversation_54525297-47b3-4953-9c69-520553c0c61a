"""
🎯 UNIFIED ROLE CONFIGURATION
Single source of truth for all role definitions, hierarchies, and permissions
across the entire application.

This file consolidates:
- Role hierarchy definitions (previously in 4+ files)
- Permission level mappings (previously in 3+ files)
- Role group definitions (previously in 2+ files)
- System operation permissions (previously scattered)
- AI capability mappings (previously in ai_permissions.py)

Usage:
    from core.config.role_config import ROLE_HIERARCHY, PERMISSION_HIERARCHY, ROLE_GROUPS
"""

# ========================================
# ROLE HIERARCHY DEFINITIONS
# ========================================

ROLE_HIERARCHY = {
    'anonymous': 0,
    'user': 1,
    'entrepreneur': 2,
    'mentor': 2,
    'investor': 2,
    'moderator': 3,
    'admin': 4,
    'super_admin': 5
}

# ========================================
# PERMISSION HIERARCHY DEFINITIONS
# ========================================

PERMISSION_HIERARCHY = {
    'none': 0,
    'read': 1,
    'write': 2,
    'moderate': 3,
    'admin': 4,
    'super_admin': 5
}

# ========================================
# ROLE TO PERMISSION MAPPING
# ========================================

ROLE_PERMISSION_MAPPING = {
    'user': 'read',
    'entrepreneur': 'write',
    'mentor': 'write',
    'investor': 'write',
    'moderator': 'moderate',
    'admin': 'admin',
    'super_admin': 'super_admin'
}

# ========================================
# ROLE GROUP DEFINITIONS
# ========================================

def get_role_groups(available_roles=None):
    """
    Get role groups based on available roles
    
    Args:
        available_roles: List of available role names (optional)
        
    Returns:
        Dict: Role groups organized by functionality
    """
    if available_roles is None:
        available_roles = list(ROLE_HIERARCHY.keys())
    
    return {
        'ALL_ROLES': available_roles,
        'ADMIN_ROLES': [r for r in available_roles if r in ['admin', 'super_admin']],
        'BUSINESS_ROLES': [r for r in available_roles if r in ['entrepreneur', 'mentor', 'investor']],
        'ELEVATED_ROLES': [r for r in available_roles if r in ['admin', 'super_admin', 'moderator']],
        'CONTENT_ACCESS_ROLES': [r for r in available_roles if r in ['user', 'entrepreneur', 'mentor', 'investor']],
        'FUNDING_ACCESS_ROLES': [r for r in available_roles if r in ['entrepreneur', 'mentor', 'investor', 'admin', 'super_admin']],
        'ANALYTICS_ACCESS_ROLES': [r for r in available_roles if r in ['user', 'entrepreneur', 'mentor', 'investor', 'admin', 'moderator']],
        'MODERATION_ROLES': [r for r in available_roles if r in ['moderator', 'admin', 'super_admin']]
    }

# ========================================
# SYSTEM OPERATION PERMISSIONS
# ========================================

SYSTEM_OPERATIONS = {
    'user_management',
    'role_management', 
    'system_configuration',
    'security_audit',
    'data_export',
    'system_backup',
    'system_restore',
    'performance_monitoring',
    'error_tracking',
    'feature_toggle'
}

# ========================================
# AI RATE LIMITS BY ROLE
# ========================================

AI_RATE_LIMITS = {
    'super_admin': {'chat': 10000, 'analysis': 5000, 'generation': 2000},
    'admin': {'chat': 1000, 'analysis': 500, 'generation': 200},
    'moderator': {'chat': 200, 'analysis': 100, 'generation': 50},
    'investor': {'chat': 100, 'analysis': 50, 'generation': 25},
    'mentor': {'chat': 100, 'analysis': 50, 'generation': 25},
    'entrepreneur': {'chat': 100, 'analysis': 50, 'generation': 25},
    'user': {'chat': 20, 'analysis': 5, 'generation': 3}
}

# ========================================
# ROLE DEFINITIONS FOR DATABASE SYNC
# ========================================

REQUIRED_ROLES = [
    {
        'name': 'super_admin',
        'display_name': 'Super Administrator',
        'description': 'Complete system control with highest level access to all platform features, system management, user impersonation, AI system control, and advanced analytics.',
        'permission_level': 'super_admin',
        'is_active': True,
        'requires_approval': False
    },
    {
        'name': 'admin',
        'display_name': 'Administrator',
        'description': 'Full system administrator with all permissions except super admin functions',
        'permission_level': 'admin',
        'is_active': True,
        'requires_approval': False
    },
    {
        'name': 'moderator',
        'display_name': 'Moderator',
        'description': 'Content moderation and community management',
        'permission_level': 'moderate',
        'is_active': True,
        'requires_approval': True
    },
    {
        'name': 'entrepreneur',
        'display_name': 'Entrepreneur',
        'description': 'Business creator with access to funding and mentorship features',
        'permission_level': 'write',
        'is_active': True,
        'requires_approval': True
    },
    {
        'name': 'mentor',
        'display_name': 'Mentor',
        'description': 'Business mentor with access to mentorship and guidance features',
        'permission_level': 'write',
        'is_active': True,
        'requires_approval': True
    },
    {
        'name': 'investor',
        'display_name': 'Investor',
        'description': 'Investment professional with access to funding opportunities',
        'permission_level': 'write',
        'is_active': True,
        'requires_approval': True
    },
    {
        'name': 'user',
        'display_name': 'User',
        'description': 'Standard platform user with basic access',
        'permission_level': 'read',
        'is_active': True,
        'requires_approval': False
    }
]

# ========================================
# UTILITY FUNCTIONS
# ========================================

def get_role_level(role_name: str) -> int:
    """Get numeric level for a role"""
    return ROLE_HIERARCHY.get(role_name, 0)

def get_permission_level(permission_name: str) -> int:
    """Get numeric level for a permission"""
    return PERMISSION_HIERARCHY.get(permission_name, 0)

def get_role_permission(role_name: str) -> str:
    """Get permission level for a role"""
    return ROLE_PERMISSION_MAPPING.get(role_name, 'read')

def get_ai_rate_limits(role_name: str) -> dict:
    """Get AI rate limits for a role"""
    return AI_RATE_LIMITS.get(role_name, AI_RATE_LIMITS['user'])

def is_system_operation(operation: str) -> bool:
    """Check if operation is a valid system operation"""
    return operation in SYSTEM_OPERATIONS

def has_higher_role_level(role1: str, role2: str) -> bool:
    """Check if role1 has higher level than role2"""
    return get_role_level(role1) > get_role_level(role2)

def has_higher_permission_level(perm1: str, perm2: str) -> bool:
    """Check if perm1 has higher level than perm2"""
    return get_permission_level(perm1) > get_permission_level(perm2)
