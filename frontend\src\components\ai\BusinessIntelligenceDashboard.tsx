/**
 * 🎯 BUSINESS INTELLIGENCE DASHBOARD
 * AI-powered analytics and insights for business performance
 */

import React, { useState, useEffect } from 'react';
import { useAIWithRecovery } from '../../hooks/useAIWithRecovery';
import { StandardCard, StandardButton, LoadingSpinner } from '../ui/StandardizedComponents';
import { 
  ChartBarIcon, 
  TrendingUpIcon, 
  LightBulbIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

interface BusinessMetrics {
  revenue: {
    current: number;
    previous: number;
    trend: 'up' | 'down' | 'stable';
    prediction: number;
  };
  userGrowth: {
    current: number;
    previous: number;
    trend: 'up' | 'down' | 'stable';
    prediction: number;
  };
  conversionRate: {
    current: number;
    previous: number;
    trend: 'up' | 'down' | 'stable';
    prediction: number;
  };
  customerSatisfaction: {
    current: number;
    previous: number;
    trend: 'up' | 'down' | 'stable';
    prediction: number;
  };
}

interface AIInsight {
  id: string;
  type: 'opportunity' | 'risk' | 'recommendation' | 'trend';
  title: string;
  description: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  actionable: boolean;
  suggestedActions: string[];
  dataPoints: string[];
}

interface PredictiveAnalysis {
  timeframe: '1_month' | '3_months' | '6_months' | '1_year';
  predictions: {
    revenue: { value: number; confidence: number };
    users: { value: number; confidence: number };
    marketShare: { value: number; confidence: number };
    risks: Array<{ risk: string; probability: number }>;
    opportunities: Array<{ opportunity: string; potential: number }>;
  };
}

export const BusinessIntelligenceDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<BusinessMetrics | null>(null);
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const [predictions, setPredictions] = useState<PredictiveAnalysis | null>(null);
  const [selectedTimeframe, setSelectedTimeframe] = useState<'1_month' | '3_months' | '6_months' | '1_year'>('3_months');
  const [isGeneratingInsights, setIsGeneratingInsights] = useState(false);

  const { 
    sendChatMessage, 
    isLoading, 
    error 
  } = useAIWithRecovery({
    onSuccess: (response) => {
      console.log('AI analysis completed:', response);
    },
    onError: (error) => {
      console.error('AI analysis failed:', error);
    }
  });

  useEffect(() => {
    loadBusinessMetrics();
    generateAIInsights();
  }, []);

  useEffect(() => {
    if (selectedTimeframe) {
      generatePredictiveAnalysis(selectedTimeframe);
    }
  }, [selectedTimeframe]);

  const loadBusinessMetrics = async () => {
    try {
      // In a real implementation, this would fetch from your analytics API
      const mockMetrics: BusinessMetrics = {
        revenue: { current: 125000, previous: 110000, trend: 'up', prediction: 140000 },
        userGrowth: { current: 1250, previous: 1100, trend: 'up', prediction: 1400 },
        conversionRate: { current: 3.2, previous: 2.8, trend: 'up', prediction: 3.6 },
        customerSatisfaction: { current: 4.3, previous: 4.1, trend: 'up', prediction: 4.5 }
      };
      setMetrics(mockMetrics);
    } catch (error) {
      console.error('Failed to load business metrics:', error);
    }
  };

  const generateAIInsights = async () => {
    setIsGeneratingInsights(true);
    try {
      const context = {
        businessType: 'AI Incubator',
        currentMetrics: metrics,
        timeframe: 'last_30_days',
        analysisType: 'comprehensive_business_intelligence'
      };

      const response = await sendChatMessage(
        'Analyze our business performance and provide actionable insights, opportunities, and risk assessments based on the current metrics and trends.',
        context
      );

      // Parse AI response into structured insights
      const parsedInsights = parseAIInsights(response.content);
      setInsights(parsedInsights);

    } catch (error) {
      console.error('Failed to generate AI insights:', error);
    } finally {
      setIsGeneratingInsights(false);
    }
  };

  const generatePredictiveAnalysis = async (timeframe: string) => {
    try {
      const context = {
        businessType: 'AI Incubator',
        currentMetrics: metrics,
        timeframe,
        analysisType: 'predictive_analysis'
      };

      const response = await sendChatMessage(
        `Generate predictive analysis for the next ${timeframe.replace('_', ' ')} including revenue forecasts, user growth predictions, market opportunities, and potential risks.`,
        context
      );

      // Parse AI response into structured predictions
      const parsedPredictions = parsePredictiveAnalysis(response.content, timeframe);
      setPredictions(parsedPredictions);

    } catch (error) {
      console.error('Failed to generate predictive analysis:', error);
    }
  };

  const parseAIInsights = (aiResponse: string): AIInsight[] => {
    // In a real implementation, this would use more sophisticated parsing
    // For now, return mock structured insights
    return [
      {
        id: '1',
        type: 'opportunity',
        title: 'Expand AI Mentorship Program',
        description: 'User engagement with AI-powered mentorship features is 40% higher than traditional mentoring.',
        confidence: 0.85,
        impact: 'high',
        actionable: true,
        suggestedActions: [
          'Increase AI mentor availability by 50%',
          'Add specialized industry mentors',
          'Implement group mentoring sessions'
        ],
        dataPoints: ['User engagement metrics', 'Session completion rates', 'User feedback scores']
      },
      {
        id: '2',
        type: 'risk',
        title: 'Customer Acquisition Cost Rising',
        description: 'CAC has increased 15% over the last quarter, potentially impacting profitability.',
        confidence: 0.78,
        impact: 'medium',
        actionable: true,
        suggestedActions: [
          'Optimize marketing channels',
          'Improve conversion funnel',
          'Implement referral program'
        ],
        dataPoints: ['Marketing spend', 'Conversion rates', 'Customer lifetime value']
      },
      {
        id: '3',
        type: 'trend',
        title: 'Mobile Usage Increasing',
        description: 'Mobile app usage has grown 60% in the last month, indicating strong mobile-first preference.',
        confidence: 0.92,
        impact: 'high',
        actionable: true,
        suggestedActions: [
          'Prioritize mobile feature development',
          'Optimize mobile user experience',
          'Add mobile-specific features'
        ],
        dataPoints: ['Mobile vs desktop usage', 'Session duration', 'Feature usage patterns']
      }
    ];
  };

  const parsePredictiveAnalysis = (aiResponse: string, timeframe: string): PredictiveAnalysis => {
    // Mock predictive analysis - in real implementation, parse AI response
    return {
      timeframe: timeframe as any,
      predictions: {
        revenue: { value: 180000, confidence: 0.82 },
        users: { value: 1800, confidence: 0.78 },
        marketShare: { value: 12.5, confidence: 0.65 },
        risks: [
          { risk: 'Increased competition from tech giants', probability: 0.35 },
          { risk: 'Economic downturn affecting startup funding', probability: 0.25 },
          { risk: 'AI regulation changes', probability: 0.15 }
        ],
        opportunities: [
          { opportunity: 'Expansion to European markets', potential: 0.75 },
          { opportunity: 'Enterprise B2B partnerships', potential: 0.68 },
          { opportunity: 'AI-powered analytics product', potential: 0.55 }
        ]
      }
    };
  };

  const getInsightIcon = (type: AIInsight['type']) => {
    switch (type) {
      case 'opportunity': return <LightBulbIcon className="w-6 h-6 text-green-500" />;
      case 'risk': return <ExclamationTriangleIcon className="w-6 h-6 text-red-500" />;
      case 'recommendation': return <CheckCircleIcon className="w-6 h-6 text-blue-500" />;
      case 'trend': return <TrendingUpIcon className="w-6 h-6 text-purple-500" />;
      default: return <ChartBarIcon className="w-6 h-6 text-gray-500" />;
    }
  };

  const getImpactColor = (impact: AIInsight['impact']) => {
    switch (impact) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (!metrics) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Business Intelligence</h1>
          <p className="text-white/70 mt-2">AI-powered insights and predictive analytics</p>
        </div>
        <StandardButton
          variant="glass"
          onClick={generateAIInsights}
          isLoading={isGeneratingInsights}
          leftIcon={<ChartBarIcon className="w-5 h-5" />}
        >
          Refresh Insights
        </StandardButton>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StandardCard variant="glass" padding="lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Revenue</p>
              <p className="text-2xl font-bold text-white">${metrics.revenue.current.toLocaleString()}</p>
              <p className={`text-sm ${metrics.revenue.trend === 'up' ? 'text-green-400' : 'text-red-400'}`}>
                {metrics.revenue.trend === 'up' ? '↗' : '↘'} 
                {Math.abs(((metrics.revenue.current - metrics.revenue.previous) / metrics.revenue.previous) * 100).toFixed(1)}%
              </p>
            </div>
            <TrendingUpIcon className="w-8 h-8 text-green-400" />
          </div>
        </StandardCard>

        <StandardCard variant="glass" padding="lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Users</p>
              <p className="text-2xl font-bold text-white">{metrics.userGrowth.current.toLocaleString()}</p>
              <p className={`text-sm ${metrics.userGrowth.trend === 'up' ? 'text-green-400' : 'text-red-400'}`}>
                {metrics.userGrowth.trend === 'up' ? '↗' : '↘'} 
                {Math.abs(((metrics.userGrowth.current - metrics.userGrowth.previous) / metrics.userGrowth.previous) * 100).toFixed(1)}%
              </p>
            </div>
            <ChartBarIcon className="w-8 h-8 text-blue-400" />
          </div>
        </StandardCard>

        <StandardCard variant="glass" padding="lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Conversion Rate</p>
              <p className="text-2xl font-bold text-white">{metrics.conversionRate.current}%</p>
              <p className={`text-sm ${metrics.conversionRate.trend === 'up' ? 'text-green-400' : 'text-red-400'}`}>
                {metrics.conversionRate.trend === 'up' ? '↗' : '↘'} 
                {Math.abs(metrics.conversionRate.current - metrics.conversionRate.previous).toFixed(1)}%
              </p>
            </div>
            <TrendingUpIcon className="w-8 h-8 text-purple-400" />
          </div>
        </StandardCard>

        <StandardCard variant="glass" padding="lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Satisfaction</p>
              <p className="text-2xl font-bold text-white">{metrics.customerSatisfaction.current}/5</p>
              <p className={`text-sm ${metrics.customerSatisfaction.trend === 'up' ? 'text-green-400' : 'text-red-400'}`}>
                {metrics.customerSatisfaction.trend === 'up' ? '↗' : '↘'} 
                {Math.abs(metrics.customerSatisfaction.current - metrics.customerSatisfaction.previous).toFixed(1)}
              </p>
            </div>
            <CheckCircleIcon className="w-8 h-8 text-green-400" />
          </div>
        </StandardCard>
      </div>

      {/* AI Insights */}
      <StandardCard variant="glass" padding="lg">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">AI-Generated Insights</h2>
          {isGeneratingInsights && <LoadingSpinner size="sm" />}
        </div>

        <div className="space-y-4">
          {insights.map((insight) => (
            <div key={insight.id} className="bg-white/5 rounded-lg p-4 border border-white/10">
              <div className="flex items-start space-x-4">
                {getInsightIcon(insight.type)}
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="font-semibold text-white">{insight.title}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getImpactColor(insight.impact)}`}>
                      {insight.impact} impact
                    </span>
                    <span className="text-xs text-white/50">
                      {Math.round(insight.confidence * 100)}% confidence
                    </span>
                  </div>
                  <p className="text-white/70 mb-3">{insight.description}</p>
                  
                  {insight.actionable && insight.suggestedActions.length > 0 && (
                    <div>
                      <p className="text-sm font-medium text-white mb-2">Suggested Actions:</p>
                      <ul className="list-disc list-inside text-sm text-white/60 space-y-1">
                        {insight.suggestedActions.map((action, index) => (
                          <li key={index}>{action}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </StandardCard>

      {/* Predictive Analysis */}
      <StandardCard variant="glass" padding="lg">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">Predictive Analysis</h2>
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value as any)}
            className="bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white text-sm"
          >
            <option value="1_month">1 Month</option>
            <option value="3_months">3 Months</option>
            <option value="6_months">6 Months</option>
            <option value="1_year">1 Year</option>
          </select>
        </div>

        {predictions && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Predictions */}
            <div>
              <h3 className="text-lg font-medium text-white mb-4">Forecasts</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-white/70">Revenue</span>
                  <div className="text-right">
                    <span className="text-white font-medium">${predictions.predictions.revenue.value.toLocaleString()}</span>
                    <span className="text-xs text-white/50 ml-2">
                      ({Math.round(predictions.predictions.revenue.confidence * 100)}% confidence)
                    </span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-white/70">Users</span>
                  <div className="text-right">
                    <span className="text-white font-medium">{predictions.predictions.users.value.toLocaleString()}</span>
                    <span className="text-xs text-white/50 ml-2">
                      ({Math.round(predictions.predictions.users.confidence * 100)}% confidence)
                    </span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-white/70">Market Share</span>
                  <div className="text-right">
                    <span className="text-white font-medium">{predictions.predictions.marketShare.value}%</span>
                    <span className="text-xs text-white/50 ml-2">
                      ({Math.round(predictions.predictions.marketShare.confidence * 100)}% confidence)
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Risks & Opportunities */}
            <div>
              <h3 className="text-lg font-medium text-white mb-4">Risks & Opportunities</h3>
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-red-400 mb-2">Top Risks</h4>
                  <div className="space-y-2">
                    {predictions.predictions.risks.map((risk, index) => (
                      <div key={index} className="flex justify-between items-center text-sm">
                        <span className="text-white/70">{risk.risk}</span>
                        <span className="text-red-400">{Math.round(risk.probability * 100)}%</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium text-green-400 mb-2">Top Opportunities</h4>
                  <div className="space-y-2">
                    {predictions.predictions.opportunities.map((opportunity, index) => (
                      <div key={index} className="flex justify-between items-center text-sm">
                        <span className="text-white/70">{opportunity.opportunity}</span>
                        <span className="text-green-400">{Math.round(opportunity.potential * 100)}%</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </StandardCard>

      {/* Error Display */}
      {error && (
        <StandardCard variant="glass" padding="lg">
          <div className="flex items-center space-x-3 text-red-400">
            <ExclamationTriangleIcon className="w-6 h-6" />
            <div>
              <p className="font-medium">AI Analysis Error</p>
              <p className="text-sm text-white/70">{error.message}</p>
            </div>
          </div>
        </StandardCard>
      )}
    </div>
  );
};

export default BusinessIntelligenceDashboard;
