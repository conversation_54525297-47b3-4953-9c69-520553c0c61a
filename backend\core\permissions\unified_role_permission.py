"""
🎯 UNIFIED ROLE PERMISSION
Consolidated permission class that replaces all scattered permission classes
across the application with a single, consistent interface.

This replaces:
- users/permissions.py (IsAdmin<PERSON>ser, IsSuperAdmin<PERSON>ser, IsOwnerOrAdmin)
- api/permissions.py (IsOwnerOrAdmin)
- api/super_admin_permissions.py (IsSuperAdmin<PERSON>ser, SuperAdminOrOwner, SystemOperationPermission)
- api/ai_permissions.py (AIFeaturePermission)

Provides:
- Single permission class for all role-based access control
- Consistent permission checking patterns
- Flexible role and permission level checking
- Object-level permissions with owner checks
- System operation permissions
- AI feature permissions
"""

from rest_framework import permissions
from django.contrib.auth.models import User
from typing import List, Optional, Union
import logging

from ..services.unified_role_service import unified_role_service

# CONSOLIDATED: Import from unified role configuration
from core.config.role_config import SYSTEM_OPERATIONS

logger = logging.getLogger(__name__)


class UnifiedRolePermission(permissions.BasePermission):
    """
    🎯 UNIFIED ROLE PERMISSION
    Single permission class that handles all role-based access control
    """
    
    # Default permission requirements (can be overridden in views)
    required_roles: Optional[List[str]] = None
    required_permission_level: Optional[str] = None
    required_any_role: bool = True  # True = any role, False = all roles
    allow_owner_access: bool = True  # Allow object owners to access
    required_system_operation: Optional[str] = None
    required_ai_capability: Optional[str] = None
    
    def has_permission(self, request, view):
        """
        Check if user has permission to access the view
        
        Args:
            request: HTTP request object
            view: Django view object
            
        Returns:
            bool: True if user has permission
        """
        user = request.user
        
        # Check authentication
        if not user or not user.is_authenticated:
            return False
        
        # Get permission requirements from view or class
        required_roles = getattr(view, 'required_roles', self.required_roles)
        required_permission_level = getattr(view, 'required_permission_level', self.required_permission_level)
        required_any_role = getattr(view, 'required_any_role', self.required_any_role)
        required_system_operation = getattr(view, 'required_system_operation', self.required_system_operation)
        required_ai_capability = getattr(view, 'required_ai_capability', self.required_ai_capability)
        
        # Check system operation permissions (highest level)
        if required_system_operation:
            return self._check_system_operation_permission(user, required_system_operation)
        
        # Check AI capability permissions
        if required_ai_capability:
            return self._check_ai_capability_permission(user, required_ai_capability)
        
        # Check role-based permissions
        if required_roles:
            if required_any_role:
                if not unified_role_service.has_any_role(user, required_roles):
                    return False
            else:
                if not unified_role_service.has_all_roles(user, required_roles):
                    return False
        
        # Check permission level
        if required_permission_level:
            if not unified_role_service.has_permission(user, required_permission_level):
                return False
        
        return True
    
    def has_object_permission(self, request, view, obj):
        """
        Check if user has permission to access a specific object
        
        Args:
            request: HTTP request object
            view: Django view object
            obj: Object being accessed
            
        Returns:
            bool: True if user has permission
        """
        user = request.user
        
        # Check authentication
        if not user or not user.is_authenticated:
            return False
        
        # Super admin has access to everything
        if unified_role_service.has_role(user, 'super_admin'):
            return True
        
        # Admin has access to most things
        if unified_role_service.has_role(user, 'admin'):
            return True
        
        # Check owner access if enabled
        allow_owner_access = getattr(view, 'allow_owner_access', self.allow_owner_access)
        if allow_owner_access and self._is_object_owner(user, obj):
            return True
        
        # Check role-based object permissions
        required_roles = getattr(view, 'required_roles', self.required_roles)
        if required_roles:
            required_any_role = getattr(view, 'required_any_role', self.required_any_role)
            
            if required_any_role:
                return unified_role_service.has_any_role(user, required_roles)
            else:
                return unified_role_service.has_all_roles(user, required_roles)
        
        # Check permission level for object access
        required_permission_level = getattr(view, 'required_permission_level', self.required_permission_level)
        if required_permission_level:
            return unified_role_service.has_permission(user, required_permission_level)
        
        # Default to denying access if no specific permissions are set
        return False
    
    # ========================================
    # PRIVATE HELPER METHODS
    # ========================================
    
    def _check_system_operation_permission(self, user: User, operation: str) -> bool:
        """
        Check if user has permission for system operations
        
        Args:
            user: Django User instance
            operation: System operation name
            
        Returns:
            bool: True if user has permission
        """
        # Only super admin can perform system operations
        if not unified_role_service.has_role(user, 'super_admin'):
            return False
        
        # CONSOLIDATED: Use unified role configuration
        # SYSTEM_OPERATIONS is now imported from role_config
        
        if operation not in SYSTEM_OPERATIONS:
            logger.warning(f"Unknown system operation requested: {operation}")
            return False
        
        return True
    
    def _check_ai_capability_permission(self, user: User, capability_id: str) -> bool:
        """
        Check if user has access to specific AI capability
        
        Args:
            user: Django User instance
            capability_id: AI capability identifier
            
        Returns:
            bool: True if user has permission
        """
        user_capabilities = self._get_user_ai_capabilities(user)
        return any(cap['id'] == capability_id for cap in user_capabilities)
    
    def _get_user_ai_capabilities(self, user: User) -> List[dict]:
        """
        Get AI capabilities for a user based on their roles
        
        Args:
            user: Django User instance
            
        Returns:
            List[dict]: List of AI capabilities
        """
        user_roles = unified_role_service.get_user_roles(user)
        
        # Define AI capabilities by role
        role_capabilities = {
            'user': [
                {'id': 'basic_chat', 'name': 'Basic AI Chat'},
                {'id': 'language_detection', 'name': 'Language Detection'}
            ],
            'entrepreneur': [
                {'id': 'basic_chat', 'name': 'Basic AI Chat'},
                {'id': 'business_analysis', 'name': 'Business Analysis'},
                {'id': 'idea_validation', 'name': 'Idea Validation'},
                {'id': 'market_research', 'name': 'Market Research'},
                {'id': 'language_detection', 'name': 'Language Detection'}
            ],
            'mentor': [
                {'id': 'basic_chat', 'name': 'Basic AI Chat'},
                {'id': 'mentorship_ai', 'name': 'Mentorship AI'},
                {'id': 'business_analysis', 'name': 'Business Analysis'},
                {'id': 'language_detection', 'name': 'Language Detection'}
            ],
            'investor': [
                {'id': 'basic_chat', 'name': 'Basic AI Chat'},
                {'id': 'investment_analysis', 'name': 'Investment Analysis'},
                {'id': 'due_diligence', 'name': 'Due Diligence AI'},
                {'id': 'market_analysis', 'name': 'Market Analysis'},
                {'id': 'language_detection', 'name': 'Language Detection'}
            ],
            'moderator': [
                {'id': 'basic_chat', 'name': 'Basic AI Chat'},
                {'id': 'content_moderation', 'name': 'Content Moderation AI'},
                {'id': 'language_detection', 'name': 'Language Detection'}
            ],
            'admin': [
                {'id': 'basic_chat', 'name': 'Basic AI Chat'},
                {'id': 'business_analysis', 'name': 'Business Analysis'},
                {'id': 'admin_analytics', 'name': 'Admin Analytics'},
                {'id': 'system_monitoring', 'name': 'System Monitoring'},
                {'id': 'language_detection', 'name': 'Language Detection'}
            ],
            'super_admin': [
                {'id': 'basic_chat', 'name': 'Basic AI Chat'},
                {'id': 'business_analysis', 'name': 'Business Analysis'},
                {'id': 'admin_analytics', 'name': 'Admin Analytics'},
                {'id': 'system_monitoring', 'name': 'System Monitoring'},
                {'id': 'ai_configuration', 'name': 'AI Configuration'},
                {'id': 'model_management', 'name': 'Model Management'},
                {'id': 'language_detection', 'name': 'Language Detection'}
            ]
        }
        
        # Collect all capabilities for user's roles
        capabilities = []
        capability_ids = set()
        
        for role in user_roles:
            role_caps = role_capabilities.get(role, [])
            for cap in role_caps:
                if cap['id'] not in capability_ids:
                    capabilities.append(cap)
                    capability_ids.add(cap['id'])
        
        return capabilities
    
    def _is_object_owner(self, user: User, obj) -> bool:
        """
        Check if user is the owner of an object
        
        Args:
            user: Django User instance
            obj: Object to check ownership
            
        Returns:
            bool: True if user owns the object
        """
        # Check direct owner field
        if hasattr(obj, 'owner'):
            return obj.owner == user
        
        # Check user field
        if hasattr(obj, 'user'):
            return obj.user == user
        
        # Check if object is the user itself
        if hasattr(obj, 'id') and hasattr(user, 'id'):
            return obj.id == user.id
        
        # Check business idea ownership
        if hasattr(obj, 'business_idea'):
            if hasattr(obj.business_idea, 'owner') and obj.business_idea.owner == user:
                return True
            
            # Check collaborators
            if hasattr(obj.business_idea, 'collaborators'):
                return obj.business_idea.collaborators.filter(id=user.id).exists()
        
        # Check profile ownership
        if hasattr(obj, 'user_profile'):
            return obj.user_profile.user == user
        
        return False


# ========================================
# CONVENIENCE PERMISSION CLASSES
# ========================================

class SuperAdminOnly(UnifiedRolePermission):
    """Permission class for super admin only access"""
    required_roles = ['super_admin']
    required_any_role = False
    allow_owner_access = False


class AdminOnly(UnifiedRolePermission):
    """Permission class for admin and super admin access"""
    required_roles = ['admin', 'super_admin']
    required_any_role = True
    allow_owner_access = False


class ModeratorOrHigher(UnifiedRolePermission):
    """Permission class for moderator, admin, and super admin access"""
    required_roles = ['moderator', 'admin', 'super_admin']
    required_any_role = True
    allow_owner_access = False


class BusinessRoles(UnifiedRolePermission):
    """Permission class for business roles (entrepreneur, mentor, investor)"""
    required_roles = ['entrepreneur', 'mentor', 'investor', 'admin', 'super_admin']
    required_any_role = True
    allow_owner_access = True


class AuthenticatedUsers(UnifiedRolePermission):
    """Permission class for all authenticated users"""
    required_roles = ['user', 'entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin']
    required_any_role = True
    allow_owner_access = True


class OwnerOrAdmin(UnifiedRolePermission):
    """Permission class for object owners or admins"""
    required_roles = ['admin', 'super_admin']
    required_any_role = True
    allow_owner_access = True


# ========================================
# LEGACY COMPATIBILITY
# ========================================

# Provide legacy class names for backward compatibility
IsAdminUser = AdminOnly
IsSuperAdminUser = SuperAdminOnly
IsOwnerOrAdmin = OwnerOrAdmin
AIFeaturePermission = UnifiedRolePermission
SuperAdminOrOwner = OwnerOrAdmin
SystemOperationPermission = SuperAdminOnly
