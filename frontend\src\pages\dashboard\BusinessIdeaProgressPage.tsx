import React, { useState, useEffect } from 'react';
import { useP<PERSON><PERSON>, Link } from 'react-router-dom';
import { ArrowLeft, Lightbulb, Target, Users, BarChart2, Sparkles } from 'lucide-react';
// MainLayout removed - handled by routing system
import BusinessIdeaProgressTracking from '../../components/dashboard/BusinessIdeaProgressTracking';
import MilestoneTracking from '../../components/dashboard/MilestoneTracking';
import GoalTracking from '../../components/dashboard/GoalTracking';
import MentorRecommendations from '../../components/dashboard/MentorRecommendations';
import BusinessAnalyticsComponent from '../../components/dashboard/BusinessAnalytics';
import AIRecommendations from '../../components/dashboard/AIRecommendations';
// ✅ UPDATED: Using UnifiedAIChat instead of duplicate chat components
import { UnifiedAIChat } from '../../components/ai';
import { businessIdeasAPI, BusinessIdea } from '../../services/incubatorApi';

import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
const BusinessIdeaProgressPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const { id } = useParams<{ id: string }>();
  const businessIdeaId = parseInt(id || '0', 10);
  const [businessIdea, setBusinessIdea] = useState<BusinessIdea | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    const fetchBusinessIdea = async () => {
      setLoading(true);
      try {
        const data = await businessIdeasAPI.getBusinessIdea(businessIdeaId);
        setBusinessIdea(data);
      } catch (err) {
        console.error('Error fetching business idea:', err);
      } finally {
        setLoading(false);
      }
    };

    if (businessIdeaId) {
      fetchBusinessIdea();
    }
  }, [businessIdeaId]);

  const renderTabContent = () => {
    if (loading || !businessIdea) {
      return (
        <div className={`flex justify-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      );
    }

    switch (activeTab) {
      case 'overview':
        return <BusinessIdeaProgressTracking businessIdeaId={businessIdeaId} />;
      case 'milestones':
        return <MilestoneTracking businessIdeaId={businessIdeaId} businessIdeaTitle={businessIdea.title} />;
      case 'goals':
        return <GoalTracking businessIdeaId={businessIdeaId} businessIdeaTitle={businessIdea.title} />;
      case 'mentors':
        return <MentorRecommendations businessIdeaId={businessIdeaId} businessIdeaTitle={businessIdea.title} />;
      case 'analytics':
        return <BusinessAnalyticsComponent businessIdeaId={businessIdeaId} businessIdeaTitle={businessIdea.title} />;
      case 'ai_recommendations':
        return <AIRecommendations businessIdeaId={businessIdeaId} businessIdeaTitle={businessIdea.title} />;
      default:
        return <BusinessIdeaProgressTracking businessIdeaId={businessIdeaId} />;
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="p-6">
        <div className="mb-6">
          <Link
            to={"/dashboard/business-ideas/" + businessIdeaId}
            className={`text-purple-400 hover:text-purple-300 flex items-center mb-4 ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <ArrowLeft size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} /> Back to Idea Details
          </Link>
          <h1 className="text-2xl font-bold text-white">
            {loading ? 'Business Idea Progress' : `${businessIdea?.title} - Progress`}
          </h1>
          <div className="text-gray-300 mt-1">
            Track the progress and performance of your business idea
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-6 border-b border-gray-700">
          <div className={`flex overflow-x-auto ${isRTL ? "flex-row-reverse" : ""}`}>
            <button
              onClick={() => setActiveTab('overview')}
              className={`px-4 py-2 flex items-center whitespace-nowrap ${
                activeTab === 'overview'
                  ? 'text-purple-400 border-b-2 border-purple-400 font-medium'
                  : 'text-gray-400 hover:text-gray-300'}
              }`}
            >
              <Lightbulb size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} /> Overview
            </button>
            <button
              onClick={() => setActiveTab('milestones')}
              className={`px-4 py-2 flex items-center whitespace-nowrap ${
                activeTab === 'milestones'
                  ? 'text-purple-400 border-b-2 border-purple-400 font-medium'
                  : 'text-gray-400 hover:text-gray-300'}
              }`}
            >
              <Target size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} /> Milestones
            </button>
            <button
              onClick={() => setActiveTab('goals')}
              className={`px-4 py-2 flex items-center whitespace-nowrap ${
                activeTab === 'goals'
                  ? 'text-purple-400 border-b-2 border-purple-400 font-medium'
                  : 'text-gray-400 hover:text-gray-300'}
              }`}
            >
              <Target size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} /> Strategic Goals
            </button>
            <button
              onClick={() => setActiveTab('mentors')}
              className={`px-4 py-2 flex items-center whitespace-nowrap ${
                activeTab === 'mentors'
                  ? 'text-purple-400 border-b-2 border-purple-400 font-medium'
                  : 'text-gray-400 hover:text-gray-300'}
              }`}
            >
              <Users size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} /> Mentor Matching
            </button>
            <button
              onClick={() => setActiveTab('analytics')}
              className={`px-4 py-2 flex items-center whitespace-nowrap ${
                activeTab === 'analytics'
                  ? 'text-purple-400 border-b-2 border-purple-400 font-medium'
                  : 'text-gray-400 hover:text-gray-300'}
              }`}
            >
              <BarChart2 size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} /> Comparative Analytics
            </button>
            <button
              onClick={() => setActiveTab('ai_recommendations')}
              className={`px-4 py-2 flex items-center whitespace-nowrap ${
                activeTab === 'ai_recommendations'
                  ? 'text-purple-400 border-b-2 border-purple-400 font-medium'
                  : 'text-gray-400 hover:text-gray-300'}
              }`}
            >
              <Sparkles size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} /> AI Recommendations
            </button>
          </div>
        </div>

        {/* Tab Content */}
        <div>
          {renderTabContent()}
        </div>
      </div>

      {/* ✅ UPDATED: Unified AI Chat Widget */}
      {!loading && businessIdea && (
        <UnifiedAIChat
          mode="floating"
          businessIdeaId={businessIdeaId}
          businessContext={{
            title: businessIdea.title,
            description: businessIdea.description,
            category: businessIdea.category,
            stage: businessIdea.current_stage
          }}
          className="z-50"
        />
      )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessIdeaProgressPage;
