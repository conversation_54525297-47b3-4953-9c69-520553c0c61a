import React, { useState, useEffect } from 'react';
import { Save, X, AlertCircle, Clock, Globe, Users, Lock } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import BaseModal from '../common/BaseModal';
import EnhancedRichTextEditor from '../common/EnhancedRichTextEditor';
import EnhancedImageUpload from '../common/EnhancedImageUpload';
import { communityApi } from '../../services/communityApi';

interface Post {
  id: string;
  title?: string;
  content: string;
  tag_names?: string[];
  visibility?: 'public' | 'followers' | 'private';
  media?: Array<{ id: string; url: string; type: string }>;
  created_at: string;
  updated_at?: string;
  author: {
    id: string;
    username: string;
    avatar?: string;
  };
}

interface EditPostModalProps {
  isOpen: boolean;
  onClose: () => void;
  post: Post;
  onUpdate: (updatedPost: Post) => void;
}

const EditPostModal: React.FC<EditPostModalProps> = ({
  isOpen,
  onClose,
  post,
  onUpdate
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [title, setTitle] = useState(post.title || '');
  const [content, setContent] = useState(post.content);
  const [tags, setTags] = useState(post.tag_names?.join(', ') || '');
  const [visibility, setVisibility] = useState<'public' | 'followers' | 'private'>(
    post.visibility || 'public'
  );
  const [images, setImages] = useState<any[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [mentions, setMentions] = useState<Array<{ id: string; username: string; avatar?: string }>>([]);
  const [hashtags, setHashtags] = useState<Array<{ name: string; count: number }>>([]);

  // Initialize form data
  useEffect(() => {
    if (post) {
      setTitle(post.title || '');
      setContent(post.content);
      setTags(post.tag_names?.join(', ') || '');
      setVisibility(post.visibility || 'public');
      
      // Convert existing media to image format
      if (post.media) {
        const existingImages = post.media.map(media => ({
          id: media.id,
          file: null,
          url: media.url,
          preview: media.url,
          uploading: false,
          uploaded: true,
          type: media.type
        }));
        setImages(existingImages);
      }
    }
  }, [post]);

  // Check for changes
  useEffect(() => {
    const originalTags = post.tag_names?.join(', ') || '';
    const currentTags = tags.trim();
    
    const changed = 
      title !== (post.title || '') ||
      content !== post.content ||
      currentTags !== originalTags ||
      visibility !== (post.visibility || 'public') ||
      images.length !== (post.media?.length || 0);
    
    setHasChanges(changed);
  }, [title, content, tags, visibility, images, post]);

  // Handle mention search
  const handleMentionSearch = async (query: string) => {
    if (query.length < 2) {
      setMentions([]);
      return;
    }

    try {
      const results = await communityApi.searchUsers(query);
      setMentions(results.slice(0, 5));
    } catch (error) {
      console.error('Error searching mentions:', error);
    }
  };

  // Handle hashtag search
  const handleHashtagSearch = async (query: string) => {
    if (query.length < 1) {
      setHashtags([]);
      return;
    }

    try {
      const results = await communityApi.searchHashtags(query);
      setHashtags(results.slice(0, 5));
    } catch (error) {
      console.error('Error searching hashtags:', error);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!hasChanges) {
      onClose();
      return;
    }

    if (!content.trim()) {
      setError('Post content cannot be empty');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const tagNames = tags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      const mediaUrls = images
        .filter(img => img.uploaded)
        .map(img => img.url);

      const updateData = {
        title: title.trim() || undefined,
        content: content.trim(),
        tag_names: tagNames.length > 0 ? tagNames : undefined,
        visibility,
        media: mediaUrls.length > 0 ? mediaUrls : undefined
      };

      const updatedPost = await communityApi.updatePost(post.id, updateData);
      onUpdate(updatedPost);
      onClose();
    } catch (error) {
      console.error('Error updating post:', error);
      setError(error instanceof Error ? error.message : 'Failed to update post');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle close with unsaved changes warning
  const handleClose = () => {
    if (hasChanges) {
      const confirmed = window.confirm(
        'You have unsaved changes. Are you sure you want to close without saving?'
      );
      if (!confirmed) return;
    }
    onClose();
  };

  const visibilityOptions = [
    { value: 'public', label: 'Public', icon: Globe, description: 'Anyone can see this post' },
    { value: 'followers', label: 'Followers', icon: Users, description: 'Only your followers can see this post' },
    { value: 'private', label: 'Private', icon: Lock, description: 'Only you can see this post' }
  ];

  return (
    <BaseModal isOpen={isOpen} onClose={handleClose} className="max-w-4xl">
      <div className="flex items-center justify-between p-6 border-b border-white/20">
        <div className="flex items-center gap-3">
          <h2 className="text-xl font-semibold text-white">Edit Post</h2>
          {hasChanges && (
            <div className="flex items-center gap-1 text-sm text-amber-400">
              <Clock className="w-4 h-4" />
              <span>Unsaved changes</span>
            </div>
          )}
        </div>
        <button
          onClick={handleClose}
          className="p-2 hover:bg-white/10 rounded-lg transition-colors text-white/80 hover:text-white"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        {/* Error Message */}
        {error && (
          <div className="flex items-center gap-2 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400">
            <AlertCircle className="w-5 h-5 flex-shrink-0" />
            <span>{error}</span>
          </div>
        )}

        {/* Title (Optional) */}
        <div>
          <label className="block text-sm font-medium text-white/90 mb-2">
            Title (Optional)
          </label>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Add a title to your post..."
            className="w-full px-3 py-2 bg-white/5 border border-white/20 rounded-lg focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 text-white placeholder-white/50 backdrop-blur-sm"
            maxLength={200}
          />
        </div>

        {/* Content */}
        <div>
          <label className="block text-sm font-medium text-white/90 mb-2">
            Content *
          </label>
          <EnhancedRichTextEditor
            value={content}
            onChange={setContent}
            placeholder="What's on your mind?"
            maxLength={2000}
            onMentionSearch={handleMentionSearch}
            onHashtagSearch={handleHashtagSearch}
            mentions={mentions}
            hashtags={hashtags}
            className="bg-white/5 border border-white/20 rounded-lg backdrop-blur-sm"
          />
        </div>

        {/* Images */}
        <div>
          <label className="block text-sm font-medium text-white/90 mb-2">
            Images
          </label>
          <EnhancedImageUpload
            images={images}
            onImagesChange={setImages}
            maxImages={4}
            maxFileSize={10}
          />
        </div>

        {/* Tags */}
        <div>
          <label className="block text-sm font-medium text-white/90 mb-2">
            Tags
          </label>
          <input
            type="text"
            value={tags}
            onChange={(e) => setTags(e.target.value)}
            placeholder="Add tags separated by commas (e.g., technology, programming, web)"
            className="w-full px-3 py-2 bg-white/5 border border-white/20 rounded-lg focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 text-white placeholder-white/50 backdrop-blur-sm"
          />
          <p className="text-sm text-white/60 mt-1">
            Separate multiple tags with commas
          </p>
        </div>

        {/* Visibility */}
        <div>
          <label className="block text-sm font-medium text-white/90 mb-2">
            Visibility
          </label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {visibilityOptions.map((option) => {
              const IconComponent = option.icon;
              return (
                <label
                  key={option.value}
                  className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors backdrop-blur-sm ${
                    visibility === option.value
                      ? 'border-purple-500/50 bg-purple-500/10'
                      : 'border-white/20 hover:bg-white/5'
                  }`}
                >
                  <input
                    type="radio"
                    name="visibility"
                    value={option.value}
                    checked={visibility === option.value}
                    onChange={(e) => setVisibility(e.target.value as any)}
                    className="sr-only"
                  />
                  <IconComponent className="w-5 h-5 mr-3 text-white/80" />
                  <div>
                    <div className="font-medium text-white">{option.label}</div>
                    <div className="text-sm text-white/60">{option.description}</div>
                  </div>
                </label>
              );
            })}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-white/20">
          <div className="text-sm text-white/60">
            Last updated: {new Date(post.updated_at || post.created_at).toLocaleString()}
          </div>
          <div className="flex items-center gap-3">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-white/80 bg-white/10 hover:bg-white/20 rounded-lg transition-colors backdrop-blur-sm"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || !hasChanges}
              className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Save className="w-4 h-4" />
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      </form>
    </BaseModal>
  );
};

export default EditPostModal;
