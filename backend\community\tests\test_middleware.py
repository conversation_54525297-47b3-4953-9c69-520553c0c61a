"""
Test Community Security Middleware
Tests rate limiting, security headers, and request validation
"""

import json
import time
from unittest.mock import patch, MagicMock
from django.test import TestCase, RequestFactory
from django.contrib.auth.models import User
from django.http import JsonResponse, HttpResponse
from django.core.cache import cache
from django.conf import settings

from ..middleware import CommunitySecurityMiddleware, CommunityAuthenticationMiddleware


class CommunitySecurityMiddlewareTest(TestCase):
    """Test community security middleware functionality"""
    
    def setUp(self):
        self.factory = RequestFactory()
        self.middleware = CommunitySecurityMiddleware(get_response=lambda r: HttpResponse())
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        # Clear cache before each test
        cache.clear()
    
    def test_non_community_requests_pass_through(self):
        """Test that non-community requests are not affected"""
        request = self.factory.get('/api/other/endpoint/')
        request.user = self.user
        
        # Should return None (pass through)
        response = self.middleware.process_request(request)
        self.assertIsNone(response)
    
    def test_security_headers_added_to_community_responses(self):
        """Test that security headers are added to community API responses"""
        request = self.factory.get('/api/community/posts/')
        request.user = self.user
        
        original_response = HttpResponse()
        response = self.middleware.process_response(request, original_response)
        
        # Check security headers
        self.assertEqual(response['X-Content-Type-Options'], 'nosniff')
        self.assertEqual(response['X-Frame-Options'], 'DENY')
        self.assertEqual(response['X-XSS-Protection'], '1; mode=block')
        self.assertEqual(response['Referrer-Policy'], 'strict-origin-when-cross-origin')
        self.assertEqual(response['Cache-Control'], 'no-cache, no-store, must-revalidate')
    
    def test_rate_limiting_for_authenticated_user(self):
        """Test rate limiting for authenticated users"""
        request = self.factory.post('/api/community/posts/')
        request.user = self.user
        request.path = '/api/community/posts/'
        request.method = 'POST'
        
        # First request should pass
        response = self.middleware.process_request(request)
        self.assertIsNone(response)
        
        # Simulate multiple requests to exceed limit
        for i in range(10):  # Limit is 10 posts per hour
            response = self.middleware.process_request(request)
            if i < 9:  # First 9 should pass (we already made 1)
                self.assertIsNone(response)
        
        # 11th request should be rate limited
        response = self.middleware.process_request(request)
        self.assertIsInstance(response, JsonResponse)
        self.assertEqual(response.status_code, 429)
    
    def test_rate_limiting_for_anonymous_user(self):
        """Test rate limiting for anonymous users (IP-based)"""
        request = self.factory.get('/api/community/posts/')
        request.user = MagicMock()
        request.user.is_authenticated = False
        request.path = '/api/community/posts/'
        request.method = 'GET'
        request.META = {'REMOTE_ADDR': '***********'}
        
        # Make requests up to the limit
        for i in range(100):  # Limit is 100 GET requests per hour
            response = self.middleware.process_request(request)
            if i < 99:
                self.assertIsNone(response)
        
        # 101st request should be rate limited
        response = self.middleware.process_request(request)
        self.assertIsInstance(response, JsonResponse)
        self.assertEqual(response.status_code, 429)
    
    def test_rate_limit_pattern_matching(self):
        """Test rate limiting with pattern matching for dynamic URLs"""
        request = self.factory.post('/api/community/posts/123/like/')
        request.user = self.user
        request.path = '/api/community/posts/123/like/'
        request.method = 'POST'
        
        # Should match the pattern '/api/community/posts/*/like/'
        response = self.middleware.process_request(request)
        self.assertIsNone(response)  # First request should pass
        
        # Test with different post ID - should use same rate limit
        request.path = '/api/community/posts/456/like/'
        response = self.middleware.process_request(request)
        self.assertIsNone(response)  # Should still be within limit
    
    def test_request_size_validation(self):
        """Test request size validation"""
        # Large request
        large_data = json.dumps({'content': 'x' * (11 * 1024 * 1024)})  # 11MB
        request = self.factory.post(
            '/api/community/posts/',
            data=large_data,
            content_type='application/json'
        )
        request.user = self.user
        request.path = '/api/community/posts/'
        request.method = 'POST'
        request.META = {'CONTENT_LENGTH': str(len(large_data))}
        
        response = self.middleware.process_request(request)
        
        self.assertIsInstance(response, JsonResponse)
        self.assertEqual(response.status_code, 413)
        self.assertIn('Request too large', response.content.decode())
    
    def test_invalid_json_validation(self):
        """Test invalid JSON validation"""
        request = self.factory.post(
            '/api/community/posts/',
            data='{"invalid": json}',  # Invalid JSON
            content_type='application/json'
        )
        request.user = self.user
        request.path = '/api/community/posts/'
        request.method = 'POST'
        request.body = b'{"invalid": json}'
        
        response = self.middleware.process_request(request)
        
        self.assertIsInstance(response, JsonResponse)
        self.assertEqual(response.status_code, 400)
        self.assertIn('Invalid JSON', response.content.decode())
    
    def test_get_client_ip_with_forwarded_header(self):
        """Test client IP extraction with X-Forwarded-For header"""
        request = self.factory.get('/api/community/posts/')
        request.user = self.user
        request.META = {
            'HTTP_X_FORWARDED_FOR': '***********, ************',
            'REMOTE_ADDR': '***********'
        }
        
        ip = self.middleware._get_client_ip(request)
        self.assertEqual(ip, '***********')  # Should use first IP from forwarded header
    
    def test_get_client_ip_without_forwarded_header(self):
        """Test client IP extraction without X-Forwarded-For header"""
        request = self.factory.get('/api/community/posts/')
        request.user = self.user
        request.META = {'REMOTE_ADDR': '***********'}
        
        ip = self.middleware._get_client_ip(request)
        self.assertEqual(ip, '***********')
    
    @patch('community.middleware.security_logger.log_rate_limit_exceeded')
    def test_rate_limit_logging(self, mock_log):
        """Test that rate limit exceeded events are logged"""
        request = self.factory.post('/api/community/posts/')
        request.user = self.user
        request.path = '/api/community/posts/'
        request.method = 'POST'
        request.META = {'REMOTE_ADDR': '***********'}
        
        # Exceed rate limit
        for i in range(11):  # Limit is 10
            self.middleware.process_request(request)
        
        # Should have logged the rate limit exceeded event
        mock_log.assert_called_once()
        call_args = mock_log.call_args[1]
        self.assertEqual(call_args['user_id'], self.user.id)
        self.assertEqual(call_args['endpoint'], '/api/community/posts/')
        self.assertEqual(call_args['ip_address'], '***********')
    
    def test_cors_headers_with_allowed_origins(self):
        """Test CORS headers when origins are configured"""
        request = self.factory.get('/api/community/posts/')
        request.user = self.user
        request.META = {'HTTP_ORIGIN': 'https://example.com'}
        
        # Mock settings
        with patch.object(settings, 'CORS_ALLOWED_ORIGINS', ['https://example.com']):
            original_response = HttpResponse()
            response = self.middleware.process_response(request, original_response)
            
            self.assertEqual(response['Access-Control-Allow-Origin'], 'https://example.com')
    
    def test_cors_headers_with_disallowed_origins(self):
        """Test CORS headers when origin is not allowed"""
        request = self.factory.get('/api/community/posts/')
        request.user = self.user
        request.META = {'HTTP_ORIGIN': 'https://malicious.com'}
        
        # Mock settings
        with patch.object(settings, 'CORS_ALLOWED_ORIGINS', ['https://example.com']):
            original_response = HttpResponse()
            response = self.middleware.process_response(request, original_response)
            
            self.assertNotIn('Access-Control-Allow-Origin', response)


class CommunityAuthenticationMiddlewareTest(TestCase):
    """Test community authentication middleware"""
    
    def setUp(self):
        self.factory = RequestFactory()
        self.middleware = CommunityAuthenticationMiddleware(get_response=lambda r: HttpResponse())
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        # Clear cache before each test
        cache.clear()
    
    def test_non_community_requests_pass_through(self):
        """Test that non-community requests are not affected"""
        request = self.factory.get('/api/other/endpoint/')
        request.user = self.user
        
        response = self.middleware.process_request(request)
        self.assertIsNone(response)
    
    def test_authenticated_user_passes_through(self):
        """Test that authenticated users pass through without issues"""
        request = self.factory.get('/api/community/posts/')
        request.user = self.user
        
        response = self.middleware.process_request(request)
        self.assertIsNone(response)
    
    @patch('community.middleware.security_logger.log_suspicious_activity')
    def test_multiple_failed_auth_attempts_logged(self, mock_log):
        """Test that multiple failed auth attempts are logged"""
        request = self.factory.get('/api/community/posts/')
        request.user = MagicMock()
        request.user.is_authenticated = False
        request.META = {
            'HTTP_AUTHORIZATION': 'Bearer invalid_token',
            'REMOTE_ADDR': '***********'
        }
        
        # Simulate multiple failed attempts
        for i in range(12):  # Threshold is 10
            self.middleware.process_request(request)
        
        # Should have logged suspicious activity
        mock_log.assert_called_once()
        call_args = mock_log.call_args[1]
        self.assertEqual(call_args['user_id'], None)
        self.assertEqual(call_args['activity_type'], 'multiple_failed_auth')
        self.assertIn('ip_address', call_args['details'])
        self.assertIn('attempts', call_args['details'])
    
    def test_no_auth_header_no_logging(self):
        """Test that requests without auth headers don't trigger logging"""
        request = self.factory.get('/api/community/posts/')
        request.user = MagicMock()
        request.user.is_authenticated = False
        request.META = {'REMOTE_ADDR': '***********'}
        
        with patch('community.middleware.security_logger.log_suspicious_activity') as mock_log:
            self.middleware.process_request(request)
            mock_log.assert_not_called()
    
    def test_get_client_ip_method(self):
        """Test client IP extraction in auth middleware"""
        request = self.factory.get('/api/community/posts/')
        request.META = {
            'HTTP_X_FORWARDED_FOR': '***********, ************',
            'REMOTE_ADDR': '***********'
        }
        
        ip = self.middleware._get_client_ip(request)
        self.assertEqual(ip, '***********')


class MiddlewareIntegrationTest(TestCase):
    """Integration tests for middleware components"""
    
    def setUp(self):
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        cache.clear()
    
    def test_middleware_chain_processing(self):
        """Test that both middleware components work together"""
        security_middleware = CommunitySecurityMiddleware(get_response=lambda r: HttpResponse())
        auth_middleware = CommunityAuthenticationMiddleware(get_response=lambda r: HttpResponse())
        
        request = self.factory.post('/api/community/posts/')
        request.user = self.user
        request.path = '/api/community/posts/'
        request.method = 'POST'
        request.META = {'REMOTE_ADDR': '***********'}
        
        # Process through both middleware
        auth_response = auth_middleware.process_request(request)
        self.assertIsNone(auth_response)  # Should pass through
        
        security_response = security_middleware.process_request(request)
        self.assertIsNone(security_response)  # Should pass through
        
        # Test response processing
        original_response = HttpResponse()
        final_response = security_middleware.process_response(request, original_response)
        
        # Should have security headers
        self.assertIn('X-Content-Type-Options', final_response)
        self.assertIn('X-Frame-Options', final_response)
    
    def test_rate_limiting_across_different_endpoints(self):
        """Test rate limiting works independently for different endpoints"""
        middleware = CommunitySecurityMiddleware(get_response=lambda r: HttpResponse())
        
        # Test posts endpoint
        posts_request = self.factory.post('/api/community/posts/')
        posts_request.user = self.user
        posts_request.path = '/api/community/posts/'
        posts_request.method = 'POST'
        
        # Test comments endpoint
        comments_request = self.factory.post('/api/community/comments/')
        comments_request.user = self.user
        comments_request.path = '/api/community/comments/'
        comments_request.method = 'POST'
        
        # Make requests to both endpoints
        for i in range(5):
            posts_response = middleware.process_request(posts_request)
            comments_response = middleware.process_request(comments_request)
            
            self.assertIsNone(posts_response)
            self.assertIsNone(comments_response)
        
        # Both should still be within their respective limits
        posts_response = middleware.process_request(posts_request)
        comments_response = middleware.process_request(comments_request)
        
        self.assertIsNone(posts_response)
        self.assertIsNone(comments_response)
    
    @patch('community.middleware.security_logger')
    def test_comprehensive_security_logging(self, mock_logger):
        """Test comprehensive security event logging"""
        security_middleware = CommunitySecurityMiddleware(get_response=lambda r: HttpResponse())
        auth_middleware = CommunityAuthenticationMiddleware(get_response=lambda r: HttpResponse())
        
        # Test rate limit logging
        request = self.factory.post('/api/community/posts/')
        request.user = self.user
        request.path = '/api/community/posts/'
        request.method = 'POST'
        request.META = {'REMOTE_ADDR': '***********'}
        
        # Exceed rate limit
        for i in range(11):
            security_middleware.process_request(request)
        
        # Test failed auth logging
        auth_request = self.factory.get('/api/community/posts/')
        auth_request.user = MagicMock()
        auth_request.user.is_authenticated = False
        auth_request.META = {
            'HTTP_AUTHORIZATION': 'Bearer invalid_token',
            'REMOTE_ADDR': '192.168.1.2'
        }
        
        for i in range(12):
            auth_middleware.process_request(auth_request)
        
        # Should have logged both types of events
        self.assertGreater(mock_logger.log_rate_limit_exceeded.call_count, 0)
        self.assertGreater(mock_logger.log_suspicious_activity.call_count, 0)
