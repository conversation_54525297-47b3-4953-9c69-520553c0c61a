import React, { useState } from 'react';
import { AlertCircle, Plus, Search, Hash, Bell, Users, MessageSquare, Heart, Share2, Bookmark, Settings, Zap } from 'lucide-react';

interface MissingFeature {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  priority: 'critical' | 'high' | 'medium' | 'low';
  category: 'core' | 'social' | 'ui' | 'performance';
  implemented: boolean;
}

const MissingFeatures: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const features: MissingFeature[] = [
    // Core Features
    {
      id: 'infinite-scroll',
      name: 'Infinite Scroll',
      description: 'Load more posts automatically as user scrolls down',
      icon: <Zap className="w-5 h-5" />,
      priority: 'critical',
      category: 'core',
      implemented: false
    },
    {
      id: 'real-time-updates',
      name: 'Real-time Updates',
      description: 'WebSocket connection for live post and comment updates',
      icon: <Zap className="w-5 h-5" />,
      priority: 'critical',
      category: 'core',
      implemented: false
    },
    {
      id: 'hashtag-system',
      name: 'Complete Hashtag System',
      description: 'Trending hashtags, hashtag pages, and hashtag-based filtering',
      icon: <Hash className="w-5 h-5" />,
      priority: 'high',
      category: 'core',
      implemented: false
    },
    {
      id: 'notifications',
      name: 'Notifications System',
      description: 'In-app notifications for likes, comments, follows, and mentions',
      icon: <Bell className="w-5 h-5" />,
      priority: 'high',
      category: 'social',
      implemented: false
    },
    {
      id: 'nested-comments',
      name: 'Nested Comments UI',
      description: 'Better UI for threaded comment replies with proper indentation',
      icon: <MessageSquare className="w-5 h-5" />,
      priority: 'high',
      category: 'ui',
      implemented: false
    },
    {
      id: 'post-editing',
      name: 'Post Editing',
      description: 'Allow users to edit their posts after publishing',
      icon: <Settings className="w-5 h-5" />,
      priority: 'high',
      category: 'core',
      implemented: false
    },
    {
      id: 'comment-editing',
      name: 'Comment Editing',
      description: 'Allow users to edit and delete their comments',
      icon: <MessageSquare className="w-5 h-5" />,
      priority: 'high',
      category: 'core',
      implemented: false
    },
    {
      id: 'advanced-search',
      name: 'Advanced Search Filters',
      description: 'Search by date, author, hashtags, content type, and popularity',
      icon: <Search className="w-5 h-5" />,
      priority: 'medium',
      category: 'core',
      implemented: false
    },
    {
      id: 'user-profiles',
      name: 'Enhanced User Profiles',
      description: 'Complete user profile pages with bio, stats, and post history',
      icon: <Users className="w-5 h-5" />,
      priority: 'high',
      category: 'social',
      implemented: false
    },
    {
      id: 'follow-system',
      name: 'Follow/Unfollow System',
      description: 'Complete following system with followers/following lists',
      icon: <Users className="w-5 h-5" />,
      priority: 'medium',
      category: 'social',
      implemented: false
    },
    {
      id: 'post-drafts',
      name: 'Post Drafts',
      description: 'Save posts as drafts and publish later',
      icon: <Plus className="w-5 h-5" />,
      priority: 'medium',
      category: 'core',
      implemented: false
    },
    {
      id: 'rich-text-editor',
      name: 'Rich Text Editor',
      description: 'Enhanced text editor with formatting, mentions, and media',
      icon: <Plus className="w-5 h-5" />,
      priority: 'medium',
      category: 'ui',
      implemented: false
    },
    {
      id: 'image-upload',
      name: 'Image Upload & Gallery',
      description: 'Multiple image uploads with gallery view and image optimization',
      icon: <Plus className="w-5 h-5" />,
      priority: 'medium',
      category: 'core',
      implemented: false
    },
    {
      id: 'post-analytics',
      name: 'Post Analytics',
      description: 'View detailed analytics for post performance and engagement',
      icon: <Settings className="w-5 h-5" />,
      priority: 'low',
      category: 'core',
      implemented: true
    },
    {
      id: 'mobile-gestures',
      name: 'Mobile Touch Gestures',
      description: 'Swipe gestures for like, share, and navigation on mobile',
      icon: <Heart className="w-5 h-5" />,
      priority: 'medium',
      category: 'ui',
      implemented: false
    },
    {
      id: 'content-moderation',
      name: 'Content Moderation Tools',
      description: 'Advanced moderation tools for admins and community managers',
      icon: <Settings className="w-5 h-5" />,
      priority: 'medium',
      category: 'core',
      implemented: true
    }
  ];

  const categories = [
    { id: 'all', name: 'All Features', count: features.length },
    { id: 'core', name: 'Core Features', count: features.filter(f => f.category === 'core').length },
    { id: 'social', name: 'Social Features', count: features.filter(f => f.category === 'social').length },
    { id: 'ui', name: 'UI/UX Features', count: features.filter(f => f.category === 'ui').length },
    { id: 'performance', name: 'Performance', count: features.filter(f => f.category === 'performance').length }
  ];

  const filteredFeatures = selectedCategory === 'all' 
    ? features 
    : features.filter(f => f.category === selectedCategory);

  const getPriorityColor = (priority: MissingFeature['priority']) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
    }
  };

  const getStatusColor = (implemented: boolean) => {
    return implemented 
      ? 'bg-green-100 text-green-800 border-green-200'
      : 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const criticalCount = features.filter(f => f.priority === 'critical' && !f.implemented).length;
  const highCount = features.filter(f => f.priority === 'high' && !f.implemented).length;
  const implementedCount = features.filter(f => f.implemented).length;
  const totalCount = features.length;

  return (
    <div className="max-w-6xl mx-auto p-6 bg-white/5 backdrop-blur-sm border border-white/20 rounded-lg shadow-lg">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">Community Features Status</h2>
        <p className="text-white/70">
          Track implementation progress and identify missing features in the community platform.
        </p>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-red-500/10 p-4 rounded-lg border border-red-500/20 backdrop-blur-sm">
          <div className="text-2xl font-bold text-red-400">{criticalCount}</div>
          <div className="text-red-300 text-sm">Critical Missing</div>
        </div>
        <div className="bg-orange-500/10 p-4 rounded-lg border border-orange-500/20 backdrop-blur-sm">
          <div className="text-2xl font-bold text-orange-400">{highCount}</div>
          <div className="text-orange-300 text-sm">High Priority</div>
        </div>
        <div className="bg-green-500/10 p-4 rounded-lg border border-green-500/20 backdrop-blur-sm">
          <div className="text-2xl font-bold text-green-400">{implementedCount}</div>
          <div className="text-green-300 text-sm">Implemented</div>
        </div>
        <div className="bg-blue-500/10 p-4 rounded-lg border border-blue-500/20 backdrop-blur-sm">
          <div className="text-2xl font-bold text-blue-400">{Math.round((implementedCount/totalCount) * 100)}%</div>
          <div className="text-blue-300 text-sm">Complete</div>
        </div>
      </div>

      {/* Category Filter */}
      <div className="flex flex-wrap gap-2 mb-6">
        {categories.map(category => (
          <button
            key={category.id}
            onClick={() => setSelectedCategory(category.id)}
            className={`px-4 py-2 rounded-lg border transition-colors ${
              selectedCategory === category.id
                ? 'bg-blue-600 text-white border-blue-600'
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            }`}
          >
            {category.name} ({category.count})
          </button>
        ))}
      </div>

      {/* Features List */}
      <div className="grid gap-4">
        {filteredFeatures.map(feature => (
          <div
            key={feature.id}
            className="p-4 border rounded-lg hover:shadow-md transition-shadow"
          >
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 p-2 bg-gray-100 rounded-lg">
                {feature.icon}
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="font-semibold text-gray-900">{feature.name}</h3>
                  <span className={`px-2 py-1 text-xs rounded-full border ${getPriorityColor(feature.priority)}`}>
                    {feature.priority}
                  </span>
                  <span className={`px-2 py-1 text-xs rounded-full border ${getStatusColor(feature.implemented)}`}>
                    {feature.implemented ? 'Implemented' : 'Missing'}
                  </span>
                </div>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {criticalCount > 0 && (
        <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <AlertCircle className="w-5 h-5 text-red-600" />
            <h3 className="font-semibold text-red-800">Critical Issues</h3>
          </div>
          <p className="text-red-700 text-sm">
            There are {criticalCount} critical features missing that significantly impact user experience. 
            These should be prioritized for immediate implementation.
          </p>
        </div>
      )}
    </div>
  );
};

export default MissingFeatures;
