/**
 * Community-specific Error Boundary
 * Provides graceful error handling with recovery options for community features
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { TFunction } from 'react-i18next';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  t?: TFunction;
  isRTL?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
  errorCategory: 'network' | 'chunk' | 'permission' | 'validation' | 'unknown';
  isRecoverable: boolean;
}

class CommunityErrorBoundary extends Component<Props, State> {
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
      errorCategory: 'unknown',
      isRecoverable: true
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    const errorCategory = CommunityErrorBoundary.categorizeError(error);
    const isRecoverable = CommunityErrorBoundary.isErrorRecoverable(error);

    return {
      hasError: true,
      error,
      errorCategory,
      isRecoverable
    };
  }

  private static categorizeError(error: Error): State['errorCategory'] {
    const message = error.message.toLowerCase();

    if (message.includes('chunkloaderror') || message.includes('loading chunk')) {
      return 'chunk';
    }
    if (message.includes('network') || message.includes('fetch')) {
      return 'network';
    }
    if (message.includes('permission') || message.includes('unauthorized')) {
      return 'permission';
    }
    if (message.includes('validation') || message.includes('invalid')) {
      return 'validation';
    }

    return 'unknown';
  }

  private static isErrorRecoverable(error: Error): boolean {
    const message = error.message.toLowerCase();

    // Non-recoverable errors
    if (message.includes('out of memory') || message.includes('maximum call stack')) {
      return false;
    }

    // Most errors are recoverable with retry
    return true;
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log error for debugging
    console.error('Community Error Boundary caught an error:', error, errorInfo);

    // Track error for monitoring
    if (typeof window !== 'undefined' && (window as any).errorTracker) {
      (window as any).errorTracker.trackError(error, {
        component: 'CommunityErrorBoundary',
        errorInfo: errorInfo.componentStack,
        retryCount: this.state.retryCount
      });
    }
  }

  handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1
      }));
    }
  };

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  getErrorMessage(): string {
    const { t } = this.props;
    const { errorCategory } = this.state;

    if (t) {
      // Get localized error message based on category
      switch (errorCategory) {
        case 'chunk':
          return t('community.errors.chunkLoadError', 'Failed to load application resources. Please refresh the page.');
        case 'network':
          return t('community.errors.networkError', 'Network connection error. Please check your internet connection.');
        case 'permission':
          return t('community.errors.permissionError', 'You do not have permission to access this resource.');
        case 'validation':
          return t('community.errors.validationError', 'Invalid data provided. Please check your input.');
        default:
          return t('community.errors.generalError', 'Something went wrong in the community section.');
      }
    }

    // Fallback messages
    const messages = {
      en: {
        chunkLoad: 'Failed to load application resources. Please refresh the page.',
        network: 'Network connection error. Please check your internet connection.',
        general: 'Something went wrong in the community section.'
      },
      ar: {
        chunkLoad: 'فشل في تحميل موارد التطبيق. يرجى تحديث الصفحة.',
        network: 'خطأ في الاتصال بالشبكة. يرجى التحقق من اتصالك بالإنترنت.',
        general: 'حدث خطأ في قسم المجتمع.'
      }
    };

    const { isRTL } = this.props;
    const { error } = this.state;
    const lang = isRTL ? 'ar' : 'en';

    if (error?.message?.includes('ChunkLoadError')) {
      return messages[lang].chunkLoad;
    }
    if (error?.message?.includes('Network')) {
      return messages[lang].network;
    }
    return messages[lang].general;
  }

  getRecoverySuggestions(): string[] {
    const { t } = this.props;
    const { errorCategory } = this.state;

    if (!t) return [];

    switch (errorCategory) {
      case 'chunk':
        return [
          t('community.errors.suggestions.refreshPage', 'Refresh the page'),
          t('community.errors.suggestions.clearCache', 'Clear browser cache'),
          t('community.errors.suggestions.checkConnection', 'Check internet connection')
        ];
      case 'network':
        return [
          t('community.errors.suggestions.checkConnection', 'Check internet connection'),
          t('community.errors.suggestions.retryLater', 'Try again later'),
          t('community.errors.suggestions.contactSupport', 'Contact support if problem persists')
        ];
      case 'permission':
        return [
          t('community.errors.suggestions.login', 'Log in to your account'),
          t('community.errors.suggestions.checkPermissions', 'Verify your account permissions'),
          t('community.errors.suggestions.contactAdmin', 'Contact administrator')
        ];
      case 'validation':
        return [
          t('community.errors.suggestions.checkInput', 'Check your input data'),
          t('community.errors.suggestions.tryAgain', 'Try the action again'),
          t('community.errors.suggestions.refreshForm', 'Refresh the form')
        ];
      default:
        return [
          t('community.errors.suggestions.retry', 'Try again'),
          t('community.errors.suggestions.refreshPage', 'Refresh the page'),
          t('community.errors.suggestions.contactSupport', 'Contact support')
        ];
    }
  }

  getActionButtons() {
    const { t, isRTL } = this.props;
    const { retryCount } = this.state;
    const canRetry = retryCount < this.maxRetries;

    const buttonClass = "px-4 py-2 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-purple-400";
    const primaryButtonClass = `${buttonClass} bg-purple-600 hover:bg-purple-700 text-white`;
    const secondaryButtonClass = `${buttonClass} bg-white/10 hover:bg-white/20 text-white border border-white/20`;

    return (
      <div className={`flex gap-3 ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
        {canRetry && (
          <button
            onClick={this.handleRetry}
            className={primaryButtonClass}
            aria-label={t ? t('community.errors.retry', 'Try Again') : 'Try Again'}
          >
            🔄 {t ? t('community.errors.retry', 'Try Again') : 'Try Again'}
          </button>
        )}
        
        <button
          onClick={this.handleReset}
          className={secondaryButtonClass}
          aria-label={t ? t('community.errors.reset', 'Reset') : 'Reset'}
        >
          🔄 {t ? t('community.errors.reset', 'Reset') : 'Reset'}
        </button>
        
        <button
          onClick={this.handleReload}
          className={secondaryButtonClass}
          aria-label={t ? t('community.errors.reload', 'Reload Page') : 'Reload Page'}
        >
          🔄 {t ? t('community.errors.reload', 'Reload Page') : 'Reload Page'}
        </button>
      </div>
    );
  }

  render() {
    const { hasError } = this.state;
    const { children, fallback, isRTL } = this.props;

    if (hasError) {
      // Use custom fallback if provided
      if (fallback) {
        return fallback;
      }

      // Default error UI
      return (
        <div className={`min-h-[400px] flex items-center justify-center p-8 ${isRTL ? 'text-right' : 'text-left'}`}>
          <div className="max-w-md w-full">
            <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-6 backdrop-blur-sm">
              {/* Error Icon */}
              <div className="text-center mb-4">
                <div className="text-6xl mb-2">⚠️</div>
                <h2 className="text-xl font-semibold text-white mb-2">
                  {this.props.t ? this.props.t('community.errors.title', 'Oops! Something went wrong') : 'Oops! Something went wrong'}
                </h2>
              </div>

              {/* Error Message */}
              <p className="text-white/80 text-center mb-4">
                {this.getErrorMessage()}
              </p>

              {/* Recovery Suggestions */}
              {this.getRecoverySuggestions().length > 0 && (
                <div className="bg-white/5 rounded-lg p-4 mb-6">
                  <h3 className="text-white font-medium mb-2 text-center">
                    {this.props.t ? this.props.t('community.errors.suggestions.title', 'Try these solutions:') : 'Try these solutions:'}
                  </h3>
                  <ul className="text-white/70 text-sm space-y-1">
                    {this.getRecoverySuggestions().map((suggestion, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-purple-400 mt-0.5">•</span>
                        <span>{suggestion}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Retry Information */}
              {this.state.retryCount > 0 && (
                <div className="bg-white/5 rounded-lg p-3 mb-4">
                  <p className="text-white/60 text-sm text-center">
                    {this.props.t 
                      ? this.props.t('community.errors.retryCount', `Retry attempts: ${this.state.retryCount}/${this.maxRetries}`)
                      : `Retry attempts: ${this.state.retryCount}/${this.maxRetries}`
                    }
                  </p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-center">
                {this.getActionButtons()}
              </div>

              {/* Technical Details (Development Only) */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="mt-6">
                  <summary className="text-white/60 text-sm cursor-pointer hover:text-white/80">
                    Technical Details (Development)
                  </summary>
                  <div className="mt-2 p-3 bg-black/20 rounded text-xs text-white/60 font-mono overflow-auto max-h-32">
                    <div className="mb-2">
                      <strong>Error:</strong> {this.state.error.message}
                    </div>
                    {this.state.errorInfo && (
                      <div>
                        <strong>Component Stack:</strong>
                        <pre className="whitespace-pre-wrap text-xs">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </details>
              )}
            </div>
          </div>
        </div>
      );
    }

    return children;
  }
}

export default CommunityErrorBoundary;
