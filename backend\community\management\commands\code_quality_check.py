"""
Management command to check code quality issues in community app
"""

import os
import re
from django.core.management.base import BaseCommand
from django.conf import settings


class Command(BaseCommand):
    help = 'Check code quality issues in community app'

    def add_arguments(self, parser):
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Automatically fix simple issues',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output',
        )

    def handle(self, *args, **options):
        verbose = options['verbose']
        fix_issues = options['fix']
        
        self.stdout.write(self.style.SUCCESS('=== Community Code Quality Check ==='))
        
        # Check for common code quality issues
        issues_found = 0
        
        # 1. Check for debug print statements
        issues_found += self.check_debug_statements(verbose, fix_issues)
        
        # 2. Check for TODO/FIXME comments
        issues_found += self.check_todo_comments(verbose)
        
        # 3. Check for long methods
        issues_found += self.check_long_methods(verbose)
        
        # 4. Check for unused imports
        issues_found += self.check_unused_imports(verbose)
        
        # 5. Check for missing docstrings
        issues_found += self.check_missing_docstrings(verbose)
        
        # Summary
        if issues_found == 0:
            self.stdout.write(
                self.style.SUCCESS('✓ No code quality issues found!')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'Found {issues_found} code quality issue(s)')
            )

    def check_debug_statements(self, verbose, fix_issues):
        """Check for debug print statements"""
        if verbose:
            self.stdout.write('\nChecking for debug statements...')
        
        issues = 0
        community_dir = os.path.join(settings.BASE_DIR, 'community')
        
        for root, dirs, files in os.walk(community_dir):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    for i, line in enumerate(lines, 1):
                        if re.search(r'print\s*\(.*DEBUG', line):
                            issues += 1
                            self.stdout.write(
                                self.style.WARNING(f'  Debug print in {file}:{i}')
                            )
        
        return issues

    def check_todo_comments(self, verbose):
        """Check for TODO/FIXME comments"""
        if verbose:
            self.stdout.write('\nChecking for TODO/FIXME comments...')
        
        issues = 0
        community_dir = os.path.join(settings.BASE_DIR, 'community')
        
        for root, dirs, files in os.walk(community_dir):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    for i, line in enumerate(lines, 1):
                        if re.search(r'(TODO|FIXME|HACK|XXX)', line, re.IGNORECASE):
                            issues += 1
                            self.stdout.write(
                                self.style.WARNING(f'  TODO/FIXME in {file}:{i}: {line.strip()}')
                            )
        
        return issues

    def check_long_methods(self, verbose):
        """Check for methods longer than 50 lines"""
        if verbose:
            self.stdout.write('\nChecking for long methods...')
        
        issues = 0
        community_dir = os.path.join(settings.BASE_DIR, 'community')
        
        for root, dirs, files in os.walk(community_dir):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    current_method = None
                    method_start = 0
                    indent_level = 0
                    
                    for i, line in enumerate(lines, 1):
                        stripped = line.strip()
                        if stripped.startswith('def '):
                            # End previous method if exists
                            if current_method and (i - method_start) > 50:
                                issues += 1
                                self.stdout.write(
                                    self.style.WARNING(
                                        f'  Long method {current_method} in {file}: {i - method_start} lines'
                                    )
                                )
                            
                            # Start new method
                            current_method = stripped.split('(')[0].replace('def ', '')
                            method_start = i
                            indent_level = len(line) - len(line.lstrip())
                        
                        elif current_method and stripped and len(line) - len(line.lstrip()) <= indent_level and not stripped.startswith('#'):
                            # Method ended
                            if (i - method_start) > 50:
                                issues += 1
                                self.stdout.write(
                                    self.style.WARNING(
                                        f'  Long method {current_method} in {file}: {i - method_start} lines'
                                    )
                                )
                            current_method = None
        
        return issues

    def check_unused_imports(self, verbose):
        """Check for potentially unused imports (basic check)"""
        if verbose:
            self.stdout.write('\nChecking for unused imports...')
        
        # This is a basic check - for comprehensive analysis, use tools like flake8
        issues = 0
        self.stdout.write('  Use flake8 or similar tools for comprehensive unused import detection')
        
        return issues

    def check_missing_docstrings(self, verbose):
        """Check for missing docstrings in classes and methods"""
        if verbose:
            self.stdout.write('\nChecking for missing docstrings...')
        
        issues = 0
        community_dir = os.path.join(settings.BASE_DIR, 'community')
        
        for root, dirs, files in os.walk(community_dir):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    for i, line in enumerate(lines):
                        stripped = line.strip()
                        if stripped.startswith('class ') or stripped.startswith('def '):
                            # Check if next non-empty line is a docstring
                            has_docstring = False
                            for j in range(i + 1, min(i + 5, len(lines))):
                                next_line = lines[j].strip()
                                if next_line:
                                    if next_line.startswith('"""') or next_line.startswith("'''"):
                                        has_docstring = True
                                    break
                            
                            if not has_docstring and not stripped.startswith('def __'):
                                issues += 1
                                self.stdout.write(
                                    self.style.WARNING(f'  Missing docstring in {file}:{i+1}: {stripped}')
                                )
        
        return issues
