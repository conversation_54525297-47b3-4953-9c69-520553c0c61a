import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import { type CommunityPost, type CommunityStats } from '../services/communityApi';

// Import focused hooks
import { useCommunityPosts } from './useCommunityPosts';
import { useCommunityStats } from './useCommunityStats';
import { useCommunityModals } from './useCommunityModals';

/**
 * Refactored Community Hook - Orchestrator Pattern
 * 
 * This hook now follows the Single Responsibility Principle by orchestrating
 * multiple focused hooks instead of containing all logic itself.
 * 
 * Focused hooks used:
 * - useCommunityPosts: Posts data and operations
 * - useCommunityStats: Stats and connection status  
 * - useCommunityModals: Modal state management
 */
export interface UseCommunityReturn {
  // Posts data (from useCommunityPosts)
  posts: CommunityPost[];
  searchResults: CommunityPost[];
  displayPosts: CommunityPost[];
  activeView: string;
  searchQuery: string;
  
  // Stats data (from useCommunityStats)
  stats: CommunityStats | null;
  connectionStatus: 'connected' | 'connecting' | 'disconnected';
  
  // Loading states
  isLoading: boolean;
  isSearching: boolean;
  isStatsLoading: boolean;
  
  // Computed stats
  computedStats: {
    trending_posts_count: number;
    verified_authors_count: number;
    saved_posts_count: number;
  };
  
  // Modal states (from useCommunityModals)
  showCreateModal: boolean;
  showAdvancedSearch: boolean;
  showUserDiscovery: boolean;
  showPostAnalytics: string | null;
  
  // Actions
  handleViewChange: (view: string) => void;
  handleSearchChange: (query: string) => void;
  handleClearSearch: () => void;
  refreshPosts: () => Promise<void>;
  refreshStats: () => Promise<void>;
  checkConnection: () => Promise<void>;
  
  // Post operations (from useCommunityPosts)
  handleLikePost: (postId: string) => Promise<void>;
  handleCreatePost: (postData: any) => Promise<void>;
  handleCommentPost: (postId: string, content: string, parentId?: string) => Promise<void>;
  handleSharePost: (postId: string) => Promise<void>;
  handleSavePost: (postId: string) => Promise<void>;
  handleEditPost: (postId: string, updatedPost: any) => Promise<void>;
  handleDeletePost: (postId: string) => Promise<void>;
  handleReportPost: (postId: string) => Promise<void>;

  // Comment operations (from useCommunityPosts)
  handleLikeComment: (commentId: string) => Promise<void>;
  handleEditComment: (commentId: string, content: string) => Promise<void>;
  handleDeleteComment: (commentId: string) => Promise<void>;
  handleReportComment: (commentId: string) => Promise<void>;

  // Modal actions (from useCommunityModals)
  openCreateModal: () => void;
  closeCreateModal: () => void;
  openAdvancedSearch: () => void;
  closeAdvancedSearch: () => void;
  openUserDiscovery: () => void;
  closeUserDiscovery: () => void;
  openPostAnalytics: (postId: string) => void;
  closePostAnalytics: () => void;
  handleCreatePostClick: () => void;
  handleAdvancedSearchResults: (results: any[]) => void;
}

/**
 * Main Community Hook - Orchestrator Pattern
 * 
 * This refactored hook now orchestrates multiple focused hooks instead of
 * containing all logic itself, following the Single Responsibility Principle.
 */
export const useCommunity = (
  onSearchResults?: (results: any[]) => void,
  onSetSearchQuery?: (query: string) => void
): UseCommunityReturn => {
  
  // Redux state
  const activeView = useSelector((state: RootState) => state.forum.activeView);
  const searchQuery = useSelector((state: RootState) => state.forum.searchQuery);
  
  // Use focused hooks for specific responsibilities
  const postsHook = useCommunityPosts();
  const statsHook = useCommunityStats();
  const modalsHook = useCommunityModals(onSearchResults, onSetSearchQuery);

  // Note: Progressive loading is now handled by individual focused hooks
  // Each hook manages its own initialization and loading lifecycle
  // - useCommunityPosts: Loads posts automatically on mount
  // - useCommunityStats: Loads stats automatically on mount
  // - useCommunityModals: Manages modal states

  // This orchestrator hook simply combines their interfaces

  // Computed values - delegate to focused hooks
  const displayPosts = useMemo(() => {
    return searchQuery.trim() ? postsHook.searchResults : postsHook.posts;
  }, [searchQuery, postsHook.searchResults, postsHook.posts]);

  const computedStats = useMemo(() => {
    const postsArray = Array.isArray(postsHook.posts) ? postsHook.posts : [];
    return {
      trending_posts_count: postsArray.filter(p => p.like_count > 10).length,
      verified_authors_count: postsArray.filter(p => p.author?.is_verified).length,
      saved_posts_count: postsArray.filter(p => p.is_saved).length,
    };
  }, [postsHook.posts]);

  // Simple orchestrator - delegate complex logic to focused hooks
  // Search functionality is now handled by useCommunityPosts hook
  
  /**
   * Return orchestrated interface combining all focused hooks
   * This follows the Facade pattern, providing a unified interface
   * while delegating actual work to specialized hooks.
   */
  return {
    // Posts data - delegated to useCommunityPosts
    posts: postsHook.posts,
    searchResults: postsHook.searchResults,
    displayPosts,
    activeView,
    searchQuery,
    
    // Stats data - delegated to useCommunityStats  
    stats: statsHook.stats,
    connectionStatus: statsHook.connectionStatus,
    
    // Loading states
    isLoading: postsHook.isLoading,
    isSearching: postsHook.isSearching,
    isStatsLoading: statsHook.isLoading,
    
    // Computed stats
    computedStats,
    
    // Modal states - delegated to useCommunityModals
    showCreateModal: modalsHook.showCreateModal,
    showAdvancedSearch: modalsHook.showAdvancedSearch,
    showUserDiscovery: modalsHook.showUserDiscovery,
    showPostAnalytics: modalsHook.showPostAnalytics,

    // Actions - delegated to focused hooks
    handleViewChange: postsHook.handleViewChange,
    handleSearchChange: postsHook.handleSearchChange,
    handleClearSearch: postsHook.handleClearSearch,
    refreshPosts: postsHook.refreshPosts,
    refreshStats: statsHook.refreshStats,
    checkConnection: statsHook.checkConnection,
    
    // Post operations - delegated to useCommunityPosts
    handleLikePost: postsHook.handleLikePost,
    handleCreatePost: postsHook.handleCreatePost,
    handleCommentPost: postsHook.handleCommentPost,
    handleSharePost: postsHook.handleSharePost,
    handleSavePost: postsHook.handleSavePost,
    handleEditPost: postsHook.handleEditPost,
    handleDeletePost: postsHook.handleDeletePost,
    handleReportPost: postsHook.handleReportPost,

    // Comment operations - delegated to useCommunityPosts
    handleLikeComment: postsHook.handleLikeComment,
    handleEditComment: postsHook.handleEditComment,
    handleDeleteComment: postsHook.handleDeleteComment,
    handleReportComment: postsHook.handleReportComment,

    // Modal actions - delegated to useCommunityModals
    openCreateModal: modalsHook.openCreateModal,
    closeCreateModal: modalsHook.closeCreateModal,
    openAdvancedSearch: modalsHook.openAdvancedSearch,
    closeAdvancedSearch: modalsHook.closeAdvancedSearch,
    openUserDiscovery: modalsHook.openUserDiscovery,
    closeUserDiscovery: modalsHook.closeUserDiscovery,
    openPostAnalytics: modalsHook.openPostAnalytics,
    closePostAnalytics: modalsHook.closePostAnalytics,
    handleCreatePostClick: modalsHook.handleCreatePostClick,
    handleAdvancedSearchResults: modalsHook.handleAdvancedSearchResults,
  };
};
