"""
Community Models
Social media-style community features for Yasmeen platform
"""

from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.validators import MaxValueValidator
from api.models import Tag


class CommunityPost(models.Model):
    """
    Social media-style community posts
    Different from api.Post which is more blog-style
    """
    VISIBILITY_CHOICES = [
        ('public', 'Public'),
        ('followers', 'Followers Only'),
        ('private', 'Private'),
    ]

    # Basic post info
    title = models.CharField(max_length=200, blank=True)  # Optional for short posts
    content = models.TextField(max_length=2000)  # Shorter than blog posts
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='community_posts')
    
    # Media attachments
    media = models.JSONField(default=list, blank=True, help_text="List of media URLs/paths")
    
    # Social features
    likes = models.ManyToManyField(User, related_name='liked_community_posts', blank=True)
    saves = models.ManyToManyField(User, related_name='saved_community_posts', blank=True, through='PostSave')
    shares_count = models.PositiveIntegerField(default=0)
    comments_count = models.PositiveIntegerField(default=0)
    
    # Categorization
    tags = models.ManyToManyField(Tag, related_name='community_posts', blank=True)
    
    # Settings
    visibility = models.CharField(max_length=20, choices=VISIBILITY_CHOICES, default='public')
    allow_comments = models.BooleanField(default=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['-created_at']),
            models.Index(fields=['author', '-created_at']),
            models.Index(fields=['visibility', '-created_at']),
        ]
    
    def __str__(self):
        return f"{self.author.username}: {self.content[:50]}..."
    
    @property
    def like_count(self):
        return self.likes.count()
    
    @property
    def save_count(self):
        return self.saves.count()

    def save(self, *args, **kwargs):
        """Override save to invalidate cache"""
        super().save(*args, **kwargs)
        self.invalidate_cache()

    def delete(self, *args, **kwargs):
        """Override delete to invalidate cache"""
        self.invalidate_cache()
        super().delete(*args, **kwargs)

    def invalidate_cache(self):
        """Invalidate related cache entries"""
        try:
            from .cache import CommunityCache
            # Invalidate posts cache
            CommunityCache.invalidate_posts_cache()
            # Invalidate user-specific cache
            CommunityCache.invalidate_user_cache(self.author.id)
            # Invalidate stats cache
            CommunityCache.invalidate_stats_cache()
            # Invalidate hashtags cache if tags are involved
            if self.tags.exists():
                CommunityCache.invalidate_hashtags_cache()
        except ImportError:
            # Cache module not available, skip invalidation
            pass


class PostSave(models.Model):
    """
    Track when users save posts (with timestamp)
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    post = models.ForeignKey(CommunityPost, on_delete=models.CASCADE)
    saved_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['user', 'post']
        ordering = ['-saved_at']


class PostShare(models.Model):
    """
    Track post shares
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    post = models.ForeignKey(CommunityPost, on_delete=models.CASCADE)
    shared_at = models.DateTimeField(auto_now_add=True)
    platform = models.CharField(max_length=50, blank=True)  # 'internal', 'twitter', 'linkedin', etc.
    
    class Meta:
        ordering = ['-shared_at']


class CommunityComment(models.Model):
    """
    Facebook-style comments with replies and likes
    """
    post = models.ForeignKey(CommunityPost, on_delete=models.CASCADE, related_name='comments')
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='community_comments')
    content = models.TextField(max_length=500)

    # Reply functionality (Facebook-style nesting)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='replies')

    # Like functionality
    likes = models.ManyToManyField(User, blank=True, related_name='liked_comments')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['created_at']  # Show oldest first for natural conversation flow

    def __str__(self):
        return f"{self.author.username}: {self.content[:50]}..."
    
    @property
    def like_count(self):
        return self.likes.count()

    @property
    def reply_count(self):
        return self.replies.count()

    def is_liked_by(self, user):
        """Check if user has liked this comment"""
        if not user.is_authenticated:
            return False
        return self.likes.filter(id=user.id).exists()

    def save(self, *args, **kwargs):
        """Override save to invalidate cache"""
        super().save(*args, **kwargs)
        self.invalidate_cache()

    def delete(self, *args, **kwargs):
        """Override delete to invalidate cache"""
        self.invalidate_cache()
        super().delete(*args, **kwargs)

    def invalidate_cache(self):
        """Invalidate related cache entries"""
        try:
            from .cache import CommunityCache
            # Invalidate posts cache (comments affect post data)
            CommunityCache.invalidate_posts_cache()
            # Invalidate user-specific cache
            CommunityCache.invalidate_user_cache(self.author.id)
            CommunityCache.invalidate_user_cache(self.post.author.id)
            # Invalidate stats cache
            CommunityCache.invalidate_stats_cache()
        except ImportError:
            # Cache module not available, skip invalidation
            pass


class UserFollow(models.Model):
    """
    User following relationships
    """
    follower = models.ForeignKey(User, on_delete=models.CASCADE, related_name='following')
    following = models.ForeignKey(User, on_delete=models.CASCADE, related_name='followers')
    followed_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['follower', 'following']
        ordering = ['-followed_at']
    
    def __str__(self):
        return f"{self.follower.username} follows {self.following.username}"


class UserActivity(models.Model):
    """
    Track user activity for feed generation
    """
    ACTIVITY_TYPES = [
        ('post_created', 'Post Created'),
        ('post_liked', 'Post Liked'),
        ('post_shared', 'Post Shared'),
        ('post_commented', 'Post Commented'),
        ('user_followed', 'User Followed'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='activities')
    activity_type = models.CharField(max_length=20, choices=ACTIVITY_TYPES)
    
    # Generic foreign key fields for different object types
    content_type = models.CharField(max_length=50)  # 'post', 'comment', 'user'
    object_id = models.PositiveIntegerField()
    
    # Additional data
    data = models.JSONField(default=dict, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['activity_type', '-created_at']),
        ]


class OnlineUser(models.Model):
    """
    Track online users for real-time features
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='online_status')
    last_seen = models.DateTimeField(auto_now=True)
    is_online = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['-last_seen']
    
    def __str__(self):
        return f"{self.user.username} - {'Online' if self.is_online else 'Offline'}"
    
    @classmethod
    def get_online_count(cls):
        """Get count of users online in last 5 minutes"""
        five_minutes_ago = timezone.now() - timezone.timedelta(minutes=5)
        return cls.objects.filter(last_seen__gte=five_minutes_ago, is_online=True).count()


class CommunityStats(models.Model):
    """
    Cache community statistics for performance
    """
    total_posts = models.PositiveIntegerField(default=0)
    total_users = models.PositiveIntegerField(default=0)
    total_likes = models.PositiveIntegerField(default=0)
    total_comments = models.PositiveIntegerField(default=0)
    posts_today = models.PositiveIntegerField(default=0)
    active_users_today = models.PositiveIntegerField(default=0)
    
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = 'Community Statistics'
        verbose_name_plural = 'Community Statistics'
    
    def __str__(self):
        return f"Community Stats - {self.updated_at.date()}"
