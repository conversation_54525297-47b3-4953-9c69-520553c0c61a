import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, XCircle, RefreshCw } from 'lucide-react';
import { communityApi } from '../../services/communityApi';
import { generateCommunityReport, getHighPriorityIssues } from '../../utils/communityAnalysis';

interface DiagnosticTest {
  name: string;
  status: 'pending' | 'success' | 'error' | 'warning';
  message: string;
  details?: string;
}

const CommunityDiagnostics: React.FC = () => {
  const [tests, setTests] = useState<DiagnosticTest[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [showReport, setShowReport] = useState(false);

  const runDiagnostics = async () => {
    setIsRunning(true);
    const diagnosticTests: DiagnosticTest[] = [];

    // Test 1: API Connectivity
    try {
      await communityApi.ping();
      diagnosticTests.push({
        name: 'API Connectivity',
        status: 'success',
        message: 'Backend API is responding'
      });
    } catch (error) {
      diagnosticTests.push({
        name: 'API Connectivity',
        status: 'error',
        message: 'Backend API is not responding',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 2: Posts Loading
    try {
      const posts = await communityApi.getPosts();
      diagnosticTests.push({
        name: 'Posts Loading',
        status: 'success',
        message: `Successfully loaded ${posts.length} posts`
      });
    } catch (error) {
      diagnosticTests.push({
        name: 'Posts Loading',
        status: 'error',
        message: 'Failed to load posts',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 3: Stats Loading
    try {
      const stats = await communityApi.getStats();
      diagnosticTests.push({
        name: 'Community Stats',
        status: 'success',
        message: 'Community statistics loaded successfully'
      });
    } catch (error) {
      diagnosticTests.push({
        name: 'Community Stats',
        status: 'error',
        message: 'Failed to load community statistics',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 4: User Recommendations
    try {
      const recommendations = await communityApi.getUserRecommendations();
      diagnosticTests.push({
        name: 'User Recommendations',
        status: 'success',
        message: `Loaded ${recommendations.length} user recommendations`
      });
    } catch (error) {
      diagnosticTests.push({
        name: 'User Recommendations',
        status: 'warning',
        message: 'User recommendations may not be working properly',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 5: Hashtags
    try {
      const hashtags = await communityApi.getTrendingHashtags();
      if (hashtags.length === 0) {
        diagnosticTests.push({
          name: 'Trending Hashtags',
          status: 'warning',
          message: 'No trending hashtags found - feature may be incomplete'
        });
      } else {
        diagnosticTests.push({
          name: 'Trending Hashtags',
          status: 'success',
          message: `Found ${hashtags.length} trending hashtags`
        });
      }
    } catch (error) {
      diagnosticTests.push({
        name: 'Trending Hashtags',
        status: 'error',
        message: 'Hashtag system not working',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 6: Component Loading
    const missingComponents = [];
    
    // Check if key components exist
    const componentChecks = [
      { name: 'PostCard', exists: true },
      { name: 'PostsFeed', exists: true },
      { name: 'CommunityHeader', exists: true },
      { name: 'CommunitySidebar', exists: true }
    ];

    const missingCount = componentChecks.filter(c => !c.exists).length;
    if (missingCount === 0) {
      diagnosticTests.push({
        name: 'Component Loading',
        status: 'success',
        message: 'All core components are available'
      });
    } else {
      diagnosticTests.push({
        name: 'Component Loading',
        status: 'warning',
        message: `${missingCount} components may have issues`
      });
    }

    // Test 7: Feature Completeness
    const highPriorityIssues = getHighPriorityIssues();
    if (highPriorityIssues.length === 0) {
      diagnosticTests.push({
        name: 'Feature Completeness',
        status: 'success',
        message: 'All high priority features are implemented'
      });
    } else {
      diagnosticTests.push({
        name: 'Feature Completeness',
        status: 'warning',
        message: `${highPriorityIssues.length} high priority features need attention`,
        details: highPriorityIssues.join(', ')
      });
    }

    setTests(diagnosticTests);
    setIsRunning(false);
  };

  useEffect(() => {
    runDiagnostics();
  }, []);

  const getStatusIcon = (status: DiagnosticTest['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      default:
        return <RefreshCw className="w-5 h-5 text-gray-400 animate-spin" />;
    }
  };

  const getStatusColor = (status: DiagnosticTest['status']) => {
    switch (status) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const successCount = tests.filter(t => t.status === 'success').length;
  const errorCount = tests.filter(t => t.status === 'error').length;
  const warningCount = tests.filter(t => t.status === 'warning').length;

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Community Diagnostics</h2>
        <button
          onClick={runDiagnostics}
          disabled={isRunning}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 ${isRunning ? 'animate-spin' : ''}`} />
          {isRunning ? 'Running...' : 'Run Tests'}
        </button>
      </div>

      {/* Summary */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="bg-green-100 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-green-800">{successCount}</div>
          <div className="text-green-600">Passing</div>
        </div>
        <div className="bg-yellow-100 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-yellow-800">{warningCount}</div>
          <div className="text-yellow-600">Warnings</div>
        </div>
        <div className="bg-red-100 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-red-800">{errorCount}</div>
          <div className="text-red-600">Errors</div>
        </div>
      </div>

      {/* Test Results */}
      <div className="space-y-3 mb-6">
        {tests.map((test, index) => (
          <div
            key={index}
            className={`p-4 border rounded-lg ${getStatusColor(test.status)}`}
          >
            <div className="flex items-center gap-3">
              {getStatusIcon(test.status)}
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900">{test.name}</h3>
                <p className="text-gray-700">{test.message}</p>
                {test.details && (
                  <p className="text-sm text-gray-500 mt-1">{test.details}</p>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Detailed Report */}
      <div className="border-t pt-6">
        <button
          onClick={() => setShowReport(!showReport)}
          className="flex items-center gap-2 text-blue-600 hover:text-blue-800 font-medium"
        >
          {showReport ? 'Hide' : 'Show'} Detailed Analysis Report
        </button>
        
        {showReport && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <pre className="whitespace-pre-wrap text-sm text-gray-700 overflow-auto max-h-96">
              {generateCommunityReport()}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
};

export default CommunityDiagnostics;
