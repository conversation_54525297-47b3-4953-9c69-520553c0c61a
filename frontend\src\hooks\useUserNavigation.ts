import { useNavigate } from 'react-router-dom';
import { useAuth } from './useAuth';

/**
 * Hook for navigating to user profiles and handling user interactions
 */
export const useUserNavigation = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  const navigateToProfile = (username: string, userId?: string) => {
    if (!username) return;

    // Navigate to user profile page using the existing route structure
    if (userId) {
      navigate(`/profile/${userId}`);
    } else {
      // Fallback to community posts by author
      navigate(`/community?author=${username}`);
    }
  };

  const navigateToUserPosts = (username: string) => {
    if (!username) return;
    navigate(`/community?author=${username}`);
  };

  const handleUserClick = (username: string, userId?: string) => {
    // Add analytics or logging here if needed
    console.log('User clicked:', username);
    navigateToProfile(username, userId);
  };

  const handleFollowUser = async (userId: string) => {
    if (!isAuthenticated) {
      // Redirect to login or show login modal
      navigate('/login');
      return;
    }

    try {
      // Call your follow API here
      console.log('Following user:', userId);
      // await communityApi.followUser(userId);
    } catch (error) {
      console.error('Failed to follow user:', error);
    }
  };

  return {
    navigateToProfile,
    navigateToUserPosts,
    handleUserClick,
    handleFollowUser,
    isAuthenticated
  };
};

export default useUserNavigation;
