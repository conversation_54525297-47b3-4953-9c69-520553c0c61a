import React from 'react';
import { Heart, MessageCircle, Bookmark, Share2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useCommunityAuth } from './CommunityAuthProvider';
import { InlineCommunityAuthGuard } from './CommunityAuthGuard';
import { CommunityPost } from '../../services/communityApi';

interface CommunityPostActionsProps {
  post: CommunityPost;
  onLike?: (postId: string) => void;
  onComment?: (postId: string) => void;
  onSave?: (postId: string) => void;
  onShare?: (postId: string) => void;
}

/**
 * Community post actions with authentication integration
 * Shows appropriate UI based on authentication state
 */
export const CommunityPostActions: React.FC<CommunityPostActionsProps> = ({
  post,
  onLike,
  onComment,
  onSave,
  onShare
}) => {
  const { t } = useTranslation();
  const { isAuthenticated, canLike, canComment, showAuthPrompt } = useCommunityAuth();

  const handleLike = () => {
    if (!canLike) {
      showAuthPrompt('like');
      return;
    }
    onLike?.(post.id);
  };

  const handleComment = () => {
    if (!canComment) {
      showAuthPrompt('comment');
      return;
    }
    onComment?.(post.id);
  };

  const handleSave = () => {
    if (!isAuthenticated) {
      showAuthPrompt('save');
      return;
    }
    onSave?.(post.id);
  };

  const handleShare = () => {
    // Share doesn't require authentication
    onShare?.(post.id);
  };

  return (
    <div className="flex items-center gap-4 pt-3 border-t border-gray-700/50">
      {/* Like Button */}
      <InlineCommunityAuthGuard
        fallback={
          <button
            onClick={handleLike}
            className="flex items-center gap-2 text-gray-400 hover:text-red-400 transition-colors"
            title={t('community.auth.loginToLike', 'Login to like')}
          >
            <Heart className="w-4 h-4" />
            <span className="text-sm">{post.like_count}</span>
          </button>
        }
      >
        <button
          onClick={handleLike}
          className={`flex items-center gap-2 transition-colors ${
            post.is_liked 
              ? 'text-red-500 hover:text-red-400' 
              : 'text-gray-400 hover:text-red-400'
          }`}
        >
          <Heart className={`w-4 h-4 ${post.is_liked ? 'fill-current' : ''}`} />
          <span className="text-sm">{post.like_count}</span>
        </button>
      </InlineCommunityAuthGuard>

      {/* Comment Button */}
      <InlineCommunityAuthGuard
        fallback={
          <button
            onClick={handleComment}
            className="flex items-center gap-2 text-gray-400 hover:text-blue-400 transition-colors"
            title={t('community.auth.loginToComment', 'Login to comment')}
          >
            <MessageCircle className="w-4 h-4" />
            <span className="text-sm">{post.comments_count}</span>
          </button>
        }
      >
        <button
          onClick={handleComment}
          className="flex items-center gap-2 text-gray-400 hover:text-blue-400 transition-colors"
        >
          <MessageCircle className="w-4 h-4" />
          <span className="text-sm">{post.comments_count}</span>
        </button>
      </InlineCommunityAuthGuard>

      {/* Save Button */}
      <InlineCommunityAuthGuard
        fallback={
          <button
            onClick={handleSave}
            className="flex items-center gap-2 text-gray-400 hover:text-yellow-400 transition-colors"
            title={t('community.auth.loginToSave', 'Login to save')}
          >
            <Bookmark className="w-4 h-4" />
          </button>
        }
      >
        <button
          onClick={handleSave}
          className={`flex items-center gap-2 transition-colors ${
            post.is_saved 
              ? 'text-yellow-500 hover:text-yellow-400' 
              : 'text-gray-400 hover:text-yellow-400'
          }`}
        >
          <Bookmark className={`w-4 h-4 ${post.is_saved ? 'fill-current' : ''}`} />
        </button>
      </InlineCommunityAuthGuard>

      {/* Share Button - No auth required */}
      <button
        onClick={handleShare}
        className="flex items-center gap-2 text-gray-400 hover:text-green-400 transition-colors"
      >
        <Share2 className="w-4 h-4" />
        <span className="text-sm">{post.shares_count}</span>
      </button>
    </div>
  );
};

export default CommunityPostActions;
