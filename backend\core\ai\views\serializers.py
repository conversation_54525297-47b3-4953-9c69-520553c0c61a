"""
🎯 AI VIEWS SERIALIZERS
Consolidated serializers for AI views

This file consolidates serializers from:
- ai_recommendations.serializers (AIRecommendationSerializer, AIRecommendationFeedbackSerializer)
"""

from rest_framework import serializers
from django.contrib.auth.models import User

# Import consolidated models
from core.ai.models import (
    AIRecommendation, 
    AIRecommendationFeedback,
    AIConfiguration,
    AIUsageLog,
    AIServiceStatus,
    AISession,
    AIInteraction
)


class UserSerializer(serializers.ModelSerializer):
    """Serializer for user details in AI contexts"""
    
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name']


class AIRecommendationSerializer(serializers.ModelSerializer):
    """
    Serializer for AI recommendations
    Consolidated from ai_recommendations.serializers.AIRecommendationSerializer
    """
    
    business_idea_title = serializers.CharField(source='business_idea.title', read_only=True)
    recommendation_type_display = serializers.Char<PERSON>ield(source='get_recommendation_type_display', read_only=True)
    priority_display = serializers.Char<PERSON><PERSON>(source='get_priority_display', read_only=True)
    implemented_by_details = UserSerializer(source='implemented_by', read_only=True)
    feedback_count = serializers.SerializerMethodField()
    helpful_count = serializers.SerializerMethodField()
    
    class Meta:
        model = AIRecommendation
        fields = [
            'id', 'business_idea', 'business_idea_title', 'recommendation_type', 
            'recommendation_type_display', 'title', 'description', 'reasoning', 
            'priority', 'priority_display', 'action_items', 'expected_outcome', 
            'relevance_score', 'is_implemented', 'implementation_notes', 
            'implemented_by', 'implemented_by_details', 'implemented_at', 
            'feedback_count', 'helpful_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['implemented_by', 'implemented_at', 'created_at', 'updated_at']
    
    def get_feedback_count(self, obj):
        """Get total feedback count"""
        return obj.feedback.count()
    
    def get_helpful_count(self, obj):
        """Get helpful feedback count"""
        return obj.feedback.filter(feedback_type='helpful').count()


class AIRecommendationFeedbackSerializer(serializers.ModelSerializer):
    """
    Serializer for AI recommendation feedback
    Consolidated from ai_recommendations.serializers.AIRecommendationFeedbackSerializer
    """
    
    user_details = UserSerializer(source='user', read_only=True)
    feedback_type_display = serializers.CharField(source='get_feedback_type_display', read_only=True)
    recommendation_title = serializers.CharField(source='recommendation.title', read_only=True)
    
    class Meta:
        model = AIRecommendationFeedback
        fields = [
            'id', 'recommendation', 'recommendation_title', 'user', 'user_details', 
            'feedback_type', 'feedback_type_display', 'comments', 'created_at'
        ]
        read_only_fields = ['user', 'created_at']
    
    def create(self, validated_data):
        """Create feedback with current user"""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class AIConfigurationSerializer(serializers.ModelSerializer):
    """Serializer for AI configuration management"""
    
    created_by_details = UserSerializer(source='created_by', read_only=True)
    updated_by_details = UserSerializer(source='updated_by', read_only=True)
    display_value = serializers.SerializerMethodField()
    
    class Meta:
        model = AIConfiguration
        fields = [
            'id', 'provider', 'config_type', 'key', 'value', 'display_value',
            'is_active', 'is_sensitive', 'description', 'created_at', 'updated_at',
            'created_by', 'created_by_details', 'updated_by', 'updated_by_details'
        ]
        read_only_fields = ['created_by', 'updated_by', 'created_at', 'updated_at']
        extra_kwargs = {
            'value': {'write_only': True}  # Hide actual value in responses
        }
    
    def get_display_value(self, obj):
        """Get masked value for display"""
        return obj.get_display_value()


class AIUsageLogSerializer(serializers.ModelSerializer):
    """Serializer for AI usage logs"""
    
    user_details = UserSerializer(source='user', read_only=True)
    
    class Meta:
        model = AIUsageLog
        fields = [
            'id', 'user', 'user_details', 'service_type', 'endpoint', 
            'request_data', 'response_data', 'success', 'error_message',
            'response_time', 'tokens_used', 'cost', 'created_at'
        ]
        read_only_fields = ['created_at']


class AIServiceStatusSerializer(serializers.ModelSerializer):
    """Serializer for AI service status"""
    
    class Meta:
        model = AIServiceStatus
        fields = [
            'id', 'provider', 'service_name', 'status', 'response_time',
            'success_rate', 'error_count', 'last_check', 'is_configured',
            'configuration_details', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class AISessionSerializer(serializers.ModelSerializer):
    """Serializer for AI chat sessions"""
    
    user_details = UserSerializer(source='user', read_only=True)
    interaction_count = serializers.SerializerMethodField()
    
    class Meta:
        model = AISession
        fields = [
            'id', 'user', 'user_details', 'session_type', 'title', 
            'context_data', 'is_active', 'interaction_count',
            'created_at', 'updated_at', 'ended_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_interaction_count(self, obj):
        """Get total interaction count for this session"""
        return obj.interactions.count()


class AIInteractionSerializer(serializers.ModelSerializer):
    """Serializer for AI interactions within sessions"""
    
    session_title = serializers.CharField(source='session.title', read_only=True)
    
    class Meta:
        model = AIInteraction
        fields = [
            'id', 'session', 'session_title', 'interaction_type', 
            'user_input', 'ai_response', 'metadata', 'response_time',
            'tokens_used', 'created_at'
        ]
        read_only_fields = ['created_at']


# ========================================
# CHAT AND ANALYSIS REQUEST SERIALIZERS
# ========================================

class ChatRequestSerializer(serializers.Serializer):
    """Serializer for chat requests"""
    
    message = serializers.CharField(max_length=10000)
    language = serializers.CharField(max_length=10, default='en')
    context = serializers.JSONField(default=dict)
    chat_type = serializers.ChoiceField(
        choices=[
            ('general', 'General Chat'),
            ('recommendation', 'Business Recommendations'),
            ('mentorship', 'Mentorship Guidance'),
            ('idea_builder', 'Idea Enhancement')
        ],
        default='general'
    )


class BusinessAnalysisRequestSerializer(serializers.Serializer):
    """Serializer for business analysis requests"""
    
    business_data = serializers.JSONField()
    analysis_type = serializers.ChoiceField(
        choices=[
            ('comprehensive', 'Comprehensive Analysis'),
            ('recommendations', 'Generate Recommendations'),
            ('idea_enhancement', 'Idea Enhancement'),
            ('market_analysis', 'Market Analysis'),
            ('financial_projection', 'Financial Projections')
        ],
        default='comprehensive'
    )


class PredictiveAnalyticsRequestSerializer(serializers.Serializer):
    """Serializer for predictive analytics requests"""
    
    analysis_type = serializers.ChoiceField(
        choices=[
            ('success_prediction', 'Business Success Prediction'),
            ('market_forecast', 'Market Trend Forecast'),
            ('risk_assessment', 'Risk Assessment'),
            ('investment_readiness', 'Investment Readiness Score')
        ]
    )
    business_data = serializers.JSONField()
    industry = serializers.CharField(max_length=100, required=False)
    timeframe_days = serializers.IntegerField(min_value=1, max_value=365, required=False)


class ComputerVisionRequestSerializer(serializers.Serializer):
    """Serializer for computer vision requests"""
    
    action = serializers.ChoiceField(
        choices=[
            ('analyze_image', 'Analyze Image'),
            ('extract_text', 'Extract Text from Image'),
            ('detect_objects', 'Object Detection'),
            ('analyze_document', 'Document Analysis')
        ]
    )
    analysis_type = serializers.CharField(max_length=50, required=False, default='general')


class VoiceAIRequestSerializer(serializers.Serializer):
    """Serializer for voice AI requests"""
    
    action = serializers.ChoiceField(
        choices=[
            ('transcribe', 'Transcribe Audio'),
            ('synthesize', 'Synthesize Speech'),
            ('analyze_sentiment', 'Analyze Voice Sentiment'),
            ('process_command', 'Process Voice Command')
        ]
    )
    text = serializers.CharField(max_length=5000, required=False)
    language = serializers.CharField(max_length=10, default='en')
    voice = serializers.CharField(max_length=50, default='default')


class RecommendationGenerationRequestSerializer(serializers.Serializer):
    """Serializer for generating new recommendations"""
    
    business_idea_id = serializers.IntegerField()
    types = serializers.ListField(
        child=serializers.ChoiceField(choices=AIRecommendation.RECOMMENDATION_TYPE_CHOICES),
        required=False,
        default=list
    )
