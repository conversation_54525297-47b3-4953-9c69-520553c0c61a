import React, { useState, useRef, useCallback } from 'react';
import { Upload, X, Image as ImageIcon, Camera, FileImage, AlertCircle, Check } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';

interface UploadedImage {
  id: string;
  file: File;
  url: string;
  preview: string;
  uploading: boolean;
  uploaded: boolean;
  error?: string;
}

interface EnhancedImageUploadProps {
  images: UploadedImage[];
  onImagesChange: (images: UploadedImage[]) => void;
  maxImages?: number;
  maxFileSize?: number; // in MB
  acceptedTypes?: string[];
  className?: string;
  disabled?: boolean;
}

const EnhancedImageUpload: React.FC<EnhancedImageUploadProps> = ({
  images,
  onImagesChange,
  maxImages = 4,
  maxFileSize = 10,
  acceptedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  className = '',
  disabled = false
}) => {
  const { isRTL } = useLanguage();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragOver, setDragOver] = useState(false);

  // Generate unique ID
  const generateId = () => Math.random().toString(36).substr(2, 9);

  // Validate file
  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return `File type ${file.type} is not supported. Please use: ${acceptedTypes.join(', ')}`;
    }
    
    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size must be less than ${maxFileSize}MB`;
    }
    
    return null;
  };

  // Create image preview
  const createImagePreview = (file: File): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.readAsDataURL(file);
    });
  };

  // Handle file selection
  const handleFileSelect = useCallback(async (files: FileList) => {
    if (disabled) return;

    const newImages: UploadedImage[] = [];
    const remainingSlots = maxImages - images.length;
    const filesToProcess = Array.from(files).slice(0, remainingSlots);

    for (const file of filesToProcess) {
      const error = validateFile(file);
      const preview = await createImagePreview(file);
      
      const imageData: UploadedImage = {
        id: generateId(),
        file,
        url: '', // Will be set after upload
        preview,
        uploading: false,
        uploaded: false,
        error
      };
      
      newImages.push(imageData);
    }

    onImagesChange([...images, ...newImages]);
  }, [images, maxImages, disabled, onImagesChange]);

  // Handle drag and drop
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    if (disabled) return;
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [disabled, handleFileSelect]);

  // Handle file input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files);
    }
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  };

  // Remove image
  const removeImage = (id: string) => {
    onImagesChange(images.filter(img => img.id !== id));
  };

  // Simulate upload (replace with actual upload logic)
  const uploadImage = async (imageId: string) => {
    const imageIndex = images.findIndex(img => img.id === imageId);
    if (imageIndex === -1) return;

    // Update uploading state
    const updatedImages = [...images];
    updatedImages[imageIndex] = { ...updatedImages[imageIndex], uploading: true };
    onImagesChange(updatedImages);

    try {
      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate successful upload
      updatedImages[imageIndex] = {
        ...updatedImages[imageIndex],
        uploading: false,
        uploaded: true,
        url: `https://example.com/uploads/${imageId}.jpg` // Replace with actual URL
      };
      onImagesChange(updatedImages);
    } catch (error) {
      updatedImages[imageIndex] = {
        ...updatedImages[imageIndex],
        uploading: false,
        error: 'Upload failed. Please try again.'
      };
      onImagesChange(updatedImages);
    }
  };

  const canAddMore = images.length < maxImages && !disabled;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      {canAddMore && (
        <div
          className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            dragOver
              ? 'border-blue-400 bg-blue-50'
              : 'border-gray-300 hover:border-gray-400'
          } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => !disabled && fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept={acceptedTypes.join(',')}
            onChange={handleInputChange}
            className="hidden"
            disabled={disabled}
          />
          
          <div className="flex flex-col items-center gap-3">
            <div className="p-3 bg-gray-100 rounded-full">
              <Upload className="w-6 h-6 text-gray-600" />
            </div>
            <div>
              <p className="text-lg font-medium text-gray-900">
                Drop images here or click to upload
              </p>
              <p className="text-sm text-gray-500">
                Up to {maxImages} images, max {maxFileSize}MB each
              </p>
              <p className="text-xs text-gray-400 mt-1">
                Supports: {acceptedTypes.map(type => type.split('/')[1]).join(', ')}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Image Previews */}
      {images.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {images.map((image) => (
            <div key={image.id} className="relative group">
              <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                <img
                  src={image.preview}
                  alt="Upload preview"
                  className="w-full h-full object-cover"
                />
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity flex items-center justify-center">
                  <button
                    onClick={() => removeImage(image.id)}
                    className="opacity-0 group-hover:opacity-100 p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-all"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Status Indicators */}
              <div className="absolute top-2 right-2 flex gap-1">
                {image.uploading && (
                  <div className="p-1 bg-blue-500 text-white rounded-full">
                    <Upload className="w-3 h-3 animate-pulse" />
                  </div>
                )}
                {image.uploaded && (
                  <div className="p-1 bg-green-500 text-white rounded-full">
                    <Check className="w-3 h-3" />
                  </div>
                )}
                {image.error && (
                  <div className="p-1 bg-red-500 text-white rounded-full">
                    <AlertCircle className="w-3 h-3" />
                  </div>
                )}
              </div>

              {/* Error Message */}
              {image.error && (
                <div className="absolute bottom-0 left-0 right-0 bg-red-500 text-white text-xs p-2 rounded-b-lg">
                  {image.error}
                </div>
              )}

              {/* Upload Button */}
              {!image.uploaded && !image.uploading && !image.error && (
                <button
                  onClick={() => uploadImage(image.id)}
                  className="absolute bottom-2 left-2 right-2 bg-blue-500 text-white text-xs py-1 px-2 rounded hover:bg-blue-600 transition-colors"
                >
                  Upload
                </button>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Summary */}
      {images.length > 0 && (
        <div className="text-sm text-gray-500 text-center">
          {images.length} of {maxImages} images selected
          {images.filter(img => img.uploaded).length > 0 && (
            <span className="text-green-600 ml-2">
              • {images.filter(img => img.uploaded).length} uploaded
            </span>
          )}
          {images.filter(img => img.error).length > 0 && (
            <span className="text-red-600 ml-2">
              • {images.filter(img => img.error).length} failed
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default EnhancedImageUpload;
