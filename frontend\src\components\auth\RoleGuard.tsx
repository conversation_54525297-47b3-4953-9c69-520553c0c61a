/**
 * 🎯 ROLE-BASED ACCESS GUARD
 * Unified component for protecting routes and features based on user capabilities
 */

import React from 'react';
import { Navigate } from 'react-router-dom';
import { useUnifiedRoles } from '../../hooks/useUnifiedRoles';
import { RoleCapabilities } from '../../config/roleConfig';
import AccessDeniedPage from '../../pages/AccessDeniedPage';

interface RoleGuardProps {
  children: React.ReactNode;
  requiredCapability?: keyof RoleCapabilities;
  requiredCapabilities?: (keyof RoleCapabilities)[];
  requireAll?: boolean; // If true, user must have ALL capabilities; if false, ANY capability
  allowedRoles?: string[];
  fallbackComponent?: React.ComponentType;
  redirectTo?: string;
  showAccessDenied?: boolean;
}

export const RoleGuard: React.FC<RoleGuardProps> = ({
  children,
  requiredCapability,
  requiredCapabilities = [],
  requireAll = false,
  allowedRoles = [],
  fallbackComponent: FallbackComponent,
  redirectTo,
  showAccessDenied = true
}) => {
  const { 
    primaryRole, 
    isAuthenticated, 
    canAccess, 
    getHomeRoute 
  } = useUnifiedRoles();

  // Check authentication first
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Check role-based access
  let hasAccess = true;

  // Check specific capability
  if (requiredCapability) {
    hasAccess = canAccess(requiredCapability);
  }

  // Check multiple capabilities
  if (requiredCapabilities.length > 0) {
    if (requireAll) {
      // User must have ALL capabilities
      hasAccess = requiredCapabilities.every(capability => canAccess(capability));
    } else {
      // User must have ANY capability
      hasAccess = requiredCapabilities.some(capability => canAccess(capability));
    }
  }

  // Check allowed roles
  if (allowedRoles.length > 0) {
    hasAccess = hasAccess && allowedRoles.includes(primaryRole);
  }

  // Handle access denied
  if (!hasAccess) {
    // Custom fallback component
    if (FallbackComponent) {
      return <FallbackComponent />;
    }

    // Redirect to specific route
    if (redirectTo) {
      return <Navigate to={redirectTo} replace />;
    }

    // Show access denied page
    if (showAccessDenied) {
      return <AccessDeniedPage />;
    }

    // Redirect to user's home route
    return <Navigate to={getHomeRoute()} replace />;
  }

  // Render children if access is granted
  return <>{children}</>;
};

// Convenience components for common use cases
export const DashboardGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <RoleGuard requiredCapability="canAccessDashboard">
    {children}
  </RoleGuard>
);

export const AdminGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <RoleGuard requiredCapability="canManageUsers">
    {children}
  </RoleGuard>
);

export const ModeratorGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <RoleGuard requiredCapability="canModerateContent">
    {children}
  </RoleGuard>
);

export const AIGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <RoleGuard requiredCapability="canAccessAI">
    {children}
  </RoleGuard>
);

export const BusinessPlanGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <RoleGuard requiredCapability="canCreateBusinessPlans">
    {children}
  </RoleGuard>
);

export const AnalyticsGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <RoleGuard requiredCapability="canAccessAnalytics">
    {children}
  </RoleGuard>
);

export const SystemSettingsGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <RoleGuard requiredCapability="canAccessSystemSettings">
    {children}
  </RoleGuard>
);

// Multi-capability guards
export const EntrepreneurGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <RoleGuard 
    requiredCapabilities={['canAccessDashboard', 'canCreateBusinessPlans']}
    requireAll={true}
  >
    {children}
  </RoleGuard>
);

export const MentorGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <RoleGuard 
    requiredCapabilities={['canAccessDashboard', 'canMentor']}
    requireAll={true}
  >
    {children}
  </RoleGuard>
);

export const InvestorGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <RoleGuard 
    requiredCapabilities={['canAccessDashboard', 'canInvest']}
    requireAll={true}
  >
    {children}
  </RoleGuard>
);

// Role-specific guards
export const SuperAdminGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <RoleGuard allowedRoles={['super_admin']}>
    {children}
  </RoleGuard>
);

export const AdminOrSuperAdminGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <RoleGuard allowedRoles={['admin', 'super_admin']}>
    {children}
  </RoleGuard>
);

export const BusinessRolesGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <RoleGuard allowedRoles={['entrepreneur', 'mentor', 'investor']}>
    {children}
  </RoleGuard>
);

// Hook for conditional rendering
export function useRoleGuard(
  requiredCapability?: keyof RoleCapabilities,
  requiredCapabilities?: (keyof RoleCapabilities)[],
  requireAll: boolean = false,
  allowedRoles?: string[]
): boolean {
  const { primaryRole, canAccess } = useUnifiedRoles();

  // Check specific capability
  if (requiredCapability && !canAccess(requiredCapability)) {
    return false;
  }

  // Check multiple capabilities
  if (requiredCapabilities && requiredCapabilities.length > 0) {
    if (requireAll) {
      if (!requiredCapabilities.every(capability => canAccess(capability))) {
        return false;
      }
    } else {
      if (!requiredCapabilities.some(capability => canAccess(capability))) {
        return false;
      }
    }
  }

  // Check allowed roles
  if (allowedRoles && allowedRoles.length > 0) {
    if (!allowedRoles.includes(primaryRole)) {
      return false;
    }
  }

  return true;
}

export default RoleGuard;
