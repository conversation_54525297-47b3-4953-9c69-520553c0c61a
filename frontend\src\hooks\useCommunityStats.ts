import { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { communityApi, type CommunityStats } from '../services/communityApi';
import { useToast } from './useToast';
import { useAuth } from './useAuth';

export interface UseCommunityStatsReturn {
  // Data
  stats: CommunityStats | null;
  connectionStatus: 'connected' | 'connecting' | 'disconnected';
  
  // Loading states
  isLoading: boolean;
  
  // Actions
  refreshStats: () => Promise<void>;
  
  // Connection management
  checkConnection: () => Promise<void>;
}

export const useCommunityStats = (): UseCommunityStatsReturn => {
  const { t } = useTranslation();
  const { showError } = useToast();
  const { isAuthenticated, user } = useAuth();

  // Local state
  const [stats, setStats] = useState<CommunityStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('connecting');

  // Load stats
  const loadStats = useCallback(async () => {
    setIsLoading(true);
    setConnectionStatus('connecting');
    
    try {
      const statsData = await communityApi.getStats();
      setStats(statsData);
      setConnectionStatus('connected');
    } catch (error) {
      console.error('Failed to load stats:', error);
      setConnectionStatus('disconnected');
      showError(t('community.messages.statsLoadError'));
    } finally {
      setIsLoading(false);
    }
  }, [showError, t]);

  // Refresh stats
  const refreshStats = useCallback(async () => {
    try {
      setConnectionStatus('connecting');
      const statsData = await communityApi.getStats();
      setStats(statsData);
      setConnectionStatus('connected');
    } catch (error) {
      console.error('Failed to refresh stats:', error);
      setConnectionStatus('disconnected');
      showError(t('community.messages.statsRefreshError'));
    }
  }, [showError, t]);

  // Check connection
  const checkConnection = useCallback(async () => {
    try {
      setConnectionStatus('connecting');
      await communityApi.ping();
      setConnectionStatus('connected');
    } catch (error) {
      console.error('Connection check failed:', error);
      setConnectionStatus('disconnected');
    }
  }, []);

  // Initial load
  useEffect(() => {
    loadStats();
  }, []); // Empty dependency array - only run once on mount

  // Auto-refresh stats every 5 minutes (reduced from 30 seconds to prevent excessive calls)
  useEffect(() => {
    const interval = setInterval(() => {
      if (connectionStatus === 'connected') {
        refreshStats();
      }
    }, 300000); // 5 minutes instead of 30 seconds

    return () => clearInterval(interval);
  }, [connectionStatus, refreshStats]); // Include dependencies but use useCallback to prevent restarts

  // Connection health check every 30 seconds (reduced from 10 seconds)
  useEffect(() => {
    const interval = setInterval(() => {
      if (connectionStatus === 'disconnected') {
        checkConnection();
      }
    }, 30000); // 30 seconds instead of 10 seconds

    return () => clearInterval(interval);
  }, [connectionStatus, checkConnection]); // Include dependencies but use useCallback to prevent restarts

  return {
    // Data
    stats,
    connectionStatus,
    
    // Loading states
    isLoading,
    
    // Actions
    refreshStats,
    
    // Connection management
    checkConnection,
  };
};
