/**
 * Secure Token Management
 * 
 * Provides secure storage and management of authentication tokens
 * with encryption, expiration handling, and secure transmission.
 */

// Simple encryption/decryption using Web Crypto API
class TokenEncryption {
  private key: CryptoKey | null = null;

  async initialize(): Promise<void> {
    if (this.key) return;

    try {
      // Generate or retrieve encryption key
      const keyData = await this.getOrCreateKey();
      this.key = await crypto.subtle.importKey(
        'raw',
        keyData,
        { name: 'AES-GCM' },
        false,
        ['encrypt', 'decrypt']
      );
    } catch (error) {
      console.warn('Failed to initialize token encryption:', error);
    }
  }

  private async getOrCreateKey(): Promise<ArrayBuffer> {
    const stored = localStorage.getItem('token_key');
    if (stored) {
      return Uint8Array.from(atob(stored), c => c.charCodeAt(0));
    }

    // Generate new key
    const key = crypto.getRandomValues(new Uint8Array(32));
    localStorage.setItem('token_key', btoa(String.fromCharCode(...key)));
    return key;
  }

  async encrypt(data: string): Promise<string> {
    if (!this.key) {
      await this.initialize();
    }

    if (!this.key) {
      throw new Error('Encryption key not available');
    }

    try {
      const iv = crypto.getRandomValues(new Uint8Array(12));
      const encodedData = new TextEncoder().encode(data);
      
      const encrypted = await crypto.subtle.encrypt(
        { name: 'AES-GCM', iv },
        this.key,
        encodedData
      );

      // Combine IV and encrypted data
      const combined = new Uint8Array(iv.length + encrypted.byteLength);
      combined.set(iv);
      combined.set(new Uint8Array(encrypted), iv.length);

      return btoa(String.fromCharCode(...combined));
    } catch (error) {
      console.warn('Token encryption failed:', error);
      return data; // Fallback to unencrypted
    }
  }

  async decrypt(encryptedData: string): Promise<string> {
    if (!this.key) {
      await this.initialize();
    }

    if (!this.key) {
      throw new Error('Decryption key not available');
    }

    try {
      const combined = Uint8Array.from(atob(encryptedData), c => c.charCodeAt(0));
      const iv = combined.slice(0, 12);
      const encrypted = combined.slice(12);

      const decrypted = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv },
        this.key,
        encrypted
      );

      return new TextDecoder().decode(decrypted);
    } catch (error) {
      console.warn('Token decryption failed:', error);
      return encryptedData; // Fallback to treating as unencrypted
    }
  }
}

interface TokenData {
  token: string;
  refreshToken?: string;
  expiresAt: number;
  issuedAt: number;
  userId: string;
}

interface SecureStorageOptions {
  encrypt: boolean;
  useSessionStorage: boolean;
  keyPrefix: string;
}

export class SecureTokenManager {
  private encryption: TokenEncryption;
  private options: SecureStorageOptions;

  constructor(options: Partial<SecureStorageOptions> = {}) {
    this.encryption = new TokenEncryption();
    this.options = {
      encrypt: true,
      useSessionStorage: false,
      keyPrefix: 'secure_token',
      ...options
    };
  }

  private getStorage(): Storage {
    return this.options.useSessionStorage ? sessionStorage : localStorage;
  }

  private getKey(suffix: string): string {
    return `${this.options.keyPrefix}_${suffix}`;
  }

  /**
   * Store authentication token securely
   */
  async storeToken(tokenData: TokenData): Promise<void> {
    try {
      const serialized = JSON.stringify(tokenData);
      const data = this.options.encrypt 
        ? await this.encryption.encrypt(serialized)
        : serialized;

      this.getStorage().setItem(this.getKey('auth'), data);
      
      // Store expiration separately for quick access
      this.getStorage().setItem(
        this.getKey('expires'), 
        tokenData.expiresAt.toString()
      );

      console.debug('Token stored securely');
    } catch (error) {
      console.error('Failed to store token:', error);
      throw new Error('Token storage failed');
    }
  }

  /**
   * Retrieve authentication token
   */
  async getToken(): Promise<TokenData | null> {
    try {
      const data = this.getStorage().getItem(this.getKey('auth'));
      if (!data) return null;

      const decrypted = this.options.encrypt 
        ? await this.encryption.decrypt(data)
        : data;

      const tokenData: TokenData = JSON.parse(decrypted);

      // Check if token is expired
      if (Date.now() >= tokenData.expiresAt) {
        await this.clearToken();
        return null;
      }

      return tokenData;
    } catch (error) {
      console.error('Failed to retrieve token:', error);
      await this.clearToken(); // Clear corrupted token
      return null;
    }
  }

  /**
   * Check if token exists and is valid
   */
  async isTokenValid(): Promise<boolean> {
    const token = await this.getToken();
    return token !== null;
  }

  /**
   * Get token expiration time
   */
  getTokenExpiration(): number | null {
    const expires = this.getStorage().getItem(this.getKey('expires'));
    return expires ? parseInt(expires, 10) : null;
  }

  /**
   * Check if token is about to expire (within 5 minutes)
   */
  isTokenExpiringSoon(): boolean {
    const expires = this.getTokenExpiration();
    if (!expires) return false;

    const fiveMinutes = 5 * 60 * 1000;
    return Date.now() >= (expires - fiveMinutes);
  }

  /**
   * Clear stored token
   */
  async clearToken(): Promise<void> {
    this.getStorage().removeItem(this.getKey('auth'));
    this.getStorage().removeItem(this.getKey('expires'));
    console.debug('Token cleared');
  }

  /**
   * Refresh token if needed
   */
  async refreshTokenIfNeeded(): Promise<boolean> {
    const tokenData = await this.getToken();
    if (!tokenData || !tokenData.refreshToken) {
      return false;
    }

    if (!this.isTokenExpiringSoon()) {
      return true; // Token is still valid
    }

    try {
      // This would typically make an API call to refresh the token
      // For now, we'll just return false to indicate refresh is needed
      console.debug('Token refresh needed');
      return false;
    } catch (error) {
      console.error('Token refresh failed:', error);
      await this.clearToken();
      return false;
    }
  }

  /**
   * Get authorization header value
   */
  async getAuthHeader(): Promise<string | null> {
    const tokenData = await this.getToken();
    return tokenData ? `Bearer ${tokenData.token}` : null;
  }

  /**
   * Store CSRF token
   */
  async storeCsrfToken(token: string): Promise<void> {
    try {
      const data = this.options.encrypt 
        ? await this.encryption.encrypt(token)
        : token;

      this.getStorage().setItem(this.getKey('csrf'), data);
    } catch (error) {
      console.error('Failed to store CSRF token:', error);
    }
  }

  /**
   * Get CSRF token
   */
  async getCsrfToken(): Promise<string | null> {
    try {
      const data = this.getStorage().getItem(this.getKey('csrf'));
      if (!data) return null;

      return this.options.encrypt 
        ? await this.encryption.decrypt(data)
        : data;
    } catch (error) {
      console.error('Failed to retrieve CSRF token:', error);
      return null;
    }
  }

  /**
   * Clear all stored data
   */
  async clearAll(): Promise<void> {
    const storage = this.getStorage();
    const keys = Object.keys(storage);
    
    keys.forEach(key => {
      if (key.startsWith(this.options.keyPrefix)) {
        storage.removeItem(key);
      }
    });

    console.debug('All secure tokens cleared');
  }
}

// Global secure token manager instance
export const secureTokenManager = new SecureTokenManager();

/**
 * Hook for using secure token management in React components
 */
export const useSecureToken = () => {
  const storeToken = async (tokenData: TokenData): Promise<void> => {
    await secureTokenManager.storeToken(tokenData);
  };

  const getToken = async (): Promise<TokenData | null> => {
    return await secureTokenManager.getToken();
  };

  const isValid = async (): Promise<boolean> => {
    return await secureTokenManager.isTokenValid();
  };

  const clearToken = async (): Promise<void> => {
    await secureTokenManager.clearToken();
  };

  const getAuthHeader = async (): Promise<string | null> => {
    return await secureTokenManager.getAuthHeader();
  };

  const refreshIfNeeded = async (): Promise<boolean> => {
    return await secureTokenManager.refreshTokenIfNeeded();
  };

  return {
    storeToken,
    getToken,
    isValid,
    clearToken,
    getAuthHeader,
    refreshIfNeeded
  };
};
