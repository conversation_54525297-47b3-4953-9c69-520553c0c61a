import { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { communityApi, type CommunityComment } from '../services/communityApi';
import { useToast } from './useToast';
import { useAuth } from './useAuth';
import { useLanguage } from './useLanguage';

export interface UsePostInteractionsReturn {
  // Comment state
  showCommentsFor: string | null;
  commentText: string;
  isSubmittingComment: boolean;
  
  // Modal states
  showUserModal: boolean;
  selectedUser: any;
  showModerationModal: boolean;
  selectedContent: any;
  
  // Actions
  handleToggleComments: (postId: string) => void;
  handleCommentChange: (text: string) => void;
  handleSubmitComment: (postId: string) => Promise<void>;
  handleLikeComment: (commentId: string, postId: string) => Promise<void>;
  handleUserClick: (userId: string) => void;
  handleReportContent: (type: string, id: string, author: string, preview: string) => void;
  
  // Modal actions
  closeUserModal: () => void;
  closeModerationModal: () => void;
  handleModerationAction: (action: string, reason: string) => Promise<void>;
}

export const usePostInteractions = (): UsePostInteractionsReturn => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user, isAuthenticated } = useAuth();
  const { showSuccess, showError, showInfo } = useToast();
  
  // Comment state
  const [showCommentsFor, setShowCommentsFor] = useState<string | null>(null);
  const [commentText, setCommentText] = useState('');
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);
  
  // Modal states
  const [showUserModal, setShowUserModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [showModerationModal, setShowModerationModal] = useState(false);
  const [selectedContent, setSelectedContent] = useState<any>(null);

  // Toggle comments visibility
  const handleToggleComments = useCallback((postId: string) => {
    setShowCommentsFor(prev => prev === postId ? null : postId);
  }, []);

  // Handle comment text change
  const handleCommentChange = useCallback((text: string) => {
    setCommentText(text);
  }, []);

  // Submit comment
  const handleSubmitComment = useCallback(async (postId: string) => {
    if (!isAuthenticated) {
      showInfo(t('community.messages.loginToComment'));
      return;
    }

    if (!commentText.trim()) {
      return;
    }

    setIsSubmittingComment(true);
    try {
      await communityApi.createComment(postId, { content: commentText.trim() });
      setCommentText('');
      showSuccess(isRTL ? 'تم إضافة التعليق' : 'Comment added successfully');
    } catch (error) {
      console.error('Comment submission error:', error);
      showError(t('community.messages.commentError'));
    } finally {
      setIsSubmittingComment(false);
    }
  }, [isAuthenticated, commentText, showInfo, showSuccess, showError, t, isRTL]);

  // Like comment
  const handleLikeComment = useCallback(async (commentId: string, postId: string) => {
    if (!isAuthenticated) {
      showInfo(t('community.messages.loginRequired'));
      return;
    }

    try {
      await communityApi.likeComment(commentId);
      showSuccess(isRTL ? 'تم الإعجاب بالتعليق' : 'Comment liked');
    } catch (error) {
      console.error('Like comment error:', error);
      showError(t('community.messages.likeError'));
    }
  }, [isAuthenticated, showInfo, showSuccess, showError, t, isRTL]);

  // Handle user click
  const handleUserClick = useCallback(async (userId: string) => {
    try {
      const userData = await communityApi.getUserProfile(userId);
      setSelectedUser(userData);
      setShowUserModal(true);
    } catch (error) {
      console.error('User profile error:', error);
      showError(t('community.messages.userProfileError'));
    }
  }, [showError, t]);

  // Handle report content
  const handleReportContent = useCallback((type: string, id: string, author: string, preview: string) => {
    setSelectedContent({
      type,
      id,
      author,
      preview
    });
    setShowModerationModal(true);
  }, []);

  // Close user modal
  const closeUserModal = useCallback(() => {
    setShowUserModal(false);
    setSelectedUser(null);
  }, []);

  // Close moderation modal
  const closeModerationModal = useCallback(() => {
    setShowModerationModal(false);
    setSelectedContent(null);
  }, []);

  // Handle moderation action
  const handleModerationAction = useCallback(async (action: string, reason: string) => {
    if (!selectedContent) return;

    try {
      await communityApi.reportContent(
        selectedContent.type,
        selectedContent.id,
        action,
        reason
      );
      showSuccess(t('community.messages.reportSubmitted'));
      closeModerationModal();
    } catch (error) {
      console.error('Moderation action error:', error);
      showError(t('community.messages.reportError'));
    }
  }, [selectedContent, showSuccess, showError, t, closeModerationModal]);

  return {
    // Comment state
    showCommentsFor,
    commentText,
    isSubmittingComment,
    
    // Modal states
    showUserModal,
    selectedUser,
    showModerationModal,
    selectedContent,
    
    // Actions
    handleToggleComments,
    handleCommentChange,
    handleSubmitComment,
    handleLikeComment,
    handleUserClick,
    handleReportContent,
    
    // Modal actions
    closeUserModal,
    closeModerationModal,
    handleModerationAction,
  };
};
