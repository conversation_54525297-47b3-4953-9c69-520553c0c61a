/**
 * 🎯 UNIFIED DESIGN SYSTEM
 * Centralized design tokens and component styles
 */

// ========================================
// DESIGN TOKENS
// ========================================

export const designTokens = {
  // Colors
  colors: {
    // Glass morphism palette
    glass: {
      bg: 'rgba(255, 255, 255, 0.05)',
      light: 'rgba(255, 255, 255, 0.03)',
      border: 'rgba(255, 255, 255, 0.2)',
      hover: 'rgba(255, 255, 255, 0.1)',
      active: 'rgba(255, 255, 255, 0.15)',
      primary: '#ffffff',
      secondary: 'rgba(255, 255, 255, 0.7)',
      muted: 'rgba(255, 255, 255, 0.5)',
      accent: 'rgba(255, 255, 255, 0.9)',
    },
    
    // Role-based colors
    role: {
      user: '#0ea5e9',
      entrepreneur: '#d946ef',
      mentor: '#22c55e',
      investor: '#f59e0b',
      admin: '#ef4444',
      super_admin: '#64748b',
      moderator: '#0ea5e9'
    },
    
    // Semantic colors
    semantic: {
      success: '#22c55e',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#0ea5e9',
      neutral: '#64748b'
    },
    
    // Background gradients
    gradients: {
      primary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      secondary: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      success: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      warning: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
      error: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
      dark: 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)'
    }
  },
  
  // Typography
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      arabic: ['Tajawal', 'Noto Sans Arabic', 'Cairo', 'sans-serif'],
      mono: ['JetBrains Mono', 'Monaco', 'Consolas', 'monospace']
    },
    
    fontSize: {
      xs: '0.75rem',    // 12px
      sm: '0.875rem',   // 14px
      base: '1rem',     // 16px
      lg: '1.125rem',   // 18px
      xl: '1.25rem',    // 20px
      '2xl': '1.5rem',  // 24px
      '3xl': '1.875rem', // 30px
      '4xl': '2.25rem', // 36px
      '5xl': '3rem',    // 48px
    },
    
    fontWeight: {
      light: '300',
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800'
    },
    
    lineHeight: {
      tight: '1.25',
      normal: '1.5',
      relaxed: '1.75'
    }
  },
  
  // Spacing
  spacing: {
    xs: '0.25rem',   // 4px
    sm: '0.5rem',    // 8px
    md: '1rem',      // 16px
    lg: '1.5rem',    // 24px
    xl: '2rem',      // 32px
    '2xl': '3rem',   // 48px
    '3xl': '4rem',   // 64px
    '4xl': '6rem',   // 96px
  },
  
  // Border radius
  borderRadius: {
    none: '0',
    sm: '0.25rem',   // 4px
    md: '0.5rem',    // 8px
    lg: '0.75rem',   // 12px
    xl: '1rem',      // 16px
    '2xl': '1.5rem', // 24px
    full: '9999px'
  },
  
  // Shadows
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    glass: '0 8px 32px rgba(31, 38, 135, 0.37)',
    glassLight: '0 4px 16px rgba(31, 38, 135, 0.2)'
  },
  
  // Transitions
  transitions: {
    fast: '150ms ease-in-out',
    normal: '300ms ease-in-out',
    slow: '500ms ease-in-out'
  },
  
  // Z-index
  zIndex: {
    dropdown: 1000,
    sticky: 1020,
    fixed: 1030,
    modal: 1040,
    popover: 1050,
    tooltip: 1060,
    toast: 1070
  }
};

// ========================================
// COMPONENT STYLES
// ========================================

export const componentStyles = {
  // Button variants
  button: {
    base: `
      inline-flex items-center justify-center font-semibold rounded-xl
      transition-all duration-300 ease-out
      focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-2
      disabled:opacity-50 disabled:cursor-not-allowed
    `,
    
    variants: {
      primary: `
        bg-gradient-to-r from-purple-600 to-blue-600 text-white
        hover:from-purple-700 hover:to-blue-700
        shadow-lg hover:shadow-xl
      `,
      
      secondary: `
        bg-white/10 text-white border border-white/20
        hover:bg-white/20 backdrop-blur-sm
      `,
      
      ghost: `
        text-white hover:bg-white/10
      `,
      
      glass: `
        bg-white/5 backdrop-blur-10 border border-white/20
        text-white hover:bg-white/10
        shadow-glass
      `
    },
    
    sizes: {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-6 py-3 text-lg',
      xl: 'px-8 py-4 text-xl'
    }
  },
  
  // Input variants
  input: {
    base: `
      w-full rounded-lg border transition-all duration-200
      focus:outline-none focus:ring-2 focus:ring-purple-500/50
      disabled:opacity-50 disabled:cursor-not-allowed
    `,
    
    variants: {
      default: `
        bg-white border-gray-300 text-gray-900
        focus:border-purple-500
      `,
      
      glass: `
        bg-white/5 backdrop-blur-10 border-white/20
        text-white placeholder-white/50
        focus:border-white/40 focus:bg-white/10
      `
    },
    
    sizes: {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-4 py-3 text-lg'
    }
  },
  
  // Card variants
  card: {
    base: `
      rounded-xl transition-all duration-300
    `,
    
    variants: {
      default: `
        bg-white border border-gray-200 shadow-md
        hover:shadow-lg
      `,
      
      glass: `
        bg-white/5 backdrop-blur-10 border border-white/20
        shadow-glass hover:bg-white/10
      `,
      
      elevated: `
        bg-white shadow-xl border-0
        hover:shadow-2xl hover:-translate-y-1
      `
    }
  },
  
  // Modal variants
  modal: {
    overlay: `
      fixed inset-0 bg-black/50 backdrop-blur-sm
      flex items-center justify-center p-4
      z-modal
    `,
    
    content: `
      bg-white/10 backdrop-blur-20 border border-white/20
      rounded-2xl shadow-2xl max-w-md w-full
      transform transition-all duration-300
    `
  }
};

// ========================================
// UTILITY FUNCTIONS
// ========================================

export function getButtonClasses(
  variant: keyof typeof componentStyles.button.variants = 'primary',
  size: keyof typeof componentStyles.button.sizes = 'md',
  className?: string
): string {
  return [
    componentStyles.button.base,
    componentStyles.button.variants[variant],
    componentStyles.button.sizes[size],
    className
  ].filter(Boolean).join(' ');
}

export function getInputClasses(
  variant: keyof typeof componentStyles.input.variants = 'default',
  size: keyof typeof componentStyles.input.sizes = 'md',
  className?: string
): string {
  return [
    componentStyles.input.base,
    componentStyles.input.variants[variant],
    componentStyles.input.sizes[size],
    className
  ].filter(Boolean).join(' ');
}

export function getCardClasses(
  variant: keyof typeof componentStyles.card.variants = 'default',
  className?: string
): string {
  return [
    componentStyles.card.base,
    componentStyles.card.variants[variant],
    className
  ].filter(Boolean).join(' ');
}

export function getRoleColor(role: string): string {
  return designTokens.colors.role[role as keyof typeof designTokens.colors.role] || designTokens.colors.role.user;
}

export function getSemanticColor(type: keyof typeof designTokens.colors.semantic): string {
  return designTokens.colors.semantic[type];
}

// ========================================
// CSS-IN-JS HELPERS
// ========================================

export function createGlassMorphismStyle(opacity: number = 0.05): React.CSSProperties {
  return {
    background: `rgba(255, 255, 255, ${opacity})`,
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    borderRadius: '16px',
    boxShadow: '0 8px 32px rgba(31, 38, 135, 0.37)',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
  };
}

export function createGradientStyle(gradient: keyof typeof designTokens.colors.gradients): React.CSSProperties {
  return {
    background: designTokens.colors.gradients[gradient]
  };
}

export default {
  designTokens,
  componentStyles,
  getButtonClasses,
  getInputClasses,
  getCardClasses,
  getRoleColor,
  getSemanticColor,
  createGlassMorphismStyle,
  createGradientStyle
};
