"""
Community Security Utilities
Provides content sanitization, validation, and security measures for community features
"""

import re
import html
import logging
from typing import Optional, List, Dict, Any
from urllib.parse import urlparse
from django.core.exceptions import ValidationError
from django.utils.html import strip_tags
from django.conf import settings

# Configure logging
logger = logging.getLogger(__name__)

# Allowed HTML tags for community content
ALLOWED_HTML_TAGS = {
    'p', 'br', 'strong', 'em', 'u', 'i', 'b', 
    'ul', 'ol', 'li', 'blockquote', 'code', 'pre'
}

# Dangerous patterns to detect and remove
DANGEROUS_PATTERNS = [
    r'<script[^>]*>.*?</script>',  # Script tags
    r'javascript:',  # JavaScript URLs
    r'on\w+\s*=',  # Event handlers (onclick, onload, etc.)
    r'<iframe[^>]*>.*?</iframe>',  # Iframe tags
    r'<object[^>]*>.*?</object>',  # Object tags
    r'<embed[^>]*>.*?</embed>',  # Embed tags
    r'<form[^>]*>.*?</form>',  # Form tags
    r'<input[^>]*>',  # Input tags
    r'<meta[^>]*>',  # Meta tags
    r'<link[^>]*>',  # Link tags
    r'<style[^>]*>.*?</style>',  # Style tags
]

# Content validation limits
CONTENT_LIMITS = {
    'post_content': {
        'min_length': 1,
        'max_length': 5000,
        'max_lines': 100,
    },
    'comment_content': {
        'min_length': 1,
        'max_length': 1000,
        'max_lines': 20,
    },
    'title': {
        'min_length': 1,
        'max_length': 200,
        'max_lines': 1,
    }
}

# Profanity and inappropriate content patterns (basic implementation)
INAPPROPRIATE_PATTERNS = [
    # Add patterns for inappropriate content detection
    # This is a basic implementation - in production, use a proper content moderation service
    r'\b(spam|scam|phishing)\b',
    r'(https?://)?[^\s]+\.(tk|ml|ga|cf)\b',  # Suspicious domains
]


class ContentSanitizer:
    """
    Content sanitization and validation utility
    """
    
    def __init__(self):
        self.dangerous_pattern = re.compile('|'.join(DANGEROUS_PATTERNS), re.IGNORECASE | re.DOTALL)
        self.inappropriate_pattern = re.compile('|'.join(INAPPROPRIATE_PATTERNS), re.IGNORECASE)
    
    def sanitize_html(self, content: str) -> str:
        """
        Sanitize HTML content by removing dangerous elements
        """
        if not content:
            return ""
        
        # Remove dangerous patterns
        content = self.dangerous_pattern.sub('', content)
        
        # Escape HTML entities
        content = html.escape(content)
        
        # Allow only safe HTML tags (basic implementation)
        # In production, consider using a library like bleach
        content = self._allow_safe_tags(content)
        
        return content.strip()
    
    def _allow_safe_tags(self, content: str) -> str:
        """
        Allow only safe HTML tags
        """
        # This is a basic implementation
        # In production, use a proper HTML sanitization library like bleach
        
        # For now, we'll be conservative and strip all HTML
        # except for basic formatting that we manually allow
        content = strip_tags(content)
        
        # Allow basic line breaks
        content = content.replace('\n', '<br>')
        
        return content
    
    def validate_content_length(self, content: str, content_type: str) -> None:
        """
        Validate content length and structure
        """
        if content_type not in CONTENT_LIMITS:
            raise ValidationError(f"Unknown content type: {content_type}")
        
        limits = CONTENT_LIMITS[content_type]
        
        # Check minimum length
        if len(content.strip()) < limits['min_length']:
            raise ValidationError(f"Content too short. Minimum {limits['min_length']} characters required.")
        
        # Check maximum length
        if len(content) > limits['max_length']:
            raise ValidationError(f"Content too long. Maximum {limits['max_length']} characters allowed.")
        
        # Check line count
        line_count = content.count('\n') + 1
        if line_count > limits['max_lines']:
            raise ValidationError(f"Too many lines. Maximum {limits['max_lines']} lines allowed.")
    
    def check_inappropriate_content(self, content: str) -> List[str]:
        """
        Check for inappropriate content patterns
        Returns list of issues found
        """
        issues = []
        
        # Check for inappropriate patterns
        matches = self.inappropriate_pattern.findall(content.lower())
        if matches:
            issues.append("Content contains inappropriate or suspicious elements")
            logger.warning(f"Inappropriate content detected: {matches}")
        
        # Check for excessive capitalization (potential spam)
        if len(content) > 50:
            caps_ratio = sum(1 for c in content if c.isupper()) / len(content)
            if caps_ratio > 0.7:
                issues.append("Excessive use of capital letters")
        
        # Check for repeated characters (potential spam)
        if re.search(r'(.)\1{10,}', content):
            issues.append("Excessive repeated characters")
        
        return issues
    
    def validate_urls(self, content: str) -> List[str]:
        """
        Validate URLs in content
        Returns list of issues found
        """
        issues = []
        
        # Find URLs in content
        url_pattern = r'https?://[^\s<>"\']+|www\.[^\s<>"\']+|[^\s<>"\']+\.[a-z]{2,}[^\s<>"\']*'
        urls = re.findall(url_pattern, content, re.IGNORECASE)
        
        for url in urls:
            try:
                # Add protocol if missing
                if not url.startswith(('http://', 'https://')):
                    url = 'http://' + url
                
                parsed = urlparse(url)
                
                # Check for suspicious domains
                if parsed.netloc.endswith(('.tk', '.ml', '.ga', '.cf')):
                    issues.append(f"Suspicious domain detected: {parsed.netloc}")
                
                # Check for IP addresses (potential security risk)
                if re.match(r'^\d+\.\d+\.\d+\.\d+', parsed.netloc):
                    issues.append("Direct IP address URLs are not allowed")
                
            except Exception as e:
                issues.append(f"Invalid URL format: {url}")
        
        return issues
    
    def sanitize_and_validate(self, content: str, content_type: str) -> Dict[str, Any]:
        """
        Complete sanitization and validation process
        Returns dict with sanitized content and validation results
        """
        result = {
            'original_content': content,
            'sanitized_content': '',
            'is_valid': True,
            'issues': [],
            'warnings': []
        }
        
        try:
            # Sanitize content
            sanitized = self.sanitize_html(content)
            result['sanitized_content'] = sanitized
            
            # Validate length
            self.validate_content_length(sanitized, content_type)
            
            # Check for inappropriate content
            inappropriate_issues = self.check_inappropriate_content(sanitized)
            if inappropriate_issues:
                result['warnings'].extend(inappropriate_issues)
            
            # Validate URLs
            url_issues = self.validate_urls(sanitized)
            if url_issues:
                result['warnings'].extend(url_issues)
            
            # Log security events if issues found
            if result['warnings']:
                logger.warning(f"Content validation warnings for {content_type}: {result['warnings']}")
            
        except ValidationError as e:
            result['is_valid'] = False
            result['issues'].append(str(e))
            logger.error(f"Content validation failed for {content_type}: {e}")
        
        except Exception as e:
            result['is_valid'] = False
            result['issues'].append("Content validation error occurred")
            logger.error(f"Unexpected error in content validation: {e}")
        
        return result


# Global sanitizer instance
content_sanitizer = ContentSanitizer()


def sanitize_community_content(content: str, content_type: str = 'post_content') -> str:
    """
    Convenience function for content sanitization
    """
    result = content_sanitizer.sanitize_and_validate(content, content_type)
    
    if not result['is_valid']:
        raise ValidationError(result['issues'])
    
    return result['sanitized_content']


def validate_community_content(content: str, content_type: str = 'post_content') -> None:
    """
    Convenience function for content validation
    """
    result = content_sanitizer.sanitize_and_validate(content, content_type)
    
    if not result['is_valid']:
        raise ValidationError(result['issues'])


class SecurityEventLogger:
    """
    Security event logging utility
    """
    
    @staticmethod
    def log_suspicious_activity(user_id: Optional[int], activity_type: str, details: Dict[str, Any]):
        """
        Log suspicious security activity
        """
        logger.warning(
            f"SECURITY EVENT - User: {user_id or 'Anonymous'}, "
            f"Activity: {activity_type}, Details: {details}"
        )
    
    @staticmethod
    def log_authentication_failure(user_id: Optional[int], reason: str, request_info: Dict[str, Any]):
        """
        Log authentication failures
        """
        logger.error(
            f"AUTH FAILURE - User: {user_id or 'Anonymous'}, "
            f"Reason: {reason}, Request: {request_info}"
        )
    
    @staticmethod
    def log_rate_limit_exceeded(user_id: Optional[int], endpoint: str, ip_address: str):
        """
        Log rate limit violations
        """
        logger.warning(
            f"RATE LIMIT EXCEEDED - User: {user_id or 'Anonymous'}, "
            f"Endpoint: {endpoint}, IP: {ip_address}"
        )


# Global security event logger
security_logger = SecurityEventLogger()
