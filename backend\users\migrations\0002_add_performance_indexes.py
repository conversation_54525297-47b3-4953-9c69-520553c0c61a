# Generated by Django for performance optimization

from django.db import migrations, models


def create_indexes_safely(apps, schema_editor):
    """Create indexes only if tables and columns exist"""
    with schema_editor.connection.cursor() as cursor:
        # Add critical missing indexes for User model (Django's auth_user table)
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_auth_user_is_active ON auth_user(is_active) WHERE is_active = true;")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_auth_user_username_active ON auth_user(username, is_active);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_auth_user_email_active ON auth_user(email, is_active);")

        # Check if users_userprofile table exists and has expected columns
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users_userprofile';")
        if cursor.fetchone():
            cursor.execute("PRAGMA table_info(users_userprofile);")
            columns = [row[1] for row in cursor.fetchall()]

            if 'user_id' in columns:
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_userprofile_user_id ON users_userprofile(user_id);")
            if 'is_active' in columns:
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_userprofile_is_active ON users_userprofile(is_active);")
            if 'created_at' in columns:
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_userprofile_created_at ON users_userprofile(created_at DESC);")

        # Check if users_userroleassignment table exists and has expected columns
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users_userroleassignment';")
        if cursor.fetchone():
            cursor.execute("PRAGMA table_info(users_userroleassignment);")
            columns = [row[1] for row in cursor.fetchall()]

            # Use user_profile_id instead of user_id for this table
            if 'user_profile_id' in columns:
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_userroleassignment_user_profile_id ON users_userroleassignment(user_profile_id);")
            if 'role_id' in columns:
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_userroleassignment_role_id ON users_userroleassignment(role_id);")
            if 'is_active' in columns:
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_userroleassignment_is_active ON users_userroleassignment(is_active);")

        # Check if users_userapproval table exists and has expected columns
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users_userapproval';")
        if cursor.fetchone():
            cursor.execute("PRAGMA table_info(users_userapproval);")
            columns = [row[1] for row in cursor.fetchall()]

            if 'user_id' in columns:
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_userapproval_user_id ON users_userapproval(user_id);")
            if 'status' in columns:
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_userapproval_status ON users_userapproval(status);")
            if 'created_at' in columns:
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_userapproval_created_at ON users_userapproval(created_at DESC);")


def reverse_create_indexes(apps, schema_editor):
    """Drop the indexes"""
    with schema_editor.connection.cursor() as cursor:
        cursor.execute("DROP INDEX IF EXISTS idx_auth_user_is_active;")
        cursor.execute("DROP INDEX IF EXISTS idx_auth_user_username_active;")
        cursor.execute("DROP INDEX IF EXISTS idx_auth_user_email_active;")
        cursor.execute("DROP INDEX IF EXISTS idx_users_userprofile_user_id;")
        cursor.execute("DROP INDEX IF EXISTS idx_users_userprofile_is_active;")
        cursor.execute("DROP INDEX IF EXISTS idx_users_userprofile_created_at;")
        cursor.execute("DROP INDEX IF EXISTS idx_users_userroleassignment_user_profile_id;")
        cursor.execute("DROP INDEX IF EXISTS idx_users_userroleassignment_role_id;")
        cursor.execute("DROP INDEX IF EXISTS idx_users_userroleassignment_is_active;")
        cursor.execute("DROP INDEX IF EXISTS idx_users_userapproval_user_id;")
        cursor.execute("DROP INDEX IF EXISTS idx_users_userapproval_status;")
        cursor.execute("DROP INDEX IF EXISTS idx_users_userapproval_created_at;")


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(
            create_indexes_safely,
            reverse_create_indexes,
        ),
    ]
