import React, { memo, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { MessageCircle, TrendingUp, Users, Bookmark, Plus } from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { useArrowKeyNavigation } from '../../hooks/useAccessibility';
import { CommunityPost } from '../../services/communityApi';
import PostCard from './PostCard';

interface CommunityStats {
  trending_posts_count: number;
  verified_authors_count: number;
  saved_posts_count: number;
}

interface PostsFeedProps {
  posts: CommunityPost[];
  searchResults: CommunityPost[];
  searchQuery: string;
  isSearching: boolean;
  activeView: string;
  isLoading: boolean;
  onViewChange: (view: string) => void;
  onLikePost: (postId: string) => void;
  onCommentPost: (postId: string, content: string) => void;
  onSharePost: (postId: string) => void;
  onSavePost: (postId: string) => void;
  onEditPost?: (postId: string, updatedPost: CommunityPost) => void;
  onDeletePost?: (postId: string) => void;
  onReportPost?: (postId: string) => void;
  // Removed complex comment handlers - using simple comments now
  onCreatePost: () => void;
  isAuthenticated: boolean;
  computedStats: CommunityStats;
}

const PostsFeed: React.FC<PostsFeedProps> = memo(({
  posts,
  searchResults,
  searchQuery,
  isSearching,
  activeView,
  isLoading,
  onViewChange,
  onLikePost,
  onCommentPost,
  onSharePost,
  onSavePost,
  onEditPost,
  onDeletePost,
  onReportPost,
  // Removed complex comment handlers
  onCreatePost,
  isAuthenticated,
  computedStats
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // Arrow key navigation for posts
  const postsContainerRef = useArrowKeyNavigation('[data-post-id]', {
    orientation: 'vertical',
    wrap: true
  });

  // Simple, clean display posts logic
  const displayPosts = useMemo(() => {
    return searchQuery.trim() ? searchResults : posts;
  }, [searchQuery, searchResults, posts]);

  // Memoized navigation tabs to prevent recreation on every render
  const navigationTabs = useMemo(() => [
    {
      id: 'feed',
      label: t('community.navigation.feed'),
      icon: MessageCircle,
      count: posts.length
    },
    {
      id: 'trending',
      label: t('community.navigation.trending'),
      icon: TrendingUp,
      count: computedStats?.trending_posts_count || 0
    },
    {
      id: 'following',
      label: t('community.navigation.following'),
      icon: Users,
      count: computedStats?.verified_authors_count || 0
    },
    {
      id: 'saved',
      label: t('community.navigation.saved'),
      icon: Bookmark,
      count: computedStats?.saved_posts_count || 0
    }
  ], [t, posts.length, computedStats]);

  return (
    <div className={`lg:col-span-3 order-1 ${isRTL ? 'lg:order-1' : 'lg:order-2'}`}>
      {/* Navigation Tabs */}
      <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-1 mb-6">
        <div className="flex overflow-x-auto scrollbar-hide">
          {navigationTabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => onViewChange(tab.id)}
                className={`flex items-center gap-2 px-4 py-3 rounded-lg font-medium transition-all duration-200 whitespace-nowrap min-w-0 flex-1 sm:flex-initial ${
                  activeView === tab.id
                    ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg'
                    : 'text-gray-400 hover:text-white hover:bg-white/10'
                }`}
              >
                <Icon className="w-4 h-4 flex-shrink-0" />
                <span className={`font-medium flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>{tab.label}</span>
                {tab.count > 0 && (
                  <span className="bg-white/20 text-xs px-2 py-1 rounded-full min-w-[1.5rem] text-center">
                    {tab.count}
                  </span>
                )}
              </button>
            );
          })}
        </div>
      </div>

      {/* Search Results Header */}
      {searchQuery.trim() && (
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-white">
              {t('community.posts.searchResults', { query: searchQuery })}
            </h2>
            <div className="text-sm text-gray-400">
              {isSearching
                ? t('community.posts.searching')
                : t('community.posts.resultsCount', { count: searchResults.length })
              }
            </div>
          </div>
        </div>
      )}

      {/* Posts Content */}
      <div id="main-content" className="space-y-6">
        {isLoading ? (
          /* Loading State */
          <div className="flex flex-col items-center justify-center py-12">
            <div className="animate-spin w-8 h-8 border-4 border-purple-400 border-t-transparent rounded-full mb-4"></div>
            <p className="text-gray-400 text-center">
              {t('community.messages.loadingData')}
            </p>
          </div>
        ) : displayPosts.length === 0 ? (
          /* Empty State */
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <MessageCircle className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">
              {searchQuery.trim() 
                ? t('community.posts.noResults')
                : t('community.posts.emptyFeed')
              }
            </h3>
            {!searchQuery.trim() && (
              <>
                <p className="text-gray-400 mb-6 max-w-md mx-auto">
                  {t('community.posts.emptyState')}
                </p>
                <button
                  onClick={() => {
                    if (!isAuthenticated) {
                      // Show login message - this will be handled by the parent component
                      return;
                    }
                    onCreatePost();
                  }}
                  className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors flex items-center gap-2 mx-auto"
                >
                  <Plus className="w-4 h-4" />
                  {t('community.actions.createPost')}
                </button>
              </>
            )}
          </div>
        ) : (
          /* Posts List */
          <div
            ref={postsContainerRef}
            className="space-y-6"
            role="list"
            aria-label={t('community.posts.list', 'Community posts')}
          >
            {displayPosts.map((post, index) => (
              <div
                key={post.id}
                role="listitem"
                data-post-id={post.id}
                tabIndex={index === 0 ? 0 : -1}
                aria-label={t('community.posts.postByAuthor', 'Post by {{author}}', { author: post.author.full_name })}
              >
                <PostCard
                  post={post}
                  onLike={onLikePost}
                  onComment={onCommentPost}
                  onShare={onSharePost}
                  onSave={onSavePost}
                  onEdit={onEditPost}
                  onDelete={onDeletePost}
                  onReport={onReportPost}
                  // Simple comments - no complex handlers needed
                  isAuthenticated={isAuthenticated}
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
});

PostsFeed.displayName = 'PostsFeed';

export default PostsFeed;
