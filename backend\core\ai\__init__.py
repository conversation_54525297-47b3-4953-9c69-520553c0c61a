"""
🎯 UNIFIED AI SERVICE PACKAGE
Consolidated AI functionality for Yasmeen AI platform

This package consolidates all AI-related functionality from:
- core.ai_service (main AI service)
- ai_recommendations (business recommendations)
- ai_core (intelligent features)
- ai_models (ML engines)

Structure:
- services/     - Core AI service classes
- models/       - Consolidated AI models
- engines/      - ML and analytics engines
- views/        - API views and endpoints
- config/       - Configuration and settings
"""

# Lazy import to avoid circular dependencies
def get_ai_service():
    """Get the consolidated AI service instance"""
    from core.services.consolidated_ai_service import ConsolidatedAIService
    return ConsolidatedAIService()

# For backward compatibility
ai_service = None

# Export the consolidated service
__all__ = [
    'get_ai_service',
    'ai_service'
]
