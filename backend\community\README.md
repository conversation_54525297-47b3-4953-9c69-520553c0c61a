# Community App

A comprehensive community platform with posts, comments, user interactions, and advanced caching.

## Features

### Core Features
- **Posts & Comments**: Create, read, update, delete posts and comments
- **User Interactions**: Like, save, share posts and comments
- **User Following**: Follow/unfollow users, view followers/following
- **Hashtags**: Trending hashtags and tag-based filtering
- **User Recommendations**: AI-powered user discovery
- **Community Stats**: Real-time community statistics

### Performance Features
- **Redis Caching**: Multi-tier caching strategy with Redis backend
- **HTTP Caching**: Endpoint-specific cache headers and ETags
- **Progressive Loading**: Posts load first, stats load in background
- **Request Deduplication**: Prevent duplicate API calls
- **Database Optimization**: Efficient queries with select_related/prefetch_related

### Security Features
- **Input Sanitization**: XSS prevention and content validation
- **Rate Limiting**: Progressive penalties for excessive requests
- **Authentication**: JWT-based authentication with activity logging
- **CSRF Protection**: Cross-site request forgery prevention
- **Security Headers**: Comprehensive security headers for API responses

## Architecture

### Models
- `CommunityPost`: Main post model with author, content, tags
- `CommunityComment`: Nested comments with parent-child relationships
- `UserFollow`: User following relationships
- `PostSave`: Saved posts functionality
- `CommunityStats`: Cached community statistics

### Views
- `CommunityPostViewSet`: CRUD operations for posts
- `CommunityCommentViewSet`: CRUD operations for comments
- `CommunityStatsViewSet`: Community statistics endpoints
- `HashtagViewSet`: Trending hashtags
- `UserRecommendationViewSet`: User discovery

### Caching Strategy
- **Posts Cache**: 5-minute TTL for post listings
- **Stats Cache**: 1-hour TTL for community statistics
- **Hashtags Cache**: 30-minute TTL for trending hashtags
- **User Recommendations**: 2-hour TTL for user suggestions
- **User Feed**: 10-minute TTL for personalized feeds

## API Endpoints

### Posts
- `GET /api/community/posts/` - List posts
- `POST /api/community/posts/` - Create post
- `GET /api/community/posts/{id}/` - Get post details
- `PUT /api/community/posts/{id}/` - Update post
- `DELETE /api/community/posts/{id}/` - Delete post
- `POST /api/community/posts/{id}/like/` - Like/unlike post
- `POST /api/community/posts/{id}/save/` - Save/unsave post
- `POST /api/community/posts/{id}/share/` - Share post
- `GET /api/community/posts/{id}/comments/` - Get post comments
- `GET /api/community/posts/trending/` - Get trending posts
- `GET /api/community/posts/feed/` - Get user feed

### Comments
- `GET /api/community/comments/` - List comments
- `POST /api/community/comments/` - Create comment
- `GET /api/community/comments/{id}/` - Get comment details
- `PUT /api/community/comments/{id}/` - Update comment
- `DELETE /api/community/comments/{id}/` - Delete comment
- `POST /api/community/comments/{id}/like/` - Like/unlike comment

### User Interactions
- `POST /api/community/users/follow/` - Follow user
- `GET /api/community/users/follow-status/` - Check follow status
- `GET /api/community/users/followers/` - Get user followers
- `GET /api/community/users/following/` - Get user following

### Statistics & Discovery
- `GET /api/community/stats/current/` - Get community stats
- `GET /api/community/stats/ping/` - Health check
- `GET /api/community/hashtags/trending/` - Get trending hashtags
- `GET /api/community/users/recommendations/` - Get user recommendations

## Management Commands

### Cache Management
```bash
# Warm up cache
python manage.py warm_cache --all
python manage.py warm_cache --stats --hashtags

# Check cache status
python manage.py cache_stats
python manage.py cache_stats --test --keys

# Clear cache
python manage.py cache_stats --clear
```

### Code Quality
```bash
# Check code quality
python manage.py code_quality_check --verbose

# Organize imports
python scripts/organize_imports.py community/
```

## Testing

### Backend Tests
```bash
# Run all community tests
pytest backend/community/tests/

# Run specific test categories
pytest backend/community/tests/test_security.py
pytest backend/community/tests/test_views.py
pytest backend/community/tests/test_models.py

# Run with coverage
pytest --cov=community backend/community/tests/
```

### Frontend Tests
```bash
# Run community hook tests
npm test -- useCommunity

# Run community component tests
npm test -- CommunityPage
```

## Configuration

### Cache Settings
```python
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'TIMEOUT': 300,
    },
    'community': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/2',
        'TIMEOUT': 900,
    },
}

CACHE_TIMEOUTS = {
    'community_posts': 300,      # 5 minutes
    'community_stats': 3600,     # 1 hour
    'trending_hashtags': 1800,   # 30 minutes
    'user_recommendations': 7200, # 2 hours
    'user_feed': 600,           # 10 minutes
}
```

### Security Settings
```python
COMMUNITY_RATE_LIMITS = {
    'posts_create': '10/hour',
    'comments_create': '30/hour',
    'likes': '100/hour',
    'follows': '20/hour',
}

COMMUNITY_SECURITY = {
    'content_sanitization': True,
    'xss_protection': True,
    'rate_limiting': True,
    'security_logging': True,
}
```

## Performance Monitoring

### Metrics Tracked
- API response times
- Cache hit/miss rates
- Database query counts
- User engagement metrics
- Error rates and types

### Optimization Techniques
- Database query optimization with select_related/prefetch_related
- Redis caching with intelligent invalidation
- HTTP caching with ETags and cache-control headers
- Progressive loading for better perceived performance
- Request deduplication to prevent redundant API calls

## Security Considerations

### Input Validation
- HTML sanitization for post content
- XSS prevention in user-generated content
- SQL injection prevention through ORM usage
- File upload validation and scanning

### Authentication & Authorization
- JWT token validation
- Permission-based access control
- Rate limiting per user and endpoint
- Activity logging for security events

### Data Protection
- Sensitive data encryption
- Secure token storage
- HTTPS enforcement
- CSRF protection

## Troubleshooting

### Common Issues
1. **Cache Connection Errors**: Check Redis server status
2. **Slow API Responses**: Monitor database queries and cache hit rates
3. **Authentication Failures**: Verify JWT token validity
4. **Rate Limiting**: Check user request patterns

### Debug Commands
```bash
# Check cache health
python manage.py cache_stats --test

# Monitor API performance
python manage.py shell -c "from community.cache import CommunityCache; print(CommunityCache.get_stats())"

# View security logs
tail -f logs/security.log
```
