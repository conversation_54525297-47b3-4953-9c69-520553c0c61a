/**
 * Business Plans Page - Entrepreneur Dashboard
 * Comprehensive business plan management and creation
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import {
  FileText,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Share2,
  Download,
  Star,
  Clock,
  Users,
  TrendingUp,
  BarChart3,
  Target,
  DollarSign,
  Calendar,
  CheckCircle,
  AlertCircle,
  MoreHorizontal,
  Copy,
  Trash2
} from 'lucide-react';

interface BusinessPlan {
  id: string;
  title: string;
  description: string;
  status: 'draft' | 'in_review' | 'approved' | 'needs_revision' | 'completed';
  progress: number;
  createdAt: string;
  updatedAt: string;
  businessIdea: {
    id: string;
    title: string;
  };
  template: {
    id: string;
    name: string;
    category: string;
  };
  collaborators: Array<{
    id: string;
    name: string;
    role: string;
    avatar?: string;
  }>;
  metrics: {
    views: number;
    downloads: number;
    shares: number;
    rating: number;
  };
  tags: string[];
  industry: string;
  fundingGoal?: number;
  timeline: string;
}

const BusinessPlansPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [businessPlans, setBusinessPlans] = useState<BusinessPlan[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedSort, setSelectedSort] = useState('updated_desc');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  useEffect(() => {
    loadBusinessPlans();
  }, [selectedFilter, selectedSort]);

  const loadBusinessPlans = async () => {
    setLoading(true);
    try {
      // Simulate API call - replace with real API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setBusinessPlans([
        {
          id: '1',
          title: 'E-commerce Platform for Syrian Artisans',
          description: 'A comprehensive business plan for creating an online marketplace connecting Syrian artisans with global customers.',
          status: 'in_review',
          progress: 75,
          createdAt: '2024-01-10T10:00:00Z',
          updatedAt: '2024-01-15T14:30:00Z',
          businessIdea: { id: 'idea1', title: 'Syrian Artisan Marketplace' },
          template: { id: 'template1', name: 'E-commerce Business Plan', category: 'Technology' },
          collaborators: [
            { id: 'user1', name: 'Ahmed Hassan', role: 'Co-founder', avatar: '/avatars/user1.jpg' },
            { id: 'user2', name: 'Fatima Al-Zahra', role: 'Marketing Lead' }
          ],
          metrics: { views: 245, downloads: 12, shares: 8, rating: 4.5 },
          tags: ['e-commerce', 'artisans', 'marketplace', 'syria'],
          industry: 'Technology',
          fundingGoal: 50000,
          timeline: '18 months'
        },
        {
          id: '2',
          title: 'Sustainable Agriculture Initiative',
          description: 'Business plan for implementing sustainable farming practices and organic food distribution in rural Syria.',
          status: 'draft',
          progress: 45,
          createdAt: '2024-01-08T09:15:00Z',
          updatedAt: '2024-01-14T16:20:00Z',
          businessIdea: { id: 'idea2', title: 'Organic Farming Network' },
          template: { id: 'template2', name: 'Agriculture Business Plan', category: 'Agriculture' },
          collaborators: [
            { id: 'user3', name: 'Omar Khalil', role: 'Agricultural Expert' }
          ],
          metrics: { views: 89, downloads: 3, shares: 2, rating: 4.0 },
          tags: ['agriculture', 'sustainable', 'organic', 'rural'],
          industry: 'Agriculture',
          fundingGoal: 25000,
          timeline: '24 months'
        },
        {
          id: '3',
          title: 'Educational Technology Platform',
          description: 'Comprehensive plan for developing an AI-powered educational platform for Syrian students.',
          status: 'approved',
          progress: 90,
          createdAt: '2024-01-05T11:30:00Z',
          updatedAt: '2024-01-13T10:45:00Z',
          businessIdea: { id: 'idea3', title: 'AI Education Platform' },
          template: { id: 'template3', name: 'EdTech Business Plan', category: 'Education' },
          collaborators: [
            { id: 'user4', name: 'Layla Mansour', role: 'Product Manager' },
            { id: 'user5', name: 'Yusuf Ibrahim', role: 'Tech Lead' },
            { id: 'user6', name: 'Nour Abdallah', role: 'UX Designer' }
          ],
          metrics: { views: 456, downloads: 28, shares: 15, rating: 4.8 },
          tags: ['education', 'ai', 'technology', 'students'],
          industry: 'Education',
          fundingGoal: 100000,
          timeline: '12 months'
        }
      ]);
    } catch (error) {
      console.error('Error loading business plans:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'in_review':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'needs_revision':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'completed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <Edit className="w-4 h-4" />;
      case 'in_review':
        return <Clock className="w-4 h-4" />;
      case 'approved':
        return <CheckCircle className="w-4 h-4" />;
      case 'needs_revision':
        return <AlertCircle className="w-4 h-4" />;
      case 'completed':
        return <Star className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const filteredPlans = businessPlans.filter(plan => {
    const matchesSearch = searchQuery === '' || 
      plan.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      plan.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      plan.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesFilter = selectedFilter === 'all' || plan.status === selectedFilter;
    
    return matchesSearch && matchesFilter;
  });

  const sortedPlans = [...filteredPlans].sort((a, b) => {
    switch (selectedSort) {
      case 'title_asc':
        return a.title.localeCompare(b.title);
      case 'title_desc':
        return b.title.localeCompare(a.title);
      case 'created_desc':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      case 'created_asc':
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      case 'updated_desc':
        return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
      case 'progress_desc':
        return b.progress - a.progress;
      case 'rating_desc':
        return b.metrics.rating - a.metrics.rating;
      default:
        return 0;
    }
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 text-white p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
          <span className="ml-4 text-lg">{t('Loading business plans...')}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className={`flex items-center justify-between mb-8 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="w-12 h-12 bg-purple-600/30 rounded-lg flex items-center justify-center">
              <FileText className="w-6 h-6 text-purple-400" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">{t('My Business Plans')}</h1>
              <p className="text-purple-200">{t('Create, manage, and track your business plans')}</p>
            </div>
          </div>
          
          <button
            onClick={() => navigate('/entrepreneur/business-plans/create')}
            className="flex items-center gap-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
          >
            <Plus className="w-4 h-4" />
            {t('Create New Plan')}
          </button>
        </div>

        {/* Filters and Search */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 mb-6">
          <div className={`flex flex-wrap items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="flex items-center gap-2 flex-1 max-w-md">
              <Search className="w-4 h-4 text-purple-400" />
              <input
                type="text"
                placeholder={t('Search business plans...')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 bg-indigo-900/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-purple-300 text-sm"
              />
            </div>

            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-purple-400" />
              <select
                value={selectedFilter}
                onChange={(e) => setSelectedFilter(e.target.value)}
                className="bg-indigo-900/50 border border-indigo-700 rounded-lg px-3 py-2 text-white text-sm"
              >
                <option value="all">{t('All Status')}</option>
                <option value="draft">{t('Draft')}</option>
                <option value="in_review">{t('In Review')}</option>
                <option value="approved">{t('Approved')}</option>
                <option value="needs_revision">{t('Needs Revision')}</option>
                <option value="completed">{t('Completed')}</option>
              </select>
            </div>

            <div className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4 text-purple-400" />
              <select
                value={selectedSort}
                onChange={(e) => setSelectedSort(e.target.value)}
                className="bg-indigo-900/50 border border-indigo-700 rounded-lg px-3 py-2 text-white text-sm"
              >
                <option value="updated_desc">{t('Recently Updated')}</option>
                <option value="created_desc">{t('Recently Created')}</option>
                <option value="title_asc">{t('Title A-Z')}</option>
                <option value="progress_desc">{t('Progress High-Low')}</option>
                <option value="rating_desc">{t('Rating High-Low')}</option>
              </select>
            </div>
          </div>
        </div>

        {/* Business Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedPlans.map((plan) => (
            <div key={plan.id} className="bg-indigo-900/30 backdrop-blur-sm rounded-lg border border-indigo-800/50 overflow-hidden hover:border-purple-500/50 transition-all duration-300 group">
              {/* Header */}
              <div className="p-6 border-b border-indigo-800/50">
                <div className={`flex items-start justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2">{plan.title}</h3>
                    <p className="text-sm text-purple-200 line-clamp-2">{plan.description}</p>
                  </div>
                  <button className="text-purple-400 hover:text-purple-300 opacity-0 group-hover:opacity-100 transition-opacity">
                    <MoreHorizontal className="w-4 h-4" />
                  </button>
                </div>
                
                <div className={`flex items-center gap-2 mt-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(plan.status)}`}>
                    {getStatusIcon(plan.status)}
                    {t(plan.status)}
                  </span>
                  <span className="text-xs text-purple-300">{plan.industry}</span>
                </div>
              </div>

              {/* Progress */}
              <div className="p-4 border-b border-indigo-800/50">
                <div className={`flex items-center justify-between mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className="text-sm text-purple-200">{t('Progress')}</span>
                  <span className="text-sm font-medium text-white">{plan.progress}%</span>
                </div>
                <div className="w-full bg-indigo-900/50 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${plan.progress}%` }}
                  />
                </div>
              </div>

              {/* Metrics */}
              <div className="p-4 border-b border-indigo-800/50">
                <div className="grid grid-cols-4 gap-2 text-center">
                  <div>
                    <div className="flex items-center justify-center mb-1">
                      <Eye className="w-3 h-3 text-blue-400" />
                    </div>
                    <div className="text-xs text-purple-200">{plan.metrics.views}</div>
                  </div>
                  <div>
                    <div className="flex items-center justify-center mb-1">
                      <Download className="w-3 h-3 text-green-400" />
                    </div>
                    <div className="text-xs text-purple-200">{plan.metrics.downloads}</div>
                  </div>
                  <div>
                    <div className="flex items-center justify-center mb-1">
                      <Share2 className="w-3 h-3 text-yellow-400" />
                    </div>
                    <div className="text-xs text-purple-200">{plan.metrics.shares}</div>
                  </div>
                  <div>
                    <div className="flex items-center justify-center mb-1">
                      <Star className="w-3 h-3 text-orange-400" />
                    </div>
                    <div className="text-xs text-purple-200">{plan.metrics.rating}</div>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="p-4">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className="flex items-center gap-2">
                    {plan.collaborators.slice(0, 3).map((collaborator, index) => (
                      <div
                        key={collaborator.id}
                        className="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center text-xs font-medium"
                        title={collaborator.name}
                        style={{ marginLeft: index > 0 ? '-8px' : '0' }}
                      >
                        {collaborator.name.charAt(0)}
                      </div>
                    ))}
                    {plan.collaborators.length > 3 && (
                      <div className="w-6 h-6 bg-indigo-600 rounded-full flex items-center justify-center text-xs font-medium">
                        +{plan.collaborators.length - 3}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => navigate(`/entrepreneur/business-plans/${plan.id}`)}
                      className="text-purple-400 hover:text-purple-300 transition-colors"
                      title={t('View Plan')}
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => navigate(`/entrepreneur/business-plans/${plan.id}/edit`)}
                      className="text-blue-400 hover:text-blue-300 transition-colors"
                      title={t('Edit Plan')}
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      className="text-green-400 hover:text-green-300 transition-colors"
                      title={t('Share Plan')}
                    >
                      <Share2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {sortedPlans.length === 0 && (
          <div className="text-center py-12">
            <FileText className="w-16 h-16 text-purple-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">{t('No business plans found')}</h3>
            <p className="text-purple-200 mb-6">{t('Create your first business plan to get started')}</p>
            <button
              onClick={() => navigate('/entrepreneur/business-plans/create')}
              className="inline-flex items-center gap-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4" />
              {t('Create Business Plan')}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default BusinessPlansPage;
