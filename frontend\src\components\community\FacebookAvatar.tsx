import React from 'react';

interface FacebookAvatarProps {
  username: string;
  userId?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showOnlineStatus?: boolean;
  onClick?: (username: string, userId?: string) => void;
  clickable?: boolean;
}

/**
 * Facebook-style user avatar component
 * Generates colorful avatars based on username
 */
export const FacebookAvatar: React.FC<FacebookAvatarProps> = ({
  username,
  userId,
  size = 'md',
  className = '',
  showOnlineStatus = false,
  onClick,
  clickable = true
}) => {
  // Generate consistent color based on username
  const getAvatarColor = (name: string) => {
    const colors = [
      'from-blue-500 to-blue-600',
      'from-green-500 to-green-600', 
      'from-purple-500 to-purple-600',
      'from-pink-500 to-pink-600',
      'from-indigo-500 to-indigo-600',
      'from-red-500 to-red-600',
      'from-yellow-500 to-yellow-600',
      'from-teal-500 to-teal-600',
    ];
    
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  };

  // Size classes
  const sizeClasses = {
    sm: 'w-6 h-6 text-xs',
    md: 'w-8 h-8 text-sm',
    lg: 'w-10 h-10 text-base'
  };

  const initial = username.charAt(0).toUpperCase();
  const colorClass = getAvatarColor(username);

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onClick && clickable) {
      onClick(username, userId);
    }
  };

  return (
    <div className={`relative ${className}`}>
      <div
        className={`
          ${sizeClasses[size]}
          bg-gradient-to-br ${colorClass}
          rounded-full
          flex items-center justify-center
          text-white font-medium
          shadow-sm
          flex-shrink-0
          ${clickable ? 'cursor-pointer hover:scale-105 transition-transform duration-200' : ''}
        `}
        onClick={handleClick}
        role={clickable ? 'button' : undefined}
        tabIndex={clickable ? 0 : undefined}
        onKeyDown={clickable ? (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            handleClick(e as any);
          }
        } : undefined}
      >
        {initial}
      </div>

      {/* Online status indicator */}
      {showOnlineStatus && (
        <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-gray-900 rounded-full"></div>
      )}
    </div>
  );
};

export default FacebookAvatar;
