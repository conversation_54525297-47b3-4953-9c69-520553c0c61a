"""
🎯 CONSOLIDATED AI VIEWS
Single source of truth for all AI API endpoints

This file consolidates and replaces:
- core/ai_views.py (main AI endpoints)
- core/ai/views/unified_ai_views.py (unified AI views)
- core/ai/views/recommendation_views.py (recommendation endpoints)

Features:
- Single consolidated service usage
- Consistent error handling and response formats
- Proper authentication and permissions
- Rate limiting and caching
- Comprehensive logging
"""

import logging
from datetime import datetime
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from rest_framework import status, viewsets, filters
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend

# Import our consolidated AI service
from core.services.consolidated_ai_service import consolidated_ai_service

logger = logging.getLogger(__name__)


class ConsolidatedChatView(APIView):
    """
    🎯 CONSOLIDATED CHAT VIEW
    Single endpoint for all AI chat functionality
    Replaces: UniversalChatView, multiple chat endpoints
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """Handle AI chat requests with automatic routing"""
        try:
            # Extract request data
            message = request.data.get('message', '')
            language = request.data.get('language')  # Auto-detect if None
            chat_type = request.data.get('chat_type', 'general')
            context = request.data.get('context', {})
            
            # Validate required fields
            if not message.strip():
                return Response({
                    'success': False,
                    'error': 'Message is required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Use consolidated AI service
            result = consolidated_ai_service.chat(
                message=message,
                language=language,
                user_id=request.user.id,
                context=context,
                chat_type=chat_type
            )
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"ConsolidatedChatView error: {e}")
            return Response({
                'success': False,
                'error': 'Internal server error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ConsolidatedBusinessAnalysisView(APIView):
    """
    🎯 CONSOLIDATED BUSINESS ANALYSIS VIEW
    Single endpoint for business analysis functionality
    Replaces: UniversalBusinessAnalysisView, multiple analysis endpoints
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """Handle business analysis requests"""
        try:
            # Extract business data
            business_data = request.data.get('business_data', {})
            language = request.data.get('language')
            
            # Handle both string and dict inputs
            if isinstance(business_data, str):
                business_text = business_data
            else:
                business_text = business_data.get('description', '')
            
            if not business_text.strip():
                return Response({
                    'success': False,
                    'error': 'Business data is required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Use consolidated AI service
            result = consolidated_ai_service.analyze_business(
                business_data=business_data,
                language=language,
                user_id=request.user.id
            )
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"ConsolidatedBusinessAnalysisView error: {e}")
            return Response({
                'success': False,
                'error': 'Internal server error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ConsolidatedRecommendationsView(APIView):
    """
    🎯 CONSOLIDATED RECOMMENDATIONS VIEW
    Single endpoint for AI recommendations functionality
    Replaces: AIRecommendationViewSet, recommendation endpoints
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """Generate business recommendations"""
        try:
            # Extract request data
            business_idea_data = request.data.get('business_idea_data', {})
            recommendation_types = request.data.get('recommendation_types', [])
            
            if not business_idea_data:
                return Response({
                    'success': False,
                    'error': 'Business idea data is required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Use consolidated AI service
            result = consolidated_ai_service.generate_business_recommendations(
                business_idea_data=business_idea_data,
                user_id=request.user.id,
                recommendation_types=recommendation_types
            )
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"ConsolidatedRecommendationsView error: {e}")
            return Response({
                'success': False,
                'error': 'Internal server error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ConsolidatedMentorshipView(APIView):
    """
    🎯 CONSOLIDATED MENTORSHIP VIEW
    Single endpoint for mentorship matching functionality
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """Find optimal mentors"""
        try:
            # Extract startup profile
            startup_profile = request.data.get('startup_profile', {})
            
            if not startup_profile:
                return Response({
                    'success': False,
                    'error': 'Startup profile is required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Use consolidated AI service
            result = consolidated_ai_service.find_optimal_mentors(
                startup_profile=startup_profile,
                user_id=request.user.id
            )
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"ConsolidatedMentorshipView error: {e}")
            return Response({
                'success': False,
                'error': 'Internal server error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ConsolidatedIdeaBuilderView(APIView):
    """
    🎯 CONSOLIDATED IDEA BUILDER VIEW
    Single endpoint for idea enhancement functionality
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """Enhance business ideas"""
        try:
            # Extract idea data
            idea_data = request.data.get('idea_data', {})
            
            if not idea_data:
                return Response({
                    'success': False,
                    'error': 'Idea data is required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Use consolidated AI service
            result = consolidated_ai_service.enhance_business_idea(
                idea_data=idea_data,
                user_id=request.user.id
            )
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"ConsolidatedIdeaBuilderView error: {e}")
            return Response({
                'success': False,
                'error': 'Internal server error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ConsolidatedAIStatusView(APIView):
    """
    🎯 CONSOLIDATED AI STATUS VIEW
    Single endpoint for AI service status
    Replaces: UniversalAIStatusView, multiple status endpoints
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get AI service status"""
        try:
            # Use consolidated AI service
            result = consolidated_ai_service.get_status()
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"ConsolidatedAIStatusView error: {e}")
            return Response({
                'success': False,
                'error': 'Internal server error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ConsolidatedTextAnalysisView(APIView):
    """
    🎯 CONSOLIDATED TEXT ANALYSIS VIEW
    Single endpoint for text analysis functionality
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """Handle text analysis requests"""
        try:
            # Extract request data
            text = request.data.get('text', '')
            analysis_type = request.data.get('analysis_type', 'general')
            language = request.data.get('language')
            
            if not text.strip():
                return Response({
                    'success': False,
                    'error': 'Text is required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Use consolidated AI service
            result = consolidated_ai_service.analyze_text(
                text=text,
                analysis_type=analysis_type,
                language=language,
                user_id=request.user.id
            )
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"ConsolidatedTextAnalysisView error: {e}")
            return Response({
                'success': False,
                'error': 'Internal server error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ConsolidatedContentGenerationView(APIView):
    """
    🎯 CONSOLIDATED CONTENT GENERATION VIEW
    Single endpoint for content generation functionality
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """Handle content generation requests"""
        try:
            # Extract request data
            content_type = request.data.get('content_type', '')
            context = request.data.get('context', {})
            language = request.data.get('language', 'en')
            
            if not content_type:
                return Response({
                    'success': False,
                    'error': 'Content type is required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Use consolidated AI service
            result = consolidated_ai_service.generate_content(
                content_type=content_type,
                context=context,
                language=language,
                user_id=request.user.id
            )
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"ConsolidatedContentGenerationView error: {e}")
            return Response({
                'success': False,
                'error': 'Internal server error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ========================================
# FUNCTION-BASED VIEWS FOR BACKWARD COMPATIBILITY
# ========================================

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def consolidated_ai_test(request):
    """Test endpoint for AI service availability"""
    try:
        status_result = consolidated_ai_service.get_status()
        return Response({
            'test': 'success',
            'available': status_result.get('available', False),
            'service': 'consolidated_ai',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return Response({
            'test': 'failed',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
