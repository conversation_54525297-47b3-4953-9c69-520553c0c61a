"""
🎯 CONSOLIDATED AI SERVICE
Single source of truth for all AI operations in the Yasmeen AI platform

This service consolidates and replaces:
- core/ai_service.py (main AI operations)
- core/ai/services/unified_ai_service.py (unified service wrapper)
- core/ai/services/recommendation_service.py (business recommendations)
- core/ai/services/mentorship_service.py (mentorship matching)
- core/ai/services/idea_builder_service.py (idea enhancement)

Architecture:
- Single service class with clear method separation
- Consistent error handling and response formats
- Centralized configuration management
- Proper caching and rate limiting
- Comprehensive logging and monitoring
"""

import logging
import asyncio
import json
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone

# Import AI configuration and utilities (safe imports)
from core.ai_config import get_ai_config, CentralGeminiConfig
from core.ai_utils import (
    load_ai_prompts, load_ai_models, get_regional_dialect_config,
    detect_user_region, format_ai_response
)

# Import Google Generative AI for direct implementation
try:
    import google.generativeai as genai
    AI_AVAILABLE = True
except ImportError:
    genai = None
    AI_AVAILABLE = False

logger = logging.getLogger(__name__)


class ConsolidatedAIService:
    """
    🎯 CONSOLIDATED AI SERVICE
    Single source of truth for all AI operations in the application
    
    Features:
    - Unified chat interface with language detection
    - Business analysis and recommendations
    - Text analysis and content generation
    - Mentorship matching and idea building
    - Predictive analytics and insights
    - Centralized configuration and caching
    - Comprehensive error handling and logging
    """
    
    _instance = None
    
    def __new__(cls):
        """Singleton pattern to ensure single instance"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """Initialize the consolidated AI service"""
        if hasattr(self, '_initialized'):
            return
            
        self.config = get_ai_config()
        self.cache_timeout = 300  # 5 minutes
        self.rate_limit_window = 60  # 1 minute
        self.max_requests_per_window = 100
        self._initialized = True
        
        logger.info("🎯 ConsolidatedAIService initialized")

    # ========================================
    # CORE AI IMPLEMENTATION METHODS
    # ========================================

    def _get_gemini_model(self):
        """Get configured Gemini model instance"""
        if not AI_AVAILABLE:
            raise Exception("Google Generative AI not available")

        if not self.config or not self.config.api_key:
            raise Exception("Gemini API key not configured")

        genai.configure(api_key=self.config.api_key)
        return genai.GenerativeModel(self.config.model_name)

    def detect_language(self, text: str) -> str:
        """Detect language of input text"""
        try:
            # Simple language detection based on character patterns
            arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
            total_chars = len([char for char in text if char.isalpha()])

            if total_chars > 0 and arabic_chars / total_chars > 0.3:
                return 'ar'
            return 'en'
        except Exception as e:
            logger.warning(f"Language detection failed: {e}")
            return 'en'

    def is_available(self) -> bool:
        """Check if AI service is available"""
        try:
            return (AI_AVAILABLE and
                   self.config and
                   self.config.api_key and
                   self.config.is_enabled)
        except Exception:
            return False

    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive AI service status"""
        try:
            available = self.is_available()

            return {
                'available': available,
                'service': 'consolidated_ai',
                'ai_library_available': AI_AVAILABLE,
                'config_loaded': bool(self.config),
                'api_key_configured': bool(self.config and self.config.api_key),
                'model_name': self.config.model_name if self.config else None,
                'is_enabled': self.config.is_enabled if self.config else False,
                'timestamp': datetime.now().isoformat(),
                'features': {
                    'chat': True,
                    'business_analysis': True,
                    'text_analysis': True,
                    'content_generation': True,
                    'recommendations': True,
                    'mentorship': True,
                    'idea_builder': True
                }
            }
        except Exception as e:
            logger.error(f"Status check failed: {e}")
            return {
                'available': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def _ai_chat_direct(self, message: str, language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
        """Direct AI chat implementation without external dependencies"""
        try:
            model = self._get_gemini_model()

            # Load prompts configuration
            prompts_config = load_ai_prompts()

            # Get appropriate prompt based on language
            system_prompt = prompts_config.get('system_prompts', {}).get(language,
                prompts_config.get('system_prompts', {}).get('en', ''))

            # Prepare the conversation
            full_prompt = f"{system_prompt}\n\nUser: {message}\nAssistant:"

            # Generate response
            response = model.generate_content(full_prompt)

            return {
                'success': True,
                'response': response.text,
                'language': language,
                'user_id': user_id,
                'timestamp': datetime.now().isoformat(),
                'service': 'consolidated_ai'
            }

        except Exception as e:
            logger.error(f"Direct AI chat error: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def _ai_analyze_business_direct(self, business_data: Union[str, Dict], language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
        """Direct business analysis implementation"""
        try:
            model = self._get_gemini_model()

            # Load prompts configuration
            prompts_config = load_ai_prompts()

            # Get business analysis prompt
            business_prompt = prompts_config.get('business_analysis', {}).get(language,
                prompts_config.get('business_analysis', {}).get('en', ''))

            # Prepare business data for analysis
            if isinstance(business_data, str):
                business_text = business_data
            else:
                business_text = json.dumps(business_data, ensure_ascii=False)

            # Create analysis prompt
            full_prompt = f"{business_prompt}\n\nBusiness Data: {business_text}\n\nAnalysis:"

            # Generate analysis
            response = model.generate_content(full_prompt)

            return {
                'analysis': response.text,
                'business_data': business_data,
                'language': language,
                'timestamp': datetime.now().isoformat(),
                'service': 'consolidated_ai'
            }

        except Exception as e:
            logger.error(f"Direct business analysis error: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def _ai_analyze_text_direct(self, text: str, analysis_type: str = 'general',
                               language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
        """Direct text analysis implementation"""
        try:
            model = self._get_gemini_model()

            # Load prompts configuration
            prompts_config = load_ai_prompts()

            # Get text analysis prompt based on type
            analysis_prompts = prompts_config.get('text_analysis', {})
            prompt = analysis_prompts.get(analysis_type, {}).get(language,
                analysis_prompts.get('general', {}).get('en', ''))

            # Create analysis prompt
            full_prompt = f"{prompt}\n\nText to analyze: {text}\n\nAnalysis:"

            # Generate analysis
            response = model.generate_content(full_prompt)

            return {
                'analysis': response.text,
                'text': text,
                'analysis_type': analysis_type,
                'language': language,
                'timestamp': datetime.now().isoformat(),
                'service': 'consolidated_ai'
            }

        except Exception as e:
            logger.error(f"Direct text analysis error: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def _ai_generate_content_direct(self, content_type: str, context: Dict[str, Any],
                                   language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
        """Direct content generation implementation"""
        try:
            model = self._get_gemini_model()

            # Load prompts configuration
            prompts_config = load_ai_prompts()

            # Get content generation prompt
            content_prompts = prompts_config.get('content_generation', {})
            prompt = content_prompts.get(content_type, {}).get(language,
                content_prompts.get('general', {}).get('en', ''))

            # Prepare context for generation
            context_text = json.dumps(context, ensure_ascii=False)

            # Create generation prompt
            full_prompt = f"{prompt}\n\nContext: {context_text}\n\nGenerated Content:"

            # Generate content
            response = model.generate_content(full_prompt)

            return {
                'content': response.text,
                'content_type': content_type,
                'context': context,
                'language': language,
                'timestamp': datetime.now().isoformat(),
                'service': 'consolidated_ai'
            }

        except Exception as e:
            logger.error(f"Direct content generation error: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    # ========================================
    # CORE CHAT FUNCTIONALITY
    # ========================================
    
    def chat(self, message: str, language: str = None, user_id: Optional[int] = None, 
             context: Optional[Dict] = None, chat_type: str = 'general') -> Dict[str, Any]:
        """
        Unified chat interface with automatic language detection
        
        Args:
            message: User message
            language: Language code (auto-detected if None)
            user_id: User ID for personalization
            context: Additional context for the conversation
            chat_type: Type of chat (general, business, recommendation, etc.)
            
        Returns:
            Dict with response, language, metadata
        """
        try:
            # Auto-detect language if not provided
            if not language:
                language = self.detect_language(message)

            # Check rate limiting
            if not self._check_rate_limit(user_id, 'chat'):
                return self._rate_limit_response()

            # Route to appropriate chat handler based on type
            if chat_type == 'recommendation':
                return self._handle_recommendation_chat(message, language, user_id, context)
            elif chat_type == 'mentorship':
                return self._handle_mentorship_chat(message, language, user_id, context)
            elif chat_type == 'idea_builder':
                return self._handle_idea_builder_chat(message, language, user_id, context)
            else:
                # General chat using direct implementation
                response = self._ai_chat_direct(message, language, user_id)
                
                return {
                    'success': True,
                    'response': response.get('response', ''),
                    'language': language,
                    'chat_type': chat_type,
                    'user_id': user_id,
                    'context': context,
                    'timestamp': datetime.now().isoformat(),
                    'service': 'consolidated_ai',
                    'detected_language': language if not language else None
                }
                
        except Exception as e:
            logger.error(f"ConsolidatedAI chat error: {e}")
            return self._error_response(f"Chat error: {str(e)}")
    
    def analyze_business(self, business_data: Union[str, Dict[str, Any]], 
                        language: str = None, user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Comprehensive business analysis
        
        Args:
            business_data: Business idea text or structured data
            language: Language for analysis (auto-detected if None)
            user_id: User ID for personalization
            
        Returns:
            Dict with analysis results and recommendations
        """
        try:
            # Handle both string and dict inputs
            if isinstance(business_data, str):
                business_text = business_data
                if not language:
                    language = self.detect_language(business_text)
            else:
                business_text = business_data.get('description', '')
                language = language or business_data.get('language', 'en')

            # Check rate limiting
            if not self._check_rate_limit(user_id, 'analysis'):
                return self._rate_limit_response()

            # Direct business analysis implementation
            analysis_result = self._ai_analyze_business_direct(business_data, language, user_id)
            
            return {
                'success': True,
                'analysis': analysis_result,
                'business_data': business_data,
                'language': language,
                'user_id': user_id,
                'timestamp': datetime.now().isoformat(),
                'service': 'consolidated_ai'
            }
            
        except Exception as e:
            logger.error(f"ConsolidatedAI business analysis error: {e}")
            return self._error_response(f"Business analysis error: {str(e)}")
    
    def analyze_text(self, text: str, analysis_type: str = 'general',
                    language: str = None, user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Text analysis with various analysis types
        
        Args:
            text: Text to analyze
            analysis_type: Type of analysis (sentiment, keywords, summary, etc.)
            language: Language code (auto-detected if None)
            user_id: User ID for personalization
            
        Returns:
            Dict with analysis results
        """
        try:
            if not language:
                language = self.detect_language(text)

            # Check rate limiting
            if not self._check_rate_limit(user_id, 'analysis'):
                return self._rate_limit_response()

            # Direct text analysis implementation
            analysis_result = self._ai_analyze_text_direct(text, analysis_type, language, user_id)
            
            return {
                'success': True,
                'analysis': analysis_result,
                'text': text,
                'analysis_type': analysis_type,
                'language': language,
                'user_id': user_id,
                'timestamp': datetime.now().isoformat(),
                'service': 'consolidated_ai'
            }
            
        except Exception as e:
            logger.error(f"ConsolidatedAI text analysis error: {e}")
            return self._error_response(f"Text analysis error: {str(e)}")
    
    def generate_content(self, content_type: str, context: Dict[str, Any],
                        language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Intelligent content generation
        
        Args:
            content_type: Type of content to generate
            context: Context and parameters for generation
            language: Language for generated content
            user_id: User ID for personalization
            
        Returns:
            Dict with generated content
        """
        try:
            # Check rate limiting
            if not self._check_rate_limit(user_id, 'generation'):
                return self._rate_limit_response()

            # Direct content generation implementation
            content_result = self._ai_generate_content_direct(content_type, context, language, user_id)
            
            return {
                'success': True,
                'content': content_result,
                'content_type': content_type,
                'context': context,
                'language': language,
                'user_id': user_id,
                'timestamp': datetime.now().isoformat(),
                'service': 'consolidated_ai'
            }
            
        except Exception as e:
            logger.error(f"ConsolidatedAI content generation error: {e}")
            return self._error_response(f"Content generation error: {str(e)}")
    
    # ========================================
    # BUSINESS RECOMMENDATIONS
    # ========================================
    
    def generate_business_recommendations(self, business_idea_data: Dict[str, Any], 
                                        user_id: Optional[int] = None,
                                        recommendation_types: List[str] = None) -> Dict[str, Any]:
        """
        Generate AI-powered business recommendations
        Consolidates functionality from recommendation_service.py
        """
        try:
            # Default recommendation types
            if not recommendation_types:
                recommendation_types = [
                    'market_analysis', 'competitive_analysis', 'business_model',
                    'marketing_strategy', 'financial_planning', 'risk_assessment',
                    'growth_strategy'
                ]
            
            # Check rate limiting
            if not self._check_rate_limit(user_id, 'recommendations'):
                return self._rate_limit_response()
            
            # Build recommendation prompt
            prompt = self._build_recommendation_prompt(business_idea_data, recommendation_types)
            
            # Generate recommendations using AI chat
            ai_response = ai_chat(prompt, language='en', user_id=user_id)
            
            if not ai_response.get('response'):
                return self._error_response('Failed to generate recommendations')
            
            # Parse AI response into structured recommendations
            recommendations = self._parse_ai_recommendations(
                ai_response['response'], business_idea_data
            )
            
            return {
                'success': True,
                'recommendations': recommendations,
                'business_idea_data': business_idea_data,
                'recommendation_types': recommendation_types,
                'user_id': user_id,
                'timestamp': datetime.now().isoformat(),
                'service': 'consolidated_ai'
            }
            
        except Exception as e:
            logger.error(f"ConsolidatedAI recommendations error: {e}")
            return self._error_response(f"Recommendations error: {str(e)}")

    # ========================================
    # MENTORSHIP AND IDEA BUILDING
    # ========================================

    def find_optimal_mentors(self, startup_profile: Dict[str, Any],
                           user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Find optimal mentors based on startup profile
        Consolidates functionality from mentorship_service.py
        """
        try:
            # Check rate limiting
            if not self._check_rate_limit(user_id, 'mentorship'):
                return self._rate_limit_response()

            # Build mentorship matching prompt
            prompt = f"""
            Based on this startup profile, recommend optimal mentors and mentorship strategies:

            Industry: {startup_profile.get('industry', 'Not specified')}
            Stage: {startup_profile.get('stage', 'Not specified')}
            Team Size: {startup_profile.get('team_size', 'Not specified')}
            Funding Status: {startup_profile.get('funding_status', 'Not specified')}
            Key Challenges: {startup_profile.get('challenges', 'Not specified')}
            Goals: {startup_profile.get('goals', 'Not specified')}

            Please provide:
            1. Ideal mentor profiles and expertise areas
            2. Mentorship strategies and approaches
            3. Key questions to ask potential mentors
            4. Expected outcomes and milestones
            """

            # Generate mentorship recommendations
            ai_response = ai_chat(prompt, language='en', user_id=user_id)

            return {
                'success': True,
                'mentorship_recommendations': ai_response.get('response', ''),
                'startup_profile': startup_profile,
                'user_id': user_id,
                'timestamp': datetime.now().isoformat(),
                'service': 'consolidated_ai'
            }

        except Exception as e:
            logger.error(f"ConsolidatedAI mentorship error: {e}")
            return self._error_response(f"Mentorship error: {str(e)}")

    def enhance_business_idea(self, idea_data: Dict[str, Any],
                            user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Enhance and develop business ideas
        Consolidates functionality from idea_builder_service.py
        """
        try:
            # Check rate limiting
            if not self._check_rate_limit(user_id, 'idea_building'):
                return self._rate_limit_response()

            # Build idea enhancement prompt
            prompt = f"""
            Help enhance and develop this business idea:

            Title: {idea_data.get('title', 'Not specified')}
            Description: {idea_data.get('description', 'Not specified')}
            Target Market: {idea_data.get('target_market', 'Not specified')}
            Problem Solving: {idea_data.get('problem', 'Not specified')}
            Current Stage: {idea_data.get('stage', 'Idea')}

            Please provide:
            1. Idea refinement and improvement suggestions
            2. Market opportunity analysis
            3. Potential business models
            4. Implementation roadmap
            5. Risk assessment and mitigation strategies
            6. Success metrics and KPIs
            """

            # Generate idea enhancement recommendations
            ai_response = ai_chat(prompt, language='en', user_id=user_id)

            return {
                'success': True,
                'enhanced_idea': ai_response.get('response', ''),
                'original_idea': idea_data,
                'user_id': user_id,
                'timestamp': datetime.now().isoformat(),
                'service': 'consolidated_ai'
            }

        except Exception as e:
            logger.error(f"ConsolidatedAI idea building error: {e}")
            return self._error_response(f"Idea building error: {str(e)}")

    # ========================================
    # SERVICE STATUS AND HEALTH
    # ========================================

    def get_status(self) -> Dict[str, Any]:
        """
        Get comprehensive AI service status
        Consolidates status checking from all services
        """
        try:
            # Get core AI service status using internal methods
            core_status = self.get_status()
            is_available = self.is_available()

            return {
                'success': True,
                'available': is_available,
                'service': 'consolidated_ai',
                'core_status': core_status,
                'features': {
                    'chat': True,
                    'business_analysis': True,
                    'text_analysis': True,
                    'content_generation': True,
                    'recommendations': True,
                    'mentorship': True,
                    'idea_builder': True
                },
                'configuration': {
                    'api_key_configured': bool(self.config.api_key),
                    'model_name': self.config.model_name,
                    'max_tokens': self.config.max_tokens,
                    'temperature': self.config.temperature
                },
                'rate_limiting': {
                    'enabled': True,
                    'window_seconds': self.rate_limit_window,
                    'max_requests': self.max_requests_per_window
                },
                'caching': {
                    'enabled': True,
                    'timeout_seconds': self.cache_timeout
                },
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"ConsolidatedAI status error: {e}")
            return {
                'success': False,
                'available': False,
                'error': str(e),
                'service': 'consolidated_ai',
                'timestamp': datetime.now().isoformat()
            }

    def is_available(self) -> bool:
        """Check if AI service is available"""
        try:
            return (AI_AVAILABLE and
                   self.config and
                   self.config.api_key and
                   self.config.is_enabled)
        except Exception:
            return False

    # ========================================
    # PRIVATE HELPER METHODS
    # ========================================

    def _check_rate_limit(self, user_id: Optional[int], operation: str) -> bool:
        """Check rate limiting for user operations"""
        if not user_id:
            return True  # No rate limiting for anonymous users in this implementation

        cache_key = f"ai_rate_limit:{user_id}:{operation}"
        current_time = timezone.now()
        window_start = current_time - timedelta(seconds=self.rate_limit_window)

        # Get current request count
        request_count = cache.get(cache_key, 0)

        if request_count >= self.max_requests_per_window:
            return False

        # Increment request count
        cache.set(cache_key, request_count + 1, self.rate_limit_window)
        return True

    def _rate_limit_response(self) -> Dict[str, Any]:
        """Return rate limit exceeded response"""
        return {
            'success': False,
            'error': 'Rate limit exceeded',
            'message': f'Maximum {self.max_requests_per_window} requests per {self.rate_limit_window} seconds',
            'retry_after': self.rate_limit_window,
            'timestamp': datetime.now().isoformat(),
            'service': 'consolidated_ai'
        }

    def _error_response(self, error_message: str) -> Dict[str, Any]:
        """Return standardized error response"""
        return {
            'success': False,
            'error': error_message,
            'timestamp': datetime.now().isoformat(),
            'service': 'consolidated_ai'
        }

    def _handle_recommendation_chat(self, message: str, language: str,
                                  user_id: Optional[int], context: Optional[Dict]) -> Dict[str, Any]:
        """Handle recommendation-focused chat"""
        try:
            # Extract business context if available
            business_data = context or {}

            # Generate business recommendations
            recommendations = self.generate_business_recommendations(
                business_idea_data=business_data,
                user_id=user_id
            )

            return {
                'success': True,
                'response': recommendations.get('recommendations', []),
                'chat_type': 'recommendation',
                'language': language,
                'user_id': user_id,
                'timestamp': datetime.now().isoformat(),
                'service': 'consolidated_ai'
            }

        except Exception as e:
            logger.error(f"Recommendation chat error: {e}")
            return self._error_response(f"Recommendation chat error: {str(e)}")

    def _handle_mentorship_chat(self, message: str, language: str,
                              user_id: Optional[int], context: Optional[Dict]) -> Dict[str, Any]:
        """Handle mentorship-focused chat"""
        try:
            # Extract startup profile from context
            startup_profile = context or {}

            # Find optimal mentors
            mentorship_result = self.find_optimal_mentors(
                startup_profile=startup_profile,
                user_id=user_id
            )

            return {
                'success': True,
                'response': mentorship_result.get('mentorship_recommendations', ''),
                'chat_type': 'mentorship',
                'language': language,
                'user_id': user_id,
                'timestamp': datetime.now().isoformat(),
                'service': 'consolidated_ai'
            }

        except Exception as e:
            logger.error(f"Mentorship chat error: {e}")
            return self._error_response(f"Mentorship chat error: {str(e)}")

    def _handle_idea_builder_chat(self, message: str, language: str,
                                user_id: Optional[int], context: Optional[Dict]) -> Dict[str, Any]:
        """Handle idea building chat"""
        try:
            # Extract idea data from context
            idea_data = context or {}

            # Enhance business idea
            enhancement_result = self.enhance_business_idea(
                idea_data=idea_data,
                user_id=user_id
            )

            return {
                'success': True,
                'response': enhancement_result.get('enhanced_idea', ''),
                'chat_type': 'idea_builder',
                'language': language,
                'user_id': user_id,
                'timestamp': datetime.now().isoformat(),
                'service': 'consolidated_ai'
            }

        except Exception as e:
            logger.error(f"Idea builder chat error: {e}")
            return self._error_response(f"Idea builder chat error: {str(e)}")

    def _build_recommendation_prompt(self, business_data: Dict[str, Any],
                                   recommendation_types: List[str]) -> str:
        """Build AI prompt for business recommendations"""
        prompt = f"""
        As an expert business consultant, analyze this business idea and provide specific, actionable recommendations:

        Business Idea:
        Title: {business_data.get('title', 'Not specified')}
        Description: {business_data.get('description', 'Not specified')}
        Industry: {business_data.get('industry', 'Not specified')}
        Target Market: {business_data.get('target_market', 'Not specified')}
        Business Model: {business_data.get('business_model', 'Not specified')}
        Stage: {business_data.get('stage', 'Idea')}

        Please provide recommendations for: {', '.join(recommendation_types)}

        Format your response as numbered recommendations with:
        1. Clear title for each recommendation
        2. Detailed description and rationale
        3. Specific action items
        4. Expected outcomes

        Focus on practical, implementable advice that can drive business success.
        """
        return prompt

    def _parse_ai_recommendations(self, ai_response: str,
                                business_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Parse AI response into structured recommendations"""
        try:
            recommendations = []

            # Split response into sections (simple parsing)
            sections = ai_response.split('\n\n')

            for i, section in enumerate(sections[:7]):  # Limit to 7 recommendations
                if section.strip() and len(section.strip()) > 20:
                    recommendation = {
                        'id': i + 1,
                        'title': f"Recommendation {i + 1}",
                        'description': section.strip(),
                        'recommendation_type': self._determine_recommendation_type(section),
                        'priority': self._determine_priority(i),
                        'relevance_score': 85.0 + (i * 2),  # Mock scoring
                        'action_items': self._extract_action_items(section),
                        'expected_outcome': self._extract_expected_outcome(section),
                        'reasoning': f"AI analysis of business idea: {business_data.get('title', 'Unknown')}"
                    }
                    recommendations.append(recommendation)

            return recommendations

        except Exception as e:
            logger.error(f"Error parsing AI recommendations: {e}")
            return []

    def _determine_recommendation_type(self, text: str) -> str:
        """Determine recommendation type from text content"""
        text_lower = text.lower()

        if any(word in text_lower for word in ['market', 'customer', 'audience']):
            return 'market_analysis'
        elif any(word in text_lower for word in ['competitor', 'competition', 'competitive']):
            return 'competitive_analysis'
        elif any(word in text_lower for word in ['revenue', 'pricing', 'business model']):
            return 'business_model'
        elif any(word in text_lower for word in ['marketing', 'promotion', 'advertising']):
            return 'marketing_strategy'
        elif any(word in text_lower for word in ['financial', 'funding', 'investment']):
            return 'financial_planning'
        elif any(word in text_lower for word in ['risk', 'challenge', 'threat']):
            return 'risk_assessment'
        else:
            return 'general'

    def _determine_priority(self, index: int) -> str:
        """Determine priority based on recommendation order"""
        if index < 2:
            return 'high'
        elif index < 5:
            return 'medium'
        else:
            return 'low'

    def _extract_action_items(self, text: str) -> List[str]:
        """Extract action items from recommendation text"""
        # Simple extraction - look for numbered lists or bullet points
        action_items = []
        lines = text.split('\n')

        for line in lines:
            line = line.strip()
            if (line.startswith(('1.', '2.', '3.', '4.', '5.')) or
                line.startswith(('•', '-', '*')) or
                'action' in line.lower() or 'step' in line.lower()):
                action_items.append(line)

        return action_items[:5]  # Limit to 5 action items

    def _extract_expected_outcome(self, text: str) -> str:
        """Extract expected outcome from recommendation text"""
        # Look for outcome-related keywords
        lines = text.split('.')
        for line in lines:
            if any(word in line.lower() for word in ['outcome', 'result', 'benefit', 'impact', 'achieve']):
                return line.strip()

        # Fallback to last sentence
        return lines[-1].strip() if lines else "Improved business performance"


# ========================================
# GLOBAL INSTANCE AND CONVENIENCE FUNCTIONS
# ========================================

# Create global instance for use across the application
consolidated_ai_service = ConsolidatedAIService()


# Convenience functions for backward compatibility
def ai_chat_consolidated(message: str, language: str = None, user_id: Optional[int] = None,
                        context: Optional[Dict] = None, chat_type: str = 'general') -> Dict[str, Any]:
    """Convenience function for AI chat"""
    return consolidated_ai_service.chat(message, language, user_id, context, chat_type)


def ai_analyze_business_consolidated(business_data: Union[str, Dict[str, Any]],
                                   language: str = None, user_id: Optional[int] = None) -> Dict[str, Any]:
    """Convenience function for business analysis"""
    return consolidated_ai_service.analyze_business(business_data, language, user_id)


def ai_get_status_consolidated() -> Dict[str, Any]:
    """Convenience function for service status"""
    return consolidated_ai_service.get_status()


def ai_is_available_consolidated() -> bool:
    """Convenience function for availability check"""
    return consolidated_ai_service.is_available()
