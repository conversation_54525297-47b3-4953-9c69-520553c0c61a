"""
OPTIMIZED COMMUNITY VIEWS
High-performance views that fix N+1 query problems and improve response times
"""

from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Count, Prefetch, Q
from django.core.cache import cache
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from .models import CommunityPost, CommunityComment
from .serializers import CommunityPostSerializer, CommunityCommentSerializer


class OptimizedCommunityPostViewSet(viewsets.ModelViewSet):
    """
    Optimized Community Posts ViewSet
    Fixes N+1 queries and implements caching for better performance
    """
    serializer_class = CommunityPostSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    
    def get_queryset(self):
        """
        Optimized queryset that eliminates N+1 queries
        """
        # Use select_related for ForeignKey relationships
        # Use prefetch_related for ManyToMany and reverse ForeignKey relationships
        queryset = CommunityPost.objects.select_related(
            'author',  # Load author in same query
            'author__userprofile',  # Load author profile if needed
        ).prefetch_related(
            # Efficiently load comments with their authors
            Prefetch(
                'comments',
                queryset=CommunityComment.objects.select_related('author').order_by('created_at')
            ),
            # Efficiently load likes
            'likes',
            # Efficiently load tags
            'tags',
            # Efficiently load saves
            'saves'
        ).annotate(
            # Pre-calculate counts to avoid additional queries
            like_count=Count('likes', distinct=True),
            comment_count=Count('comments', distinct=True),
            save_count=Count('saves', distinct=True)
        ).order_by('-created_at')
        
        # Apply filters
        visibility = self.request.query_params.get('visibility', None)
        if visibility:
            queryset = queryset.filter(visibility=visibility)
        
        author = self.request.query_params.get('author', None)
        if author:
            queryset = queryset.filter(author__username=author)
        
        return queryset
    
    def list(self, request, *args, **kwargs):
        """
        Cached list view for better performance
        """
        # Create cache key based on query parameters
        cache_key = f"community_posts_{request.GET.urlencode()}"
        
        # Try to get from cache first (cache for 5 minutes)
        cached_data = cache.get(cache_key)
        if cached_data:
            return Response(cached_data)
        
        # If not in cache, get from database
        queryset = self.filter_queryset(self.get_queryset())
        
        # Limit to reasonable number for performance
        page_size = min(int(request.query_params.get('page_size', 20)), 50)
        queryset = queryset[:page_size]
        
        serializer = self.get_serializer(queryset, many=True)
        
        # Cache the result
        cache.set(cache_key, serializer.data, 300)  # 5 minutes
        
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """
        Optimized stats endpoint that avoids N+1 queries
        """
        cache_key = "community_stats"
        cached_stats = cache.get(cache_key)
        
        if cached_stats:
            return Response(cached_stats)
        
        # Use efficient aggregation instead of loops
        stats = CommunityPost.objects.aggregate(
            total_posts=Count('id'),
            total_likes=Count('likes'),
            total_comments=Count('comments'),
            total_saves=Count('saves')
        )
        
        # Add user stats efficiently
        from django.contrib.auth.models import User
        user_stats = User.objects.filter(is_active=True).aggregate(
            total_active_users=Count('id')
        )
        
        stats.update(user_stats)
        
        # Cache for 10 minutes
        cache.set(cache_key, stats, 600)
        
        return Response(stats)
    
    @action(detail=True, methods=['post'])
    def like(self, request, pk=None):
        """
        Optimized like/unlike functionality
        """
        post = self.get_object()
        user = request.user
        
        # Use get_or_create to avoid race conditions
        if user in post.likes.all():
            post.likes.remove(user)
            liked = False
        else:
            post.likes.add(user)
            liked = True
        
        # Invalidate related caches
        cache.delete_many([
            f"community_posts_",
            "community_stats",
            f"post_{pk}_details"
        ])
        
        return Response({
            'liked': liked,
            'like_count': post.likes.count()
        })


class OptimizedCommunityCommentViewSet(viewsets.ModelViewSet):
    """
    Optimized Community Comments ViewSet
    """
    serializer_class = CommunityCommentSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    
    def get_queryset(self):
        """
        Optimized queryset for comments
        """
        return CommunityComment.objects.select_related(
            'author',
            'author__userprofile',
            'post'
        ).prefetch_related(
            'likes',
            'replies'
        ).annotate(
            like_count=Count('likes', distinct=True),
            reply_count=Count('replies', distinct=True)
        )
    
    def list(self, request, *args, **kwargs):
        """
        List comments with caching
        """
        post_id = request.query_params.get('post', None)
        if not post_id:
            return Response({'error': 'post parameter is required'}, status=400)
        
        cache_key = f"comments_post_{post_id}"
        cached_comments = cache.get(cache_key)
        
        if cached_comments:
            return Response(cached_comments)
        
        queryset = self.get_queryset().filter(post_id=post_id)
        serializer = self.get_serializer(queryset, many=True)
        
        # Cache for 2 minutes (comments change more frequently)
        cache.set(cache_key, serializer.data, 120)
        
        return Response(serializer.data)
    
    def create(self, request, *args, **kwargs):
        """
        Create comment and invalidate caches
        """
        response = super().create(request, *args, **kwargs)
        
        # Invalidate related caches
        post_id = request.data.get('post')
        if post_id:
            cache.delete_many([
                f"comments_post_{post_id}",
                f"community_posts_",
                "community_stats"
            ])
        
        return response


# Utility functions for cache management
def invalidate_community_caches():
    """
    Utility function to invalidate all community-related caches
    """
    cache.delete_many([
        "community_stats",
        "community_posts_",
    ])
    
    # Delete pattern-based cache keys (if using Redis)
    try:
        from django.core.cache import cache
        if hasattr(cache, 'delete_pattern'):
            cache.delete_pattern("community_posts_*")
            cache.delete_pattern("comments_post_*")
            cache.delete_pattern("post_*_details")
    except:
        pass


def warm_community_caches():
    """
    Pre-warm frequently accessed caches
    """
    from django.test import RequestFactory
    
    factory = RequestFactory()
    
    # Warm up posts cache
    request = factory.get('/api/community/posts/')
    viewset = OptimizedCommunityPostViewSet()
    viewset.request = request
    viewset.list(request)
    
    # Warm up stats cache
    viewset.stats(request)
    
    print("[CACHE] Community caches warmed up")


# Performance monitoring decorator
def monitor_performance(func):
    """
    Decorator to monitor view performance
    """
    import time
    import logging
    
    logger = logging.getLogger('performance')
    
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        duration = (end_time - start_time) * 1000  # Convert to milliseconds
        
        if duration > 1000:  # Log slow queries (>1 second)
            logger.warning(f"Slow view: {func.__name__} took {duration:.2f}ms")
        
        return result
    
    return wrapper
